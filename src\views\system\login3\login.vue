<template>
  <div class="login-container">
    <!-- 左侧背景区域 -->
    <div class="login-background">
      <div class="background-overlay">
        <div class="brand-container">
          <div class="logo-container">
            <img src="/public/20250513134845.jpg" alt="Logo" class="logo"/>
          </div>
          <h1 class="brand-name">TEST ALL</h1>
          <p class="brand-slogan">接口测试平台</p>
        </div>
        <div class="feature-list">
          <div class="feature-item">
            <check-circle-outlined />
            <span>高效的接口自动化测试</span>
          </div>
          <div class="feature-item">
            <check-circle-outlined />
            <span>智能化测试报告分析</span>
          </div>
          <div class="feature-item">
            <check-circle-outlined />
            <span>多环境配置管理</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧登录表单区域 -->
    <div class="login-form-container">
      <div class="login-form-wrapper">
        <h2 class="welcome-text">欢迎回来</h2>
        <p class="login-subtitle">请登录您的账号</p>

        <a-form ref="formRef" class="login-form" :model="loginForm" :rules="rules">
          <a-form-item name="loginName">
            <a-input
              v-model:value.trim="loginForm.loginName"
              size="large"
              placeholder="请输入用户名"
            >
              <template #prefix>
                <user-outlined />
              </template>
            </a-input>
          </a-form-item>

          <a-form-item name="emailCode" v-if="emailCodeShowFlag">
            <a-input-group compact>
              <a-input
                style="width: calc(100% - 120px)"
                v-model:value="loginForm.emailCode"
                autocomplete="on"
                placeholder="请输入邮箱验证码"
                size="large"
              >
                <template #prefix>
                  <mail-outlined />
                </template>
              </a-input>
              <a-button
                @click="sendSmsCode"
                class="code-btn"
                type="primary"
                size="large"
                :disabled="emailCodeButtonDisabled"
              >
                {{ emailCodeTips }}
              </a-button>
            </a-input-group>
          </a-form-item>

          <a-form-item name="password">
            <a-input-password
              v-model:value="loginForm.password"
              size="large"
              placeholder="请输入密码"
            >
              <template #prefix>
                <lock-outlined />
              </template>
            </a-input-password>
          </a-form-item>

          <a-form-item name="captchaCode">
            <div class="captcha-group">
              <a-input
                v-model:value.trim="loginForm.captchaCode"
                placeholder="请输入验证码"
                size="large"
                class="captcha-input"
              >
                <template #prefix>
                  <safety-certificate-outlined />
                </template>
              </a-input>
              <div class="captcha-image" @click="getCaptcha">
                <img :src="captchaBase64Image" alt="验证码" />
              </div>
            </div>
          </a-form-item>

          <div class="login-options">
            <a-checkbox v-model:checked="rememberPwd">记住密码</a-checkbox>
            <span class="demo-info">演示账号：admin / 123456</span>
          </div>

          <a-form-item>
            <a-button
              type="primary"
              size="large"
              block
              class="login-button"
              @click="onLogin"
              :loading="loginLoading"
            >
              <span v-if="!loginLoading">立即登录</span>
              <span v-else>登录中...</span>
            </a-button>
          </a-form-item>
        </a-form>
        <div class="register-link">
          还没有账号? <a>立即注册</a>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { message } from 'ant-design-vue';
import { onMounted, onUnmounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { loginApi } from '/@/api/system/login-api';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { LOGIN_DEVICE_ENUM } from '/@/constants/system/login-device-const';
import { useUserStore } from '/@/store/modules/system/user';
import { buildRoutes } from '/@/router/index';
import { smartSentry } from '/@/lib/smart-sentry';
import {localRead, localSave} from '/@/utils/local-util.js';
import LocalStorageKeyConst from '/@/constants/local-storage-key-const.js';
import {
  UserOutlined,
  LockOutlined,
  CheckCircleOutlined,
  GithubOutlined,
  WechatOutlined,
  DingtalkOutlined,
  MailOutlined,
  SafetyCertificateOutlined
} from '@ant-design/icons-vue';

  //--------------------- 登录表单 ---------------------------------

  const loginForm = reactive({
    loginName: 'admin',
    password: '',
    captchaCode: '',
    captchaUuid: '',
    loginDevice: LOGIN_DEVICE_ENUM.PC.value,
  });
  const rules = {
    loginName: [{ required: true, message: '用户名不能为空' }],
    password: [{ required: true, message: '密码不能为空' }],
    captchaCode: [{ required: false, message: '验证码不能为空' }],
  };

  const router = useRouter();
  const formRef = ref();
  const rememberPwd = ref(false);
  const loginLoading = ref(false);

  onMounted(() => {
    document.onkeyup = (e) => {
      if (e.keyCode === 13) {
        onLogin();
      }
    };
  });

  onUnmounted(() => {
    document.onkeyup = null;
  });

  //登录
  async function onLogin() {
    formRef.value.validate().then(async () => {
      try {
        loginLoading.value = true;
        SmartLoading.show();
        localSave('rememberPwd', rememberPwd.value);
        const res = await loginApi.login(loginForm);
        console.log('res',res.data)
        stopRefreshCaptchaInterval();
        localSave(LocalStorageKeyConst.USER_TOKEN, res.data.token ? res.data.token : '');
        message.success('登录成功');
        if (rememberPwd.value){
          localSave(LocalStorageKeyConst.USER_ACCOUNT, loginForm.loginName);
          localSave(LocalStorageKeyConst.USER_PASSWORD, loginForm.password);
        }
        //更新用户信息到pinia
        useUserStore().setUserLoginInfo(res.data);
        //构建系统的路由
        buildRoutes();
        console.log('开始跳转首页')
        await router.push('/home');
      } catch (e) {
        if (e.data && e.data.code !== 0) {
          loginForm.captchaCode = '';
          await getCaptcha();
        }
        smartSentry.captureError(e);
      } finally {
        loginLoading.value = false;
        SmartLoading.hide();
      }
    });
  }

  //--------------------- 验证码 ---------------------------------

  const captchaBase64Image = ref('');
  async function getCaptcha() {

    try {
      let captchaResult = await loginApi.getCaptcha();
      captchaBase64Image.value = captchaResult.data.captchaBase64Image;
      loginForm.captchaUuid = captchaResult.data.captchaUuid;
      beginRefreshCaptchaInterval(captchaResult.data.expireSeconds);
    } catch (e) {
      console.log(e);
    }
  }

  let refreshCaptchaInterval = null;
  function beginRefreshCaptchaInterval(expireSeconds) {
    if (refreshCaptchaInterval === null) {
      refreshCaptchaInterval = setInterval(getCaptcha, (expireSeconds - 5) * 1000);
    }
  }

  function stopRefreshCaptchaInterval() {
    if (refreshCaptchaInterval != null) {
      clearInterval(refreshCaptchaInterval);
      refreshCaptchaInterval = null;
    }
  }

  onMounted(() => {
    getCaptcha();
  });

  //--------------------- 邮箱验证码 ---------------------------------

  const emailCodeShowFlag = ref(false);
  let emailCodeTips = ref('获取邮箱验证码');
  let emailCodeButtonDisabled = ref(false);
  // 定时器
  let countDownTimer = null;
  // 开始倒计时
  function runCountDown() {
    emailCodeButtonDisabled.value = true;
    let countDown = 60;
    emailCodeTips.value = `${countDown}秒后重新获取`;
    countDownTimer = setInterval(() => {
      if (countDown > 1) {
        countDown--;
        emailCodeTips.value = `${countDown}秒后重新获取`;
      } else {
        clearInterval(countDownTimer);
        emailCodeButtonDisabled.value = false;
        emailCodeTips.value = '获取验证码';
      }
    }, 1000);
  }

  // 获取双因子登录标识
  async function getTwoFactorLoginFlag() {
    try {
      let result = await loginApi.getTwoFactorLoginFlag();
      emailCodeShowFlag.value = result.data;
    } catch (e) {
      smartSentry.captureError(e);
    }
  }

  // 发送邮箱验证码
  async function sendSmsCode() {
    try {
      SmartLoading.show();
      let result = await loginApi.sendLoginEmailCode(loginForm.loginName);
      message.success('验证码发送成功!请登录邮箱查看验证码~');
      runCountDown();
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  }
  onMounted(()=>{
    rememberPwd.value = JSON.parse(localRead('rememberPwd') || 'false');
    loginForm.loginName = localRead(LocalStorageKeyConst.USER_ACCOUNT) || 'admin';
    loginForm.password = localRead(LocalStorageKeyConst.USER_PASSWORD) || '';
  })

</script>
<style lang="less" scoped>
  @import './login.less';
</style>
