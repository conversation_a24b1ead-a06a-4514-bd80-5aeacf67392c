<template>
  <!-- 直接使用v-for在a-sub-menu上，避免template嵌套 -->
  <a-sub-menu
    v-for="directory in directories"
    :key="directory.id"
  >
    <template #title>
      <span class="menu-title">
        <folder-outlined />
        <span>{{ directory.name }} ({{ getApisByDirectoryId(directory.id).length }})</span>
      </span>
    </template>

    <!-- 接口列表 -->
    <a-menu-item
      v-for="api in getApisByDirectoryId(directory.id)"
      :key="api.id"
      @click="$emit('select', api)"
    >
      <span class="api-item">
        <a-tag :color="getMethodColor(api.method)">{{ api.method }}</a-tag>
        <span>{{ api.name }}</span>
      </span>
    </a-menu-item>

    <!-- 递归处理子目录 -->
    <directory-menu
      v-if="directory.children && directory.children.length"
      :directories="directory.children"
      :apis="apis"
      @select="$emit('select', $event)"
    />
  </a-sub-menu>
</template>

<script setup>
import { FolderOutlined } from '@ant-design/icons-vue';

// 定义props
const props = defineProps({
  directories: {
    type: Array,
    required: true
  },
  apis: {
    type: Array,
    required: true
  }
});

// 定义emits
const emit = defineEmits(['select']);

// 获取指定目录下的接口
const getApisByDirectoryId = (directoryId) => {
  return props.apis.filter(api => api.directoryId === directoryId);
};

// 获取HTTP方法对应的颜色
const getMethodColor = (method) => {
  const colors = {
    'GET': 'green',
    'POST': 'orange',
    'PUT': 'blue',
    'DELETE': 'red',
    'PATCH': 'purple'
  };
  return colors[method] || 'default';
};
</script>

<style scoped>
.menu-title {
  display: flex;
  align-items: center;
}

.menu-title .anticon {
  margin-right: 8px;
}

.api-item {
  display: flex;
  align-items: center;
}

.api-item .ant-tag {
  min-width: 40px;
  text-align: center;
  margin-right: 8px;
}
</style>