<template>
  <a-drawer
    :open="visible"
    :body-style="{ paddingBottom: '80px' }"
    :maskClosable="true"
    @close="onClose"
  >
      <!-- 搜索框 -->
    <a-input-search
      v-model:value="searchValue"
      placeholder="搜索用例名称/模块/产品线"
      enter-button
      style="margin-bottom: 16px"
    />

    <!-- 树形选择器 -->
    <a-tree
      :tree-data="treeData"
      :expanded-keys="expandedKeys"
      :checked-keys="checkedKeys"
      autoExpandParent
      checkable
      showLine
      @expand="onExpand"
      @check="onCheck"
    />
    <div class="footer">
      <a-button style="margin-right: 8px" @click="onClose">取消</a-button>
      <a-button style="margin-right: 8px" type="primary" @click="onSubmit">提交 </a-button>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import { Api } from "/@/api/business/apitest/api.js";
import { smartSentry } from "/@/lib/smart-sentry.js";
import {ApiCaseApi} from "/@/api/business/apitest/api-case-api.js";
import {message, Modal} from "ant-design-vue";


// 响应式数据
const searchValue = ref('');
const expandedKeys = ref([]);
const checkedKeys = ref([]);
const treeData = ref([]);
const loading = ref(false);

// 优化后的查询方法
const queryApiTree = async () => {
  try {
    loading.value = true;
    const res = await Api.queryApiTree();
    treeData.value = res.data;
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    loading.value = false;
  }
};


// 展开处理（修复展开逻辑）
const onExpand = (keys) => {
  expandedKeys.value = keys;
};

// 选中处理（添加防抖）
const onCheck = (checkedKeysValue) => {
  console.log(checkedKeysValue)
  checkedKeys.value = checkedKeysValue.filter(key => !key.startsWith("productLine") && !key.startsWith("module"));

};


const visible = ref(false);

const showDrawer = async () => {

  if (!treeData.value.length) await queryApiTree();
  await hasChecked()
  visible.value = true;

};

const onClose = () => {
  searchValue.value = '';
  expandedKeys.value = [];
  checkedKeys.value = [];
  visible.value = false;
};
const onSubmit = () =>{
  ApiCaseApi.add({
    caseId: props.record.caseId,
    apiIdList:checkedKeys.value
  }).then(()=>{
    message.success("接口关联成功")
    onClose()
  })
}

const hasChecked = async () => {

  try{
    const res = await ApiCaseApi.getApiTree({caseId:props.record.caseId})
    const parentKeys = getParentKeys(res.data).filter(key=>!key.startsWith("productLine") && !key.startsWith("module"))
    expandedKeys.value = parentKeys
    checkedKeys.value = parentKeys

  }catch(e){
    smartSentry.captureError(e)
  }

};
const getParentKeys = (treeData) => {
  const parentKeys = [];
  const findParentKeys = (treeData) => {
    for (const data of treeData){
      parentKeys.push(data.key)
      if (data.children.length > 0) {
        findParentKeys(data.children);
      }
    }
  }
  findParentKeys(treeData)
  return parentKeys
}

const props = defineProps({
  record: {
    type: Object,
    default: () => ({}),
  },
});

defineExpose({
  showDrawer,
});


</script>

<style lang="less" scoped>
  .footer {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 100%;
    border-top: 1px solid #e9e9e9;
    padding: 10px 16px;
    background: #fff;
    text-align: left;
    z-index: 1;
  }
</style>