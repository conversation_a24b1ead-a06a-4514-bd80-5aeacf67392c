<template>
  <a-modal
    :open="open"
    :title="isEdit ? '编辑提取规则' : '添加提取规则'"
    @cancel="onCancel"
    @ok="save"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
    >
      <a-form-item label="变量名称" name="name">
        <a-input v-model:value="formData.name" />
      </a-form-item>
      <a-form-item label="源" name="source">
        <a-select v-model:value="formData.source">
          <a-select-option v-for="option in sourceOptions" :key="option.value" :value="option.value">
            {{ option.label }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="表达式:JSONPath|正则" name="expression">
        <a-input v-model:value="formData.expression" />
      </a-form-item>
      <a-form-item label="作用域" name="scope">
        <a-radio-group v-model:value="formData.scope">
          <a-radio value="global">
            <a-tag color="#52c41a">全局</a-tag>
          </a-radio>
          <a-radio value="case">
            <a-tag color="#f5222d">用例</a-tag>
          </a-radio>
          <a-radio value="step">
            <a-tag color="#108ee9">步骤</a-tag>
          </a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="默认值" name="default">
        <a-input v-model:value="formData.default"/>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import {ref} from "vue";
import {defineProps, defineEmits, defineExpose} from 'vue'

const props = defineProps({});
const emits = defineEmits(['add','edit']);

const open = ref(false);
const isEdit = ref(false);
const formRef = ref(null);
const formData = ref({
  name: '',
  scope: 'step',
  source: 'response',
  expression: '',
  default: ''
});
const recordCopy = ref(null);
const indexCopy = ref(null)

const sourceOptions = ref([
  {label: '响应结果', value: 'response'},
  {label: '响应头', value: 'headers'},
  {label: '响应cookie', value: 'cookies'}
]);

const rules = ref({
  name: [
    {required: true, message: '请输入变量名称', trigger: 'blur'},
  ],
  source: [
    {required: true, message: '请选择源', trigger: 'change'},
  ],
  expression: [
    {required: true, message: '请输入表达式', trigger: 'blur'},
  ],
  scope: [
    {required: true, message: '请选择作用域', trigger: 'change'},
  ],
  default: [
    {required: false, message: '请输入默认值', trigger: 'blur'},
  ],
});

const showModal = (record,index) => {
  // 判断是否为编辑模式
  isEdit.value = !!record.name; // 如果有name字段则认为是编辑
  // 保存原始记录的引用
  recordCopy.value = record;
  indexCopy.value = index;

  // 填充表单数据
  Object.assign(formData.value, record);

  // 打开模态框
  open.value = true;
};

const save = async () => {
  try {
    // 表单验证
    if (formRef.value) {
      await formRef.value.validate();
    }

    if (isEdit.value) {
      // 编辑模式：更新原始记录
      // Object.assign(recordCopy.value, formData.value);
      emits('edit', {...formData.value},  indexCopy.value);
    } else {
      // 添加模式：发出添加事件
      emits('add', {...formData.value});
    }

    // 关闭模态框
    onCancel();
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

const onCancel = () => {
  recordCopy.value = null;
  open.value = false;
  indexCopy.value = null;
};

defineExpose({showModal});
</script>