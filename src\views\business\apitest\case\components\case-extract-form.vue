<template>
  <a-form :model="formData">
    <a-form-item label="变量名称">
      <a-input v-model:value="formData.name" placeholder="变量名称" />
    </a-form-item>
    <a-form-item label="源">
      <a-input v-model:value="formData.source" placeholder="请选择提取目标" />
    </a-form-item>
    <a-form-item label="表达式">
      <a-input v-model:value="formData.expression" placeholder="表达式" />
    </a-form-item>
    <a-form-item label="作用域">
      <a-input v-model:value="formData.scope" placeholder="请选择作用域" />
    </a-form-item>
    <a-form-item label="默认值">
      <a-input v-model:value="formData.default" placeholder="默认值" />
    </a-form-item>
  </a-form>
</template>
<script setup>

import {defineProps, reactive} from 'vue'

const props = defineProps({
  extractRule: {
    type: Object,
    default: () => ({}),
  },
})

const formData = reactive(props.extractRule)

</script>