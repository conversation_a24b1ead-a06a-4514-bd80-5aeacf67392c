<template>
  <div class="case-execution-container">
    <!-- 用例列表部分 -->
    <div class="case-list-container">
      <a-form class="smart-query-form">
        <a-row class="smart-query-form-row">
          <a-form-item label="用例名称" class="smart-query-form-item">
            <a-input style="width: 300px" v-model:value="queryForm.caseName" placeholder="用例名称" />
          </a-form-item>
          <a-form-item label="优先级" class="smart-query-form-item">
            <a-input style="width: 300px" v-model:value="queryForm.priority" placeholder="优先级" />
          </a-form-item>
          <a-form-item label="标签" class="smart-query-form-item">
            <a-input style="width: 300px" v-model:value="queryForm.tags" placeholder="标签" />
          </a-form-item>
          <a-form-item label="状态" style="width: 150px" class="smart-query-form-item">
            <a-select v-model:value="queryForm.status">
              <a-select-option :value=1>启用</a-select-option>
              <a-select-option :value=0>禁用</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item class="smart-query-form-item smart-margin-left10">
            <a-button-group>
              <a-button type="primary" @click="onSearch">
                <template #icon>
                  <SearchOutlined />
                </template>
                查询
              </a-button>
              <a-button @click="resetQuery">
                <template #icon>
                  <ReloadOutlined />
                </template>
                重置
              </a-button>
            </a-button-group>
          </a-form-item>
        </a-row>
      </a-form>

      <a-card size="small" :bordered="false" :hoverable="true">
        <a-row class="smart-table-btn-block">
          <div class="smart-table-operate-block">
            <a-button @click="add" type="primary">
              <template #icon>
                <PlusOutlined />
              </template>
              新增用例
            </a-button>
            <a-button type="primary" @click="addToSuite">
              <template #icon><plus-outlined /></template>
              添加到集合
            </a-button>
          </div>
        </a-row>

        <a-table
          :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          :scroll="{ x: 1300 }"
          size="small"
          :dataSource="tableData"
          :columns="columns"
          rowKey="caseId"
          :pagination="true"
          :loading="tableLoading"
          bordered
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'tags'">
              <a-tag v-for="tag in record.tags" :key="tag" color="blue">{{ tag }}</a-tag>
            </template>
            <template v-if="column.dataIndex === 'action'">
              <div class="smart-table-operate">
                <a-button @click="showDrawer(record)" size="small" type="link">编辑</a-button>
                <a-button @click="confirmDelete(record.enterpriseId)" size="small" danger type="link">删除</a-button>
              </div>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 悬浮的执行配置面板 -->
    <execution-panel :selectRowKeys="selectedRowKeys"/>
    <!-- 模态框 -->
    <a-modal
      title="添加到集合"
      :visible="addToSuiteModalVisible"
      @ok="handleAddToSuiteOk"
      @cancel="addToSuiteModalVisible = false"
      :okButtonProps="{ props: { disabled: !selectedSuiteForAdd } }"
    >
      <a-form layout="vertical">
        <a-form-item
          label="选择集合"
          :rules="[{ required: true, message: '请选择要添加到的集合' }]"
        >
          <a-select
            v-model:value="selectedSuiteForAdd"
            placeholder="选择要添加到的集合"
            style="width: 100%"
            show-search
            :filter-option="filterOption"
          >
            <a-select-option v-for="suite in suiteList" :key="suite.suiteId" :value="suite.suiteId">
              {{ suite.suiteName }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-button type="link" @click="showCreateSuiteModal">
            <template #icon><plus-outlined /></template>
            创建新集合
          </a-button>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 执行状态卡片 -->
    <a-card v-if="isRunning" class="status-card" :bordered="false">
      <div class="status-header">
        <div class="status-title">
          <loading-outlined spin />
          <span>正在执行测试...</span>
        </div>
        <div class="status-info">
          <a-tag color="success">通过: {{ passedCount }}</a-tag>
          <a-tag color="error">失败: {{ failedCount }}</a-tag>
          <a-tag color="processing">用时: {{ elapsedTime }}秒</a-tag>
        </div>
      </div>
      <div class="current-case">
        当前执行: {{ currentCase }}
      </div>
      <a-progress
        :percent="executionProgress"
        :status="executionStatus"
        stroke-color="#1890ff"
        stroke-width="6"
      />
    </a-card>
  </div>
</template>

<script setup>
import {reactive, ref, onMounted, computed, h, watch, onUnmounted} from 'vue';
import { message, Modal, Tag } from 'ant-design-vue';
import { PAGE_SIZE, PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
import { useRouter } from 'vue-router';
import { smartSentry } from '/@/lib/smart-sentry';
import { CaseApi } from "/@/api/business/apitest/case-api.js";
import { SuiteApi } from "/@/api/business/apitest/suite-api.js";
import { EnvironmentApi } from '/@/api/business/apitest/environment-api.js';
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  PlayCircleOutlined,
  LoadingOutlined,
  CodeOutlined,
  BugOutlined,
  CloudOutlined,
  EnvironmentOutlined
} from "@ant-design/icons-vue";
import ExecutionPanel from "/@/views/business/apitest/excute/components/execution-panel.vue";

// --------------------------- Props ---------------------------
const props = defineProps({
  groupId: {
    type: Number,
    default: null
  }
});

const dragPanel = ref(null);
const panelPosition = ref({ x: 20, y: 80 }); // 初始位置
const isDragging = ref(false);
const dragOffset = ref({ x: 0, y: 0 });

// --------------------------- 表格列定义 ---------------------------
const columns = ref([
  {
    title: '用例名称',
    dataIndex: 'caseName',
    minWidth: 20,
    ellipsis: true,
  },
  {
    title: '优先级',
    minWidth: 100,
    dataIndex: 'priority',
    ellipsis: true,
    customRender: ({text, record}) => {
      const colors = {
        1: '#f5222d', // 高-红色
        2: '#faad14', // 中-黄色
        3: '#52c41a'  // 低-绿色
      };
      const priority = {
        1: '高',
        2: '中',
        3: '低'
      }
      return h(Tag, { color: colors[text] || '#d9d9d9' }, () => priority[text]);
    },
  },
  {
    title: '标签',
    minWidth: 120,
    dataIndex: 'tags',
    ellipsis: true,
    customRender: ({text, record}) => {
      return h(Tag, {}, () => text ? JSON.stringify(text) : []);
    },
  },
  {
    title: '描述',
    minWidth: 100,
    dataIndex: 'desc',
    ellipsis: true,
  },
  {
    title: '状态',
    minWidth: 120,
    dataIndex: 'status',
    ellipsis: true,
    customRender: ({text, record}) => {
      return text === true ? '启用' : '禁用';
    },
  },
  {
    title: '创建用户',
    minWidth: 60,
    dataIndex: 'createUserId'
  },
  {
    title: '更新用户',
    dataIndex: 'updateUserId',
    minWidth: 20,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    minWidth: 100,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    minWidth: 100,
  },
  {
    title: '操作',
    dataIndex: 'action',
    minWidth: 100,
  },
]);

// --------------------------- 查询表单 ---------------------------
const queryFormState = {
  caseName: '',
  productLineName: '',
  moduleName: '',
  featureName:'',
  priority:'',
  tags:'',
  status: undefined,
  pageNum: 1,
  pageSize: PAGE_SIZE,
};
const queryForm = reactive({ ...queryFormState });
const tableLoading = ref(false);
const tableData = ref([]);
const total = ref(0);
const selectedRowKeys = ref([]);
const selectedCases = ref([]);

// --------------------------- 集合相关 ---------------------------
const selectedSuiteForAdd = ref(null);
const addToSuiteModalVisible = ref(false);
const suiteList = ref([]);

// --------------------------- 执行面板状态 ---------------------------
const showExecutionPanel = ref(false);
const activeTab = ref('functional');
const selectedEnv = ref('dev');
const useTestData = ref('none');
const loopCount = ref(1);
const threadCount = ref(1);
const runLocation = ref('local');
const enableNotification = ref(false);
const retryOnFailure = ref(false);
const stopOnFailure = ref(false);
const timeout = ref(30);
const saveResults = ref(true);
const generateHtmlReport = ref(true);
const pytestArgs = ref('-v');
const isRunning = ref(false);
const passedCount = ref(0);
const failedCount = ref(0);
const elapsedTime = ref(0);
const currentCase = ref('');
const executionProgress = ref(0);
const executionStatus = ref('active');
let executionTimer = null;
let progressUpdateTimer = null;

// --------------------------- 方法 ---------------------------
// 查询相关
function onSearch() {
  ajaxQuery();
}

function resetQuery() {
  Object.assign(queryForm, queryFormState);
  ajaxQuery();
}

async function ajaxQuery() {
  try {
    tableLoading.value = true;
    let responseModel = await CaseApi.getByGroupId(props.groupId);
    tableData.value = responseModel.data;
    total.value = responseModel.data.length; // 假设API返回总数
  } catch (e) {
    smartSentry.captureError(e);
    message.error("获取用例列表失败");
  } finally {
    tableLoading.value = false;
  }
}

// 获取环境列表

// 获取集合列表
async function getSuiteOperation() {
  try {
    let response = await SuiteApi.get();
    suiteList.value = response.data.map(item => {
      return {
        suiteId: item.suiteId,
        suiteName: item.suiteName
      }
    });
  } catch (e) {
    smartSentry.captureError(e);
    message.error("获取集合列表失败");
  }
}

// 删除确认
function confirmDelete(enterpriseId) {
  Modal.confirm({
    title: '确定要删除吗？',
    content: '删除后，该信息将不可恢复',
    okText: '删除',
    okType: 'danger',
    onOk() {
      console.log('OK');
    },
    cancelText: '取消',
    onCancel() {},
  });
}

// 路由跳转
let router = useRouter();
const showDrawer = async (record) => {
  await router.push({
    path: '/case/detail',
    query: {
      caseId: record.caseId
    }
  });
};

function add() {
  // 跳转到新增页面
}

// 表格选择变化事件
function onSelectChange(keys, rows) {
  selectedRowKeys.value = keys;
  selectedCases.value = rows;
}

function filterOption(input, option) {
  return option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0;
}

// 添加到集合
function handleAddToSuiteOk() {
  if (!selectedSuiteForAdd.value) {
    message.warning('请选择要添加到的集合');
    return;
  }

  // 调用 /api_test/case_suite/add 接口
  message.success('已添加到集合');
  addToSuiteModalVisible.value = false;
}

function addToSuite() {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请至少选择一个测试用例');
    return;
  }

  addToSuiteModalVisible.value = true;
}

function showCreateSuiteModal() {
  // 显示创建集合模态框
}

// 获取环境图标
function getEnvIcon(envName) {
  if (envName.includes('开发')) return CodeOutlined;
  if (envName.includes('测试')) return BugOutlined;
  if (envName.includes('生产')) return CloudOutlined;
  return EnvironmentOutlined;
}

// 切换执行面板显示
function toggleExecutionPanel() {
  showExecutionPanel.value = !showExecutionPanel.value;
}

// 保存配置
function saveConfig() {
  const config = {
    envId: selectedEnv.value,
    useTestData: useTestData.value,
    loopCount: loopCount.value,
    threadCount: threadCount.value,
    runLocation: runLocation.value,
    enableNotification: enableNotification.value,
    retryOnFailure: retryOnFailure.value,
    stopOnFailure: stopOnFailure.value,
    timeout: timeout.value,
    saveResults: saveResults.value,
    generateHtmlReport: generateHtmlReport.value,
    pytestArgs: pytestArgs.value
  };

  // 保存配置到本地存储
  localStorage.setItem('testExecutionConfig', JSON.stringify(config));
  message.success('配置已保存');
}

// 加载保存的配置
function loadSavedConfig() {
  const savedConfig = localStorage.getItem('testExecutionConfig');
  if (savedConfig) {
    try {
      const config = JSON.parse(savedConfig);
      selectedEnv.value = config.envId || 'dev';
      useTestData.value = config.useTestData || 'none';
      loopCount.value = config.loopCount || 1;
      threadCount.value = config.threadCount || 1;
      runLocation.value = config.runLocation || 'local';
      enableNotification.value = config.enableNotification || false;
      retryOnFailure.value = config.retryOnFailure || false;
      stopOnFailure.value = config.stopOnFailure || false;
      timeout.value = config.timeout || 30;
      saveResults.value = config.saveResults !== undefined ? config.saveResults : true;
      generateHtmlReport.value = config.generateHtmlReport !== undefined ? config.generateHtmlReport : true;
      pytestArgs.value = config.pytestArgs || '-v';
    } catch (error) {
      console.error('加载保存的配置失败:', error);
    }
  }
}

// 运行测试
const runTest = async () => {
  if (isRunning.value) return;

  // 检查是否选择了测试用例
  if (selectedRowKeys.value.length === 0) {
    message.warning('请至少选择一个测试用例');
    return;
  }

  isRunning.value = true;

  try {
    // 构建请求数据
    const requestData = {
      caseIds: selectedRowKeys.value,
      envId: selectedEnv.value,
      loopCount: loopCount.value,
      threadCount: threadCount.value,
      runLocation: runLocation.value,
      useTestData: useTestData.value !== 'none',
      testDataType: useTestData.value,
      notifyOnComplete: enableNotification.value,
      retryOnFailure: retryOnFailure.value,
      stopOnFailure: stopOnFailure.value,
      timeout: timeout.value,
      saveResults: saveResults.value,
      generateHtmlReport: generateHtmlReport.value,
      pytestArgs: pytestArgs.value
    };

    console.log('执行测试:', requestData);

    // 启动执行
    executionProgress.value = 0;
    passedCount.value = 0;
    failedCount.value = 0;
    elapsedTime.value = 0;
    currentCase.value = '准备中...';
    executionStatus.value = 'active';

    // 启动计时器
    executionTimer = setInterval(() => {
      elapsedTime.value++;
    }, 1000);

    // 模拟进度更新
    simulateProgressUpdate();
  } catch (error) {
    console.error('执行测试失败:', error);
    message.error('执行测试失败');
    isRunning.value = false;
  }
};

// 模拟进度更新
function simulateProgressUpdate() {
  const cases = selectedCases.value;
  const totalCases = cases.length;
  let completedCases = 0;

  // 确保清除之前的更新定时器
  if (progressUpdateTimer) {
    clearInterval(progressUpdateTimer);
  }

  progressUpdateTimer = setInterval(() => {
    if (!isRunning.value) {
      clearInterval(progressUpdateTimer);
      return;
    }

    if (completedCases < totalCases) {
      completedCases++;
      currentCase.value = cases[completedCases - 1].caseName;

      // 随机设置通过/失败
      if (Math.random() > 0.2) {
        passedCount.value++;
      } else {
        failedCount.value++;

        // 如果设置了失败时停止
        if (stopOnFailure.value) {
          clearInterval(progressUpdateTimer);
          isRunning.value = false;
          clearInterval(executionTimer);
          executionStatus.value = 'exception';
          message.error('测试执行失败，已停止');
          return;
        }
      }

      // 更新进度
      executionProgress.value = Math.floor((completedCases / totalCases) * 100);
    } else {
      clearInterval(progressUpdateTimer);

      // 只有在执行状态下才显示完成消息，防止中断后仍显示完成
      if (isRunning.value) {
        // 执行完成
        isRunning.value = false;
        clearInterval(executionTimer);
        executionStatus.value = failedCount.value > 0 ? 'exception' : 'success';
        message.success('测试执行完成');
      }
    }
  }, 1000); // 每1秒更新一次
}
const startDrag = (event) => {
  isDragging.value = true;

  // 计算鼠标在面板内的相对位置
  const rect = dragPanel.value.getBoundingClientRect();
  dragOffset.value = {
    x: event.clientX - rect.left,
    y: event.clientY - rect.top
  };

  // 添加全局事件监听
  document.addEventListener('mousemove', onDrag);
  document.addEventListener('mouseup', stopDrag);

  // 防止文本选择
  event.preventDefault();
};

// 拖拽中
const onDrag = (event) => {
  if (!isDragging.value) return;

  // 计算新位置
  panelPosition.value = {
    x: event.clientX - dragOffset.value.x,
    y: event.clientY - dragOffset.value.y
  };

  // 限制不超出屏幕边界
  const rect = dragPanel.value.getBoundingClientRect();
  const maxX = window.innerWidth - rect.width;
  const maxY = window.innerHeight - rect.height;

  if (panelPosition.value.x < 0) panelPosition.value.x = 0;
  if (panelPosition.value.y < 0) panelPosition.value.y = 0;
  if (panelPosition.value.x > maxX) panelPosition.value.x = maxX;
  if (panelPosition.value.y > maxY) panelPosition.value.y = maxY;
};

// 停止拖拽
const stopDrag = () => {
  isDragging.value = false;
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mouseup', stopDrag);
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mouseup', stopDrag);
});
// 监听groupId变化
watch(() => props.groupId, () => {
  ajaxQuery();
});

// 组件挂载时执行
onMounted(() => {
  ajaxQuery();
  getSuiteOperation();
  loadSavedConfig();
});
</script>

<style scoped>
.execution-panel-container {
  position: fixed;
  /* 移除 top 和 right 固定值，改为由 JavaScript 控制位置 */
  width: 380px;
  max-height: calc(100vh - 100px);
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.execution-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f0f2f5;
  border-bottom: 1px solid #e8e8e8;
  cursor: move; /* 添加移动光标指示可拖动 */
}
.case-execution-container {
  position: relative;
  width: 100%;
  height: 100%;
}

/* 用例列表样式 */
.case-list-container {
  width: 100%;
}

/* 执行面板样式 */
.execution-panel-container {
  position: fixed;
  top: 80px;
  right: 20px;
  width: 380px;
  max-height: calc(100vh - 100px);
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.execution-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f0f2f5;
  border-bottom: 1px solid #e8e8e8;
}

.panel-title {
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 16px;
}

.panel-title .anticon {
  margin-right: 8px;
}

.execution-config-panel {
  padding: 16px;
  overflow-y: auto;
  max-height: calc(100vh - 160px);
}

/* 标签页样式 */
.tab-container {
  margin-bottom: 16px;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  margin: 12px 0 8px 0;
  color: #333;
  display: flex;
  align-items: center;
}

.section-title :deep(.anticon) {
  margin-left: 4px;
  color: #999;
  font-size: 12px;
}

.environment-selector,
.test-data-selector,
.run-location-selector {
  margin-bottom: 16px;
}

.config-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.config-col {
  flex: 1;
}

.notification-config {
  display: flex;
  align-items: center;
  margin: 16px 0;
}

.notification-text {
  margin-left: 8px;
}

.advanced-settings {
  margin-top: auto;
  margin-bottom: 16px;
}

.advanced-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.option-label {
  font-size: 14px;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

:deep(.ant-collapse-header) {
  font-weight: 500;
}

/* 执行状态卡片样式 */
.status-card {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 380px;
  z-index: 1001;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.status-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
}

.status-title .anticon {
  margin-right: 8px;
  color: #1890ff;
}

.status-info {
  display: flex;
  gap: 8px;
}

.current-case {
  margin-bottom: 12px;
  color: #666;
}

/* 环境选项样式 */
.env-option {
  display: flex;
  align-items: center;
}

.env-option .anticon {
  margin-right: 8px;
}

/* 表单样式 */
.smart-query-form {
  background-color: #fff;
  padding: 16px;
  margin-bottom: 16px;
  border-radius: 4px;
}

.smart-query-form-row {
  display: flex;
  flex-wrap: wrap;
}

.smart-query-form-item {
  margin-right: 16px;
  margin-bottom: 8px;
}

.smart-margin-left10 {
  margin-left: 10px;
}

.smart-table-btn-block {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
}

.smart-table-operate-block {
  display: flex;
  gap: 8px;
}

.smart-query-table-page {
  margin-top: 16px;
  text-align: right;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .execution-panel-container {
    width: 100%;
    top: 0;
    right: 0;
    height: 100vh;
    border-radius: 0;
  }

  .status-card {
    width: 100%;
    right: 0;
    bottom: 0;
    border-radius: 0;
  }

  .config-row {
    flex-direction: column;
    gap: 8px;
  }
}
</style>