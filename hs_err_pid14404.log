#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 268435456 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3828), pid=14404, tid=51664
#
# JRE version:  (21.0.4+13) (build )
# Java VM: OpenJDK 64-Bit Server VM (21.0.4+13-b509.17, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://gitee.com': 

Host: Intel(R) Core(TM) i5-9600KF CPU @ 3.70GHz, 6 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.3912)
Time: Fri Jul 25 18:27:22 2025  Windows 11 , 64 bit Build 26100 (10.0.26100.3912) elapsed time: 0.175928 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x0000028ed4bb3e40):  JavaThread "Unknown thread" [_thread_in_vm, id=51664, stack(0x000000d733a00000,0x000000d733b00000) (1024K)]

Stack: [0x000000d733a00000,0x000000d733b00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e36d9]
V  [jvm.dll+0x8c0bd3]
V  [jvm.dll+0x8c312e]
V  [jvm.dll+0x8c3813]
V  [jvm.dll+0x288256]
V  [jvm.dll+0x6dfe65]
V  [jvm.dll+0x6d433a]
V  [jvm.dll+0x36209b]
V  [jvm.dll+0x369c46]
V  [jvm.dll+0x3bbd76]
V  [jvm.dll+0x3bc048]
V  [jvm.dll+0x33485c]
V  [jvm.dll+0x33554b]
V  [jvm.dll+0x888039]
V  [jvm.dll+0x3c8ea8]
V  [jvm.dll+0x8711d8]
V  [jvm.dll+0x45d85e]
V  [jvm.dll+0x45f541]
C  [jli.dll+0x52ab]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x9c5dc]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ffe28122108, length=0, elements={
}

Java Threads: ( => current thread )
Total: 0

Other Threads:
  0x0000028ed6f710d0 WorkerThread "GC Thread#0"                     [id=60304, stack(0x000000d733b00000,0x000000d733c00000) (1024K)]
  0x0000028ed6fc0410 ConcurrentGCThread "G1 Main Marker"            [id=22712, stack(0x000000d733c00000,0x000000d733d00000) (1024K)]
  0x0000028ed6fc0f10 WorkerThread "G1 Conc#0"                       [id=58084, stack(0x000000d733d00000,0x000000d733e00000) (1024K)]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffe27817917]
VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffe28196a48] Heap_lock - owner thread: 0x0000028ed4bb3e40

Heap address: 0x0000000701000000, size: 4080 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 16 size 48 Howl #buckets 8 coarsen threshold 3686 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 4096

Heap:
 garbage-first heap   total 0K, used 0K [0x0000000701000000, 0x0000000800000000)
  region size 2048K, 0 young (0K), 0 survivors (0K)
 Metaspace       used 0K, committed 0K, reserved 0K
  class space    used 0K, committed 0K, reserved 0K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom

Card table byte_map: [0x0000028eea620000,0x0000028eeae20000] _byte_map_base: 0x0000028ee6e18000

Marking Bits: (CMBitMap*) 0x0000028ed6f717d0
 Bits: [0x0000028eeae20000, 0x0000028eeede0000)

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.006 Loaded shared library D:\PyCharm\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes loaded (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (0 events):
No events

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff6dd020000 - 0x00007ff6dd02a000 	D:\PyCharm\jbr\bin\java.exe
0x00007ffea91c0000 - 0x00007ffea9426000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffea8670000 - 0x00007ffea8739000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffea6830000 - 0x00007ffea6bfc000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffea63e0000 - 0x00007ffea652b000 	C:\Windows\System32\ucrtbase.dll
0x00007ffe9a470000 - 0x00007ffe9a488000 	D:\PyCharm\jbr\bin\jli.dll
0x00007ffe9c7a0000 - 0x00007ffe9c7bb000 	D:\PyCharm\jbr\bin\VCRUNTIME140.dll
0x00007ffea82a0000 - 0x00007ffea846a000 	C:\Windows\System32\USER32.dll
0x00007ffea6800000 - 0x00007ffea6827000 	C:\Windows\System32\win32u.dll
0x00007ffea88d0000 - 0x00007ffea88fb000 	C:\Windows\System32\GDI32.dll
0x00007ffe95c30000 - 0x00007ffe95eca000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3912_none_3e07963ce335137e\COMCTL32.dll
0x00007ffea6d80000 - 0x00007ffea6eb2000 	C:\Windows\System32\gdi32full.dll
0x00007ffea7df0000 - 0x00007ffea7e99000 	C:\Windows\System32\msvcrt.dll
0x00007ffea66b0000 - 0x00007ffea6753000 	C:\Windows\System32\msvcp_win.dll
0x00007ffea8900000 - 0x00007ffea8930000 	C:\Windows\System32\IMM32.DLL
0x00007ffea0e60000 - 0x00007ffea0e6c000 	D:\PyCharm\jbr\bin\vcruntime140_1.dll
0x00007ffe67930000 - 0x00007ffe679bd000 	D:\PyCharm\jbr\bin\msvcp140.dll
0x00007ffe274d0000 - 0x00007ffe28287000 	D:\PyCharm\jbr\bin\server\jvm.dll
0x00007ffea6f90000 - 0x00007ffea7042000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffea90d0000 - 0x00007ffea9176000 	C:\Windows\System32\sechost.dll
0x00007ffea78e0000 - 0x00007ffea79f6000 	C:\Windows\System32\RPCRT4.dll
0x00007ffea7a00000 - 0x00007ffea7a74000 	C:\Windows\System32\WS2_32.dll
0x00007ffea4f40000 - 0x00007ffea4f9e000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffe9f520000 - 0x00007ffe9f556000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffe9f560000 - 0x00007ffe9f56b000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffea4f20000 - 0x00007ffea4f34000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffea51f0000 - 0x00007ffea520a000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffe9c730000 - 0x00007ffe9c73a000 	D:\PyCharm\jbr\bin\jimage.dll
0x00007ffea31d0000 - 0x00007ffea3411000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffea7ea0000 - 0x00007ffea8224000 	C:\Windows\System32\combase.dll
0x00007ffea87e0000 - 0x00007ffea88c0000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffe93f30000 - 0x00007ffe93f69000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffea6760000 - 0x00007ffea67f9000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffe95c10000 - 0x00007ffe95c2f000 	D:\PyCharm\jbr\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\PyCharm\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3912_none_3e07963ce335137e;D:\PyCharm\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://gitee.com': 
java_class_path (initial): D:/PyCharm/plugins/vcs-git/lib/git4idea-rt.jar;D:/PyCharm/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 6                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 268435456                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4278190080                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4278190080                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=D:/Git/mingw64/libexec/git-core;D:/Git/mingw64/libexec/git-core;D:\Git\mingw64\bin;D:\Git\usr\bin;C:\Users\<USER>\bin;D:\miniconda3;D:\miniconda3\Library\mingw-w64\bin;D:\miniconda3\Library\usr\bin;D:\miniconda3\Library\bin;D:\miniconda3\Scripts;D:\miniconda3\bin;D:\miniconda3\condabin;C:\Program Files\Common Files\Oracle\Java\javapath;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;D:\Git\cmd;D:\nvm;D:\nodejs;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\nvm;D:\nodejs;d:\PyCharm\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;D:\allure-2.32.0\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;D:\NetSarang\Xshell 8;D:\NetSarang\Xftp 8;D:\Tencent\微信web开发者工具\dll;C:\Windows\dll;.;C:\Program Files\python;C:\Program Files\python\Scripts;.;D:\BtSoft\panel\script;.;d:\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\nvm;D:\nodejs;d:\PyCharm\bin;.;D:\IntelliJ IDEA\bin;.;D:\Microsoft VS Code\bin;D:\cursor\resources\app\bin
USERNAME=Administrator
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 158 Stepping 13, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

Process memory usage:
Resident Set Size: 13032K (0% of 16711012K total physical memory with 1754052K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.3912)
OS uptime: 7 days 7:26 hours
Hyper-V role detected

CPU: total 6 (initial active 6) (6 cores per cpu, 1 threads per core) family 6 model 158 stepping 13 microcode 0xf4, cx8, cmov, fxsr, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv, rdtscp, f16c
Processor Information for all 6 processors :
  Max Mhz: 3696, Current Mhz: 3696, Mhz Limit: 3696

Memory: 4k page, system-wide physical 16319M (1712M free)
TotalPageFile size 30399M (AvailPageFile size 82M)
current process WorkingSet (physical memory assigned to process): 12M, peak: 12M
current process commit charge ("private bytes"): 56M, peak: 312M

vm_info: OpenJDK 64-Bit Server VM (21.0.4+13-b509.17) for windows-amd64 JRE (21.0.4+13-b509.17), built on 2024-09-04 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
