<!-- components/ResponseCookiesViewer.vue -->
<template>
  <div class="cookies-viewer">
    <a-empty v-if="!cookies || cookies.length === 0" description="没有Cookie数据" />

    <template v-else>
      <a-table
        :dataSource="cookies"
        :columns="columns"
        :pagination="false"
        size="small"
        bordered
        :scroll="{ y: 300, x: 800 }"
        rowKey="name"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'value'">
            <a-typography-paragraph
              :copyable="{ text: record.value }"
              :ellipsis="{ rows: 2, expandable: true, symbol: '展开' }"
            >
              {{ record.value }}
            </a-typography-paragraph>
          </template>

          <template v-else-if="column.dataIndex === 'expires'">
            <span v-if="record.expires">{{ formatDate(record.expires) }}</span>
            <span v-else>会话</span>
          </template>

          <template v-else-if="column.dataIndex === 'secure'">
            <a-tag v-if="record.secure" color="success">安全</a-tag>
            <a-tag v-else color="warning">不安全</a-tag>
          </template>

          <template v-else-if="column.dataIndex === 'httpOnly'">
            <a-tag v-if="record.httpOnly" color="blue">是</a-tag>
            <a-tag v-else color="default">否</a-tag>
          </template>
        </template>
      </a-table>

      <div class="cookies-info">
        <div class="info-row">
          <a-badge status="success" text="安全" /> - 仅通过HTTPS发送
        </div>
        <div class="info-row">
          <a-badge status="blue" text="HttpOnly" /> - JavaScript无法访问
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
import { defineProps } from 'vue';

const props = defineProps({
  cookies: {
    type: Array,
    default: () => []
  }
});

// 表格列定义
const columns = [
  { title: '名称', dataIndex: 'name', width: 150, fixed: 'left' },
  { title: '值', dataIndex: 'value', width: 200 },
  { title: '域', dataIndex: 'domain', width: 150 },
  { title: '路径', dataIndex: 'path', width: 100 },
  { title: '过期时间', dataIndex: 'expires', width: 150 },
  { title: '安全', dataIndex: 'secure', width: 80 },
  { title: 'HttpOnly', dataIndex: 'httpOnly', width: 100 }
];

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '会话';

  try {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return dateStr;

    return date.toLocaleString();
  } catch (error) {
    return dateStr;
  }
};
</script>

<style scoped>
.cookies-viewer {
  display: flex;
  flex-direction: column;
}

.cookies-info {
  margin-top: 16px;
  font-size: 12px;
  color: #666;
}

.info-row {
  margin-bottom: 4px;
}
</style>