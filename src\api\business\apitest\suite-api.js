import {getRequest, postRequest} from "/@/lib/axios.js";

export const SuiteApi = {
    pageQuery: (param) => {
        return getRequest('/api_test/suite/page', param);
    },
    add(data){
        return postRequest('/api_test/suite/add', data);
    },
    update(data){
        return postRequest('/api_test/suite/update', data);
    },
    delete(ids){
        return getRequest('/api_test/suite/delete/',{suiteIds:ids});
    },
    get(params){
        return getRequest('/api_test/suite/get', params);
    }
}