<template>
  <div class="script-editor-container">
    <div class="editor-toolbar">
      <a-space>
        <a-button size="small" @click="formatCode">
          <template #icon><code-outlined /></template>
          格式化
        </a-button>
        <a-button size="small" @click="insertTemplate">
          <template #icon><plus-outlined /></template>
          插入模板
        </a-button>
        <a-dropdown>
          <a-button size="small">
            <template #icon><book-outlined /></template>
            帮助
            <down-outlined />
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item @click="showVariableHelp">变量使用</a-menu-item>
              <a-menu-item @click="showApiHelp">API调用</a-menu-item>
              <a-menu-item @click="showAssertHelp">断言方法</a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </a-space>
    </div>

    <div class="editor-wrapper">
      <a-textarea
        v-model:value="localScript"
        :rows="rows"
        placeholder="// 在此输入JavaScript代码
// 可用变量:
// - pm: 类似Postman的pm对象
// - response: 响应对象
// - request: 请求对象
// - variables: 全局变量

console.log('脚本执行');"
        class="script-textarea"
        @change="handleChange"
      />
    </div>

    <!-- 变量提示面板 -->
    <div v-if="showVariables" class="variables-panel">
      <a-collapse size="small">
        <a-collapse-panel key="variables" header="可用变量">
          <div class="variable-item" v-for="variable in availableVariables" :key="variable.name">
            <code @click="insertVariable(variable.name)">{{ variable.name }}</code>
            <span class="variable-desc">{{ variable.description }}</span>
          </div>
        </a-collapse-panel>
      </a-collapse>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import {
  CodeOutlined,
  PlusOutlined,
  BookOutlined,
  DownOutlined
} from '@ant-design/icons-vue';

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  rows: {
    type: Number,
    default: 8
  },
  showVariables: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['update:modelValue']);

const localScript = ref(props.modelValue);

// 可用变量列表
const availableVariables = ref([
  { name: 'pm.response.json()', description: '获取响应JSON数据' },
  { name: 'pm.response.text()', description: '获取响应文本' },
  { name: 'pm.response.status', description: '响应状态码' },
  { name: 'pm.globals.set(key, value)', description: '设置全局变量' },
  { name: 'pm.globals.get(key)', description: '获取全局变量' },
  { name: 'pm.test(name, fn)', description: '添加测试断言' },
  { name: 'pm.expect(actual)', description: '断言期望值' }
]);

// 监听变化
watch(localScript, (newVal) => {
  emit('update:modelValue', newVal);
});

watch(() => props.modelValue, (newVal) => {
  localScript.value = newVal;
});

// 格式化代码
const formatCode = () => {
  try {
    // 简单的代码格式化
    let formatted = localScript.value
      .replace(/;/g, ';\n')
      .replace(/{/g, '{\n  ')
      .replace(/}/g, '\n}')
      .replace(/\n\s*\n/g, '\n');

    localScript.value = formatted;
    message.success('代码已格式化');
  } catch (error) {
    message.error('格式化失败');
  }
};

// 插入模板
const insertTemplate = () => {
  const template = `
// 响应数据提取
const responseData = pm.response.json();
console.log('响应数据:', responseData);

// 设置变量
pm.globals.set('token', responseData.token);

// 添加断言
pm.test('状态码为200', function() {
  pm.expect(pm.response.status).to.equal(200);
});

pm.test('响应包含token', function() {
  pm.expect(responseData).to.have.property('token');
});
`;

  localScript.value += template;
};

// 插入变量
const insertVariable = (variableName) => {
  const textarea = document.querySelector('.script-textarea textarea');
  if (textarea) {
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const text = localScript.value;

    localScript.value = text.substring(0, start) + variableName + text.substring(end);

    // 设置光标位置
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + variableName.length, start + variableName.length);
    }, 0);
  }
};

// 帮助方法
const showVariableHelp = () => {
  message.info('变量使用帮助：使用pm对象访问请求和响应数据');
};

const showApiHelp = () => {
  message.info('API调用帮助：可以在脚本中发起HTTP请求');
};

const showAssertHelp = () => {
  message.info('断言帮助：使用pm.test()和pm.expect()进行断言');
};

const handleChange = () => {
  emit('update:modelValue', localScript.value);
};
</script>

<style scoped>
.script-editor-container {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

.editor-toolbar {
  background: #fafafa;
  padding: 8px 12px;
  border-bottom: 1px solid #d9d9d9;
}

.editor-wrapper {
  position: relative;
}

.script-textarea {
  border: none;
  border-radius: 0;
}

:deep(.script-textarea .ant-input) {
  border: none;
  box-shadow: none;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.variables-panel {
  border-top: 1px solid #d9d9d9;
  background: #f9f9f9;
}

.variable-item {
  display: flex;
  align-items: center;
  padding: 4px 0;
  cursor: pointer;
}

.variable-item:hover {
  background: #e6f7ff;
}

.variable-item code {
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 3px;
  margin-right: 8px;
  cursor: pointer;
  color: #1890ff;
}

.variable-desc {
  color: #666;
  font-size: 12px;
}
</style>