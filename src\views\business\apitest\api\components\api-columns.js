import {h, ref} from "vue";
import {Tag} from "ant-design-vue";

export const apiColumns = ref([
      {
      title: '产品线',
      dataIndex: 'productLineName',
      minWidth: 20,
      ellipsis: true,
    },
      {
      title: '模块',
      dataIndex: 'moduleName',
      minWidth: 20,
      ellipsis: true,
    },
      {
      title: '功能点',
      dataIndex: 'featureName',
      minWidth: 20,
      ellipsis: true,
    },
    {
      title: '接口名称',
      dataIndex: 'name',
      minWidth: 20,
      ellipsis: true,
    },
    {
      title: '接口地址',
      dataIndex: 'url',
      minWidth: 20,
      ellipsis: true,
    },
    {
      title: '请求方法',
      dataIndex: 'method',
      width: 100,
      customRender: ({ text }) => {
        const colorMap = {
          'GET': '#108ee9',      // 蓝色
          'POST': '#87d068',     // 绿色
          'PUT': '#2db7f5',      // 浅蓝色
          'DELETE': '#f50',      // 红色
          'PATCH': '#722ed1',    // 紫色
          'OPTIONS': '#faad14',  // 黄色
          'HEAD': '#fadb14'      // 浅黄色
        };
        return h(Tag, { color: colorMap[text.toUpperCase()] || '#d9d9d9' }, () => text.toUpperCase());
      },
    },
    {
      title: '数据类型',
      minWidth: 100,
      dataIndex: 'contentType',
      ellipsis: true,
    },
    {
      title: '请求头',
      width: 120,
      dataIndex: 'headers',
      ellipsis: true,
    },
    {
      title: '请求数据',
      minWidth: 100,
      dataIndex: 'data',
      ellipsis: true,
      customRender: ({ text }) => {
        return JSON.stringify(text);
      },
    },
    {
      title: '备注',
      minWidth: 50,
      dataIndex: 'desc',
    },
      {
      title: '状态',
      minWidth: 50,
      dataIndex: 'status',
      customRender: ({ text }) => {
        console.log(text)
        return text === 1 ? '启用' : '禁用';
      },
    },
    {
      title: '创建用户',
      minWidth: 60,
      dataIndex: 'createUserId',
    },
    {
      title: '更新用户',
      dataIndex: 'updateUserId',
      minWidth: 20,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      minWidth: 100,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      minWidth: 100,
    },
    {
      title: '操作',
      dataIndex: 'action',
      minWidth: 100,
    },
  ]);