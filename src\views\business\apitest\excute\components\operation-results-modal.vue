<template>
  <a-modal
    v-model:open="visible"
    title="前后置操作执行结果"
    width="1200px"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="operation-results-container">
      <a-empty v-if="!currentResults || currentResults.length === 0" description="暂无执行结果" />

      <div v-else>
        <a-collapse v-model:activeKey="activeKeys" ghost>
          <a-collapse-panel
            v-for="(result, index) in currentResults"
            :key="index"
            :header="getResultHeader(result, index)"
          >
            <template #extra>
              <a-tag :color="result.success ? 'success' : 'error'">
                {{ result.success ? '成功' : '失败' }}
              </a-tag>
            </template>

            <!-- 基本信息 -->
            <a-descriptions :column="2" bordered size="small" style="margin-bottom: 16px;">
              <a-descriptions-item label="操作类型">
                <a-tag :color="getOperationColor(result.operationType)">
                  {{ getOperationLabel(result.operationType) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="操作名称">
                {{ result.operationName || '-' }}
              </a-descriptions-item>
              <a-descriptions-item label="执行状态">
                <a-tag :color="result.success ? 'success' : 'error'">
                  {{ result.success ? '成功' : '失败' }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="执行时间">
                {{ result.executionTime }}ms
              </a-descriptions-item>
              <a-descriptions-item label="消息" :span="2">
                {{ result.message || '-' }}
              </a-descriptions-item>
              <a-descriptions-item v-if="result.error" label="错误信息" :span="2">
                <a-typography-text type="danger">{{ result.error }}</a-typography-text>
              </a-descriptions-item>
            </a-descriptions>

            <!-- 设置的变量 -->
            <div v-if="result.variablesSet && Object.keys(result.variablesSet).length > 0" style="margin-bottom: 16px;">
              <h4>设置的变量</h4>
              <a-table
                :dataSource="getVariablesTableData(result.variablesSet)"
                :columns="variableColumns"
                size="small"
                :pagination="false"
                bordered
              />
            </div>

            <!-- 操作数据 -->
            <div v-if="result.data">
              <h4>操作数据</h4>

              <!-- 断言结果 -->
              <div v-if="result.operationType === 'assertion'">
                <!-- 断言统计信息 -->
                <div v-if="result.data.total !== undefined" style="margin-bottom: 16px;">
                  <a-descriptions :column="3" size="small" bordered>
                    <a-descriptions-item label="总数">{{ result.data.total }}</a-descriptions-item>
                    <a-descriptions-item label="通过">
                      <a-tag color="success">{{ result.data.passed }}</a-tag>
                    </a-descriptions-item>
                    <a-descriptions-item label="失败">
                      <a-tag color="error">{{ result.data.failed }}</a-tag>
                    </a-descriptions-item>
                  </a-descriptions>
                </div>

                <!-- 断言详情表格 -->
                <a-table
                  :dataSource="getAssertionTableData(result.data)"
                  :columns="assertionColumns"
                  size="small"
                  :pagination="false"
                  bordered
                  row-key="name"
                >
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'result'">
                      <a-tag :color="record.passed ? 'success' : 'error'">
                        {{ record.passed ? '通过' : '失败' }}
                      </a-tag>
                    </template>
                    <template v-else-if="column.key === 'actual'">
                      <a-tooltip :title="String(record.actual)">
                        <span class="truncate-text">{{ String(record.actual) }}</span>
                      </a-tooltip>
                    </template>
                    <template v-else-if="column.key === 'expected'">
                      <a-tooltip :title="String(record.expected)">
                        <span class="truncate-text">{{ String(record.expected) }}</span>
                      </a-tooltip>
                    </template>
                    <template v-else-if="column.key === 'message'">
                      <a-tooltip :title="record.message">
                        <span class="truncate-text">{{ record.message }}</span>
                      </a-tooltip>
                    </template>
                  </template>
                </a-table>
              </div>

              <!-- 其他操作结果 (data 是 dict) -->
              <div v-else>
                <json-viewer
                  :value="result.data"
                  :expand-depth="2"
                  copyable
                  boxed
                  sort
                />
              </div>
            </div>
          </a-collapse-panel>
        </a-collapse>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, computed } from 'vue';
import { JsonViewer } from 'vue3-json-viewer';
import 'vue3-json-viewer/dist/vue3-json-viewer.css';
const props = defineProps({
  results: {
    type: Array,
    default: () => []
  }
});

const visible = ref(false);
const activeKeys = ref([]);
const currentResults = ref([]);

// 变量表格列定义
const variableColumns = [
  {
    title: '变量名',
    dataIndex: 'name',
    key: 'name',
    width: 200
  },
  {
    title: '变量值',
    dataIndex: 'value',
    key: 'value',
    ellipsis: true
  }
];

// 断言结果表格列定义
const assertionColumns = [
  {
    title: '断言名称',
    dataIndex: 'name',
    key: 'name',
    width: 150
  },
  {
    title: '断言路径',
    dataIndex: 'path',
    key: 'path',
    width: 150
  },
  {
    title: '期望值',
    dataIndex: 'expected',
    key: 'expected',
    width: 120,
    ellipsis: true
  },
  {
    title: '实际值',
    dataIndex: 'actual',
    key: 'actual',
    width: 120,
    ellipsis: true
  },
  {
    title: '比较运算符',
    dataIndex: 'operator',
    key: 'operator',
    width: 100
  },
  {
    title: '结果',
    dataIndex: 'passed',
    key: 'result',
    width: 80
  },
  {
    title: '消息',
    dataIndex: 'message',
    key: 'message',
    ellipsis: true
  }
];

// 获取操作类型标签
const getOperationLabel = (operationType) => {
  const labels = {
    'script': '自定义脚本',
    'database': '数据库操作',
    'extract_variable': '变量提取',
    'assertion': '断言'
  };
  return labels[operationType] || operationType;
};

// 获取操作类型颜色
const getOperationColor = (operationType) => {
  const colors = {
    'script': '#722ed1',
    'database': '#52c41a',
    'extract_variable': '#1890ff',
    'assertion': '#fa8c16'
  };
  return colors[operationType] || '#d9d9d9';
};

// 获取结果标题
const getResultHeader = (result, index) => {
  const operationLabel = getOperationLabel(result.operationType);
  const operationName = result.operationName ? ` - ${result.operationName}` : '';
  return `${index + 1}. ${operationLabel}${operationName}`;
};

// 转换变量数据为表格格式
const getVariablesTableData = (variables) => {
  return Object.entries(variables).map(([name, value]) => ({
    name,
    value: typeof value === 'object' ? JSON.stringify(value) : String(value)
  }));
};

// 获取断言表格数据
const getAssertionTableData = (data) => {
  // 如果 data 直接是数组，返回数组
  if (Array.isArray(data)) {
    return data;
  }

  // 如果 data 有 assertions 字段，返回 assertions 数组
  if (data && data.assertions && Array.isArray(data.assertions)) {
    return data.assertions;
  }

  // 否则返回空数组
  return [];
};

// 显示模态框
const showModal = (results) => {
  currentResults.value = results || [];
  visible.value = true;
  // 默认展开第一个结果
  activeKeys.value = currentResults.value.length > 0 ? [0] : [];
};

// 关闭模态框
const handleCancel = () => {
  visible.value = false;
  activeKeys.value = [];
};

defineExpose({
  showModal
});
</script>

<style scoped>
.operation-results-container {
  max-height: 600px;
  overflow-y: auto;
}

.truncate-text {
  display: inline-block;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

:deep(.ant-collapse-header) {
  align-items: center !important;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
}

:deep(.vjs-tree) {
  font-size: 12px;
}
</style>
