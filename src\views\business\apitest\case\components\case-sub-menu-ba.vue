<template>
  <template v-for="menuItem in menuTree" :key="menuItem.groupId">
    <template v-if="menuItem.children?.length===0 && menuItem.cases?.length===0">
      <a-menu-item :key="'group-' + menuItem.groupId">
        <folder-outlined />
        <span>{{menuItem.name}}</span>
      </a-menu-item>
    </template>
    <template v-else>
      <a-sub-menu :key="'group-' + menuItem.groupId">
        <template #title>
          <folder-outlined />
          <span>{{menuItem.name}}</span>
        </template>
        <a-menu-item v-for="caseItem in menuItem.cases" :key="'case-' + caseItem.caseId">
          <file-outlined />
          <span>{{caseItem.caseName}}</span>
        </a-menu-item>
        <case-sub-menu-ba :menu-tree="menuItem.children" />
      </a-sub-menu>
    </template>
  </template>
</template>

<script setup>
import {
  PlusOutlined, FolderOutlined, FileOutlined, <PERSON>Outlined,
  DeleteOutlined, MoreOutlined, SaveOutlined, Play<PERSON>ircleOutlined,
  FolderAddOutlined
} from '@ant-design/icons-vue';
// 定义props
const props = defineProps({
  menuTree: {
    type: Array,
    required: true
  }
});
const onSubMenuClick = (keyPath, event) => {
  console.log('onSubMenuClick', keyPath);
};

// 定义emits
defineEmits(['select']);
</script>