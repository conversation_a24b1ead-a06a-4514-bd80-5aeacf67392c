<!-- components/RequestSettingsEditor.vue -->
<template>
  <div class="settings-editor">
    <a-form layout="vertical">
      <a-form-item label="请求超时时间">
        <a-input-number
          v-model:value="localSettings.timeout"
          :min="1000"
          :max="300000"
          :step="1000"
          addon-after="毫秒"
          style="width: 200px"
          @change="emitUpdate"
        />
        <div class="form-help-text">请求超过此时间将自动取消</div>
      </a-form-item>

      <a-form-item label="重定向设置">
        <a-switch
          v-model:checked="localSettings.followRedirects"
          @change="emitUpdate"
        />
        <span class="setting-label">自动跟随重定向</span>

        <div v-if="localSettings.followRedirects" style="margin-top: 8px">
          <a-input-number
            v-model:value="localSettings.maxRedirects"
            :min="1"
            :max="10"
            :step="1"
            style="width: 120px"
            @change="emitUpdate"
          />
          <span class="setting-label">最大重定向次数</span>
        </div>
      </a-form-item>

      <a-form-item label="SSL/TLS 设置">
        <a-switch
          v-model:checked="localSettings.validateSSL"
          @change="emitUpdate"
        />
        <span class="setting-label">验证SSL证书</span>
        <div class="form-help-text">关闭此选项可以请求自签名证书的服务器</div>
      </a-form-item>

      <a-form-item label="响应大小限制">
        <a-input-number
          v-model:value="localSettings.maxResponseSize"
          :min="1"
          :max="100"
          :step="1"
          addon-after="MB"
          style="width: 150px"
          @change="emitUpdate"
        />
        <div class="form-help-text">超过此大小的响应将被截断</div>
      </a-form-item>

      <a-form-item label="缓存">
        <a-switch
          v-model:checked="localSettings.enableCache"
          @change="emitUpdate"
        />
        <span class="setting-label">启用请求缓存</span>

        <div v-if="localSettings.enableCache" style="margin-top: 8px">
          <a-input-number
            v-model:value="localSettings.cacheTime"
            :min="1"
            :max="60"
            :step="1"
            addon-after="分钟"
            style="width: 150px"
            @change="emitUpdate"
          />
          <span class="setting-label">缓存有效期</span>
          <div class="form-help-text">在有效期内的相同请求将返回缓存的响应</div>
        </div>
      </a-form-item>

      <a-form-item label="请求ID">
        <a-switch
          v-model:checked="localSettings.includeRequestId"
          @change="emitUpdate"
        />
        <span class="setting-label">包含请求ID</span>
        <div class="form-help-text">自动添加 X-Request-ID 请求头用于跟踪</div>
      </a-form-item>

      <a-divider>高级设置</a-divider>

      <a-form-item label="重试设置">
        <a-switch
          v-model:checked="localSettings.enableRetry"
          @change="emitUpdate"
        />
        <span class="setting-label">启用自动重试</span>

        <div v-if="localSettings.enableRetry" style="margin-top: 16px">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="最大重试次数">
                <a-input-number
                  v-model:value="localSettings.retryCount"
                  :min="1"
                  :max="5"
                  :step="1"
                  style="width: 100%"
                  @change="emitUpdate"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="重试延迟 (毫秒)">
                <a-input-number
                  v-model:value="localSettings.retryDelay"
                  :min="100"
                  :max="10000"
                  :step="100"
                  style="width: 100%"
                  @change="emitUpdate"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-form-item label="重试条件">
            <a-checkbox-group v-model:value="localSettings.retryConditions" @change="emitUpdate">
              <a-checkbox value="network">网络错误</a-checkbox>
              <a-checkbox value="5xx">服务器错误 (5xx)</a-checkbox>
              <a-checkbox value="timeout">请求超时</a-checkbox>
            </a-checkbox-group>
          </a-form-item>
        </div>
      </a-form-item>

      <a-form-item label="代理设置">
        <a-switch
          v-model:checked="localSettings.enableProxy"
          @change="emitUpdate"
        />
        <span class="setting-label">使用代理服务器</span>

        <div v-if="localSettings.enableProxy" style="margin-top: 16px">
          <a-row :gutter="16">
            <a-col :span="16">
              <a-form-item label="代理服务器地址">
                <a-input
                  v-model:value="localSettings.proxyHost"
                  placeholder="例如: http://proxy.example.com"
                  @change="emitUpdate"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="代理端口">
                <a-input-number
                  v-model:value="localSettings.proxyPort"
                  :min="1"
                  :max="65535"
                  style="width: 100%"
                  @change="emitUpdate"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="代理用户名 (可选)">
                <a-input
                  v-model:value="localSettings.proxyUsername"
                  placeholder="代理用户名"
                  @change="emitUpdate"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="代理密码 (可选)">
                <a-input-password
                  v-model:value="localSettings.proxyPassword"
                  placeholder="代理密码"
                  @change="emitUpdate"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import { reactive, defineProps, defineEmits, watch } from 'vue';

const props = defineProps({
  settings: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:settings']);

// 默认设置
const defaultSettings = {
  // timeout: 30000,
  // followRedirects: true,
  // maxRedirects: 5,
  // validateSSL: true,
  // maxResponseSize: 10,
  // enableCache: false,
  // cacheTime: 5,
  // includeRequestId: true,
  // enableRetry: false,
  // retryCount: 3,
  // retryDelay: 1000,
  // retryConditions: ['network', '5xx'],
  // enableProxy: false,
  // proxyHost: '',
  // proxyPort: 8080,
  // proxyUsername: '',
  // proxyPassword: ''
};

// 本地设置
const localSettings = reactive({
  ...defaultSettings,
  ...props.settings
});

// 监听props变化
watch(() => props.settings, (newSettings) => {
  Object.assign(localSettings, {
    ...defaultSettings,
    ...newSettings
  });
}, { deep: true });

// 发送更新
const emitUpdate = () => {
  emit('update:settings', { ...localSettings });
};
</script>

<style scoped>
.settings-editor {
  padding: 16px 0;
}

.setting-label {
  margin-left: 8px;
  color: #333;
}

.form-help-text {
  margin-top: 4px;
  font-size: 12px;
  color: #666;
}
</style>