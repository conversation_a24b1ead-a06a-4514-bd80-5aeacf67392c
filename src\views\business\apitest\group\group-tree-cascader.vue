<template>
  <a-cascader
    v-model:value="groupIds"
    :options="groupTree"
    :field-names="{ label: 'name', value: 'groupId', children: 'children' }"
    placeholder="请选择所属模块"
    :show-search="true"
    allow-clear
    @change="change"
  />
</template>

<script setup>
  import {defineProps, onBeforeMount, onMounted, ref, watch, watchEffect} from 'vue';
  import { message } from 'ant-design-vue';
  import { GroupApi } from '/@/api/business/apitest/group-api.js';

  const props = defineProps({
    groupId: {
      type: [Number,null],
      required: true,
    },
  });

  const emits = defineEmits(['update:groupId']);

  const groupIds = ref([]);
  const groupTree = ref([]);

  const getGroupTree = async () => {
    try {
      const res = await GroupApi.getGroupTree();
      groupTree.value = res.data;
    } catch (e) {
      message.error('获取接口数失败');
    }
  };

  const setGroupIds = (groupId) => {
    const findPath = (node, targetId, path = []) => {
      path.push(node.groupId);

      if (node.groupId === targetId) {
        return [...path]; // 找到目标，返回当前路径副本
      }

      if (node.children && node.children.length > 0) {
        for (let child of node.children) {
          const result = findPath(child, targetId, [...path]); // 传递路径副本
          if (result) return result; // 找到就返回
        }
      }

      return null; // 没找到
    };

    for (let tree of groupTree.value) {
      const path = findPath(tree, groupId);
      if (path) return path;
    }

    return []; // 没有找到匹配项
  };

  const change = (value) => {
    emits('update:groupId', value[value.length - 1]);
  };

  onMounted(async () => {
    await getGroupTree();
  });

  watch(
      [groupTree, () => props.groupId],
      ([tree, groupId]) => {
        if (tree.length > 0 && groupId) {
          groupIds.value = setGroupIds(groupId);
        }
      },{ immediate: true }
  );
</script>