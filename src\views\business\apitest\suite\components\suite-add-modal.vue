!-- 创建/编辑集合对话框 -->
<template>
  <a-modal
  :title="isEdit ? '编辑集合' : '创建集合'"
  :open="open"
  @cancel="open = false"
  @ok="open = false"
  :okButtonProps="{ props: { disabled: !suiteForm.suiteName } }"
  >
    <a-form :model="suiteForm" layout="vertical">
      <a-form-item
        label="集合名称"
        name="suiteName"
        :rules="[{ required: true, message: '请输入集合名称' }]"
      >
        <a-input
          v-model:value="suiteForm.suiteName"
          placeholder="请输入集合名称"
        >
          <template #prefix>
            <folder-outlined />
          </template>
        </a-input>
      </a-form-item>
      <a-form-item label="描述" name="desc">
        <a-textarea
          v-model:value="suiteForm.desc"
          placeholder="请输入集合描述"
          :rows="4"
        />
      </a-form-item>
      <a-form-item label="状态" name="status">
        <a-radio-group v-model:value="suiteForm.status" button-style="solid">
          <a-radio-button :value="1">
            <check-circle-outlined /> 启用
          </a-radio-button>
          <a-radio-button :value="0">
            <stop-outlined /> 禁用
          </a-radio-button>
        </a-radio-group>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import {CheckCircleOutlined, FolderOutlined, StopOutlined} from "@ant-design/icons-vue";
import {computed, reactive, ref} from "vue";

const emits = defineEmits(['loadQuery']);
const isEdit = computed(()=> !!suiteForm.suiteId);
const open = ref(false);
const suiteForm = reactive({
  suiteId: undefined,
  suiteName: '',
  desc: '',
  status: 1
})

const show = (suiteData) =>{
  if (suiteData){
    Object.assign(suiteForm, suiteData)
  }
  open.value = true;
}
defineExpose( {
  show
})
</script>