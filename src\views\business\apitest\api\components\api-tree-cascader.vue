<template>
  <a-cascader
    v-model:value="apiIds"
    :options="apiTree"
    :field-names="{ label: 'name', value: 'id', children: 'children' }"
    placeholder="请选择关联接口"
    :show-search="true"
    allow-clear
    @change="change"
  />
</template>

<script setup>

import {defineProps, onMounted, ref, watch} from 'vue'
import {Api} from "/src/api/business/apitest/api.js";
import { message } from 'ant-design-vue';
const props = defineProps({
  apiId: {
    type: String,
    required: true,
  },
})
const emits = defineEmits(['update:apiId'])
const apiTree = ref([])
const apiIds = ref([])

const getApiTree = async () =>{
  try{
    const res = await Api.queryApiTree()
    apiTree.value = res.data
  }catch (e){

    message.error("获取接口数失败")
  }

}

const setApiIds = (apiId) => {
    const findPath = (node, targetId, path = []) => {
      path.push(node.id);

      if (node.id === targetId) {
        return [...path]; // 找到目标，返回当前路径副本
      }

      if (node.children && node.children.length > 0) {
        for (let child of node.children) {
          const result = findPath(child, targetId, [...path]); // 传递路径副本
          if (result) return result; // 找到就返回
        }
      }

      return null; // 没找到
    };

    for (let tree of apiTree.value) {
      const path = findPath(tree, apiId);
      if (path) return path;
    }
    return []; // 没有找到匹配项
  };
const change = (value) =>{
  const lastId = value[value.length-1]
  emits('update:apiId', value[value.length-1].split("-")[1])
}

onMounted(async ()=>{

  await getApiTree()
  apiIds.value = setApiIds("api-"+props.apiId)
})

</script>