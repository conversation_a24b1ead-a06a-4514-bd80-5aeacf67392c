<template>
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="用例集合名称" class="smart-query-form-item">
        <a-input style="width: 300px" v-model:value="queryForm.suiteName" placeholder="产品线" />
      </a-form-item>
      <a-form-item label="状态" style="width: 150px" class="smart-query-form-item">
        <a-select v-model:value="queryForm.status">
          <a-select-option :value=1>启用</a-select-option>
          <a-select-option :value=0>禁用</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item class="smart-query-form-item smart-margin-left10">
        <a-button-group>
          <a-button type="primary" @click="onSearch">
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </a-button>
          <a-button @click="resetQuery">
            <template #icon>
              <ReloadOutlined />
            </template>
            重置
          </a-button>
        </a-button-group>
      </a-form-item>
    </a-row>
  </a-form>

  <a-card size="small" :bordered="false" :hoverable="true">
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="editSuiteData" type="primary">
          <template #icon>
            <PlusOutlined />
          </template>
          新建集合
        </a-button>
        <a-button @click="exportExcel()" type="primary">
          <template #icon>
            <FileExcelOutlined />
          </template>
          导入
        </a-button>
      </div>
    </a-row>

    <a-table
      :row-selection="rowSelection"
      :scroll="{ x: 1300 }"
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="id"
      :pagination="false"
      :loading="tableLoading"
      bordered
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button @click="editSuiteData(record)" size="small" type="link">编辑</a-button>
            <a-button @click="chooseCase(record.suiteId)" size="small" type="link">查看用例</a-button>
            <a-button @click="confirmDelete(record.suiteId)" size="small" danger type="link">删除</a-button>
          </div>
        </template>
      </template>
    </a-table>

    <suite-add-modal ref="suiteAddRef" @load-query="ajaxQuery"/>
    <case-tree-modal ref="caseTreeRef"/>
    <case-suite-table ref="caseSuiteTableRef" />

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="ajaxQuery"
        @showSizeChange="ajaxQuery"
        :show-total="(total) => `共${total}条`"
      />
    </div>
  </a-card>
</template>
<script setup>
import {reactive, ref, onMounted, computed, h} from 'vue';
import {message, Modal, Tag} from 'ant-design-vue';
import { PAGE_SIZE, PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
import { useRouter } from 'vue-router';
import { smartSentry } from '/@/lib/smart-sentry';
import {CaseApi} from "/@/api/business/apitest/case-api.js";
import {SuiteApi} from "/@/api/business/apitest/suite-api.js";
import SuiteAddModal from "/@/views/business/apitest/suite/components/suite-add-modal.vue";
import CaseTreeModal from "/@/views/business/apitest/case/components/case-tree-modal.vue";
import CaseSuiteTable from "/@/views/business/apitest/suite/components/case-suite-table.vue";

  // --------------------------- 企业表格 列 ---------------------------

  const columns = ref([
      {
      title: '名称',
      dataIndex: 'suiteName',
      minWidth: 20,
      ellipsis: true,
    },
      {
      title: '描述',
      minWidth: 100,
      dataIndex: 'desc',
      ellipsis: true,
    },
    {
      title: '状态',
      minWidth: 120,
      dataIndex: 'status',
      ellipsis: true,
      customRender: ({text, record}) => {
        const options = {
          1: '启用',
          0: '禁用',
          2: '草稿'
        }
        return options[text]
      },
    },
    {
      title: '创建用户',
      minWidth: 60,
      dataIndex: 'createUserId'
    },
    {
      title: '更新用户',
      dataIndex: 'updateUserId',
      minWidth: 20,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      minWidth: 100,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      minWidth: 100,
    },
    {
      title: '操作',
      dataIndex: 'action',
      minWidth: 100,
    },
  ]);

  // --------------------------- 查询 ---------------------------

  const queryFormState = {
    suiteName: '',
    status: undefined,
    pageNum: 1,
    pageSize: PAGE_SIZE,
  };
  const queryForm = reactive({ ...queryFormState });
  const tableLoading = ref(false);
  const tableData = ref([]);
  const total = ref(0);

  const suiteAddRef = ref(null)
  const caseTreeRef = ref(null)
  const caseSuiteTableRef = ref(null)
  function onSearch() {
    queryForm.pageNum = 1;
    ajaxQuery();
  }

  function resetQuery() {
    Object.assign(queryForm, queryFormState);
    ajaxQuery();
  }

  async function ajaxQuery() {
    try {
      tableLoading.value = true;
      let responseModel = await SuiteApi.pageQuery(queryForm);
      const list = responseModel.data.items;
      total.value = responseModel.data.total;
      tableData.value = list;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  // --------------------------- 删除 ---------------------------

  function confirmDelete(ids) {
    Modal.confirm({
      title: '确定要删除吗？',
      content: '删除后，该信息将不可恢复',
      okText: '删除',
      okType: 'danger',
      onOk() {
        deleteSuiteData(ids)
      },
      cancelText: '取消',
      onCancel() {},
    });
  }

  const chooseCase = (suiteId) => {
    caseSuiteTableRef.value.showModal(suiteId)
  }

  // --------------------------- 增加、修改、详情 ---------------------------
  function addSuiteData() {
    caseTreeRef.value.showModal();
  }
  function editSuiteData(record) {
    suiteAddRef.value.show(record);
  }

  function deleteSuiteData(ids){
    SuiteApi.delete(ids).then(()=>{
      message.success('删除成功');
      ajaxQuery();
    })
  }
  const rowSelection = {
    onSelect:(record, selected, selectedRows, nativeEvent)=>{
      console.log(record)
      console.log(selected)
      console.log(selectedRows)
      console.log(nativeEvent)
    }
  }
  onMounted(ajaxQuery);

</script>
