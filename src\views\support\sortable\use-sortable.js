import { onMounted, onBeforeUnmount } from 'vue';
import Sortable from 'sortablejs';

export function useSortable(elementRef, options = {}) {
  let sortableInstance = null;

  onMounted(() => {
    if (elementRef.value) {
      sortableInstance = new Sortable(elementRef.value, options);
    }
  });

  onBeforeUnmount(() => {
    if (sortableInstance) {
      sortableInstance.destroy();
    }
  });

  return { sortableInstance };
}