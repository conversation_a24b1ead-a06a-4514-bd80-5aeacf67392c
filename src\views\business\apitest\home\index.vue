<template>
  <div class="api-test-dashboard">
    <h1>接口自动化测试平台</h1>

    <!-- 顶部统计卡片 -->
    <a-row :gutter="16" class="stat-cards">
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="总接口数"
            :value="dashboardData.totalApis"
            :value-style="{ color: '#3f8600' }"
            prefix-icon="ApiOutlined"
          />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="总用例数"
            :value="dashboardData.totalCases"
            :value-style="{ color: '#1890ff' }"
            prefix-icon="FileOutlined"
          />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="今日执行数"
            :value="dashboardData.todayExecutions"
            :value-style="{ color: '#722ed1' }"
            prefix-icon="PlayCircleOutlined"
          />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="总执行时间(小时)"
            :value="(dashboardData.totalExecutionTime / 3600).toFixed(2)"
            :value-style="{ color: '#cf1322' }"
            prefix-icon="ClockCircleOutlined"
          />
        </a-card>
      </a-col>
    </a-row>

    <!-- 测试执行趋势图 -->
    <execution-trend-chart :trend-data="trendData" />

    <!-- 测试通过率和接口覆盖率 -->
    <pass-rate-coverage-charts
      :pass-rate-data="passRateData"
      :coverage-data="coverageData"
    />

    <!-- 分组测试统计热力图 -->
    <group-heatmap-chart :group-data="groupData" />

    <!-- 失败率最高的接口和最近执行记录 -->
    <failure-execution-tables
      :top-failed-apis="topFailedApis"
      :recent-executions="recentExecutions"
      :scheduled-executions="scheduledExecutions"
    />

    <!-- 接口健康度分析雷达图 -->
    <health-radar-chart :health-data="healthData" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { message } from 'ant-design-vue';

import HealthRadarChart from "/src/views/business/apitest/home/<USER>/health-radar-chart.vue";
import FailureExecutionTables from "/src/views/business/apitest/home/<USER>/failure-execution-tables.vue";
import GroupHeatmapChart from "/src/views/business/apitest/home/<USER>/group-heatmap-chart.vue";
import PassRateCoverageCharts from "/src/views/business/apitest/home/<USER>/pass-rate-coverage-charts.vue";
import ExecutionTrendChart from "/src/views/business/apitest/home/<USER>/execution-trend-chart.vue";
import {DashboardApi} from "/@/api/business/apitest/dashboard-api.js";

// 数据定义
const dashboardData = ref({
  totalApis: 0,
  totalCases: 0,
  todayExecutions: 0,
  totalExecutionTime: 0
});
const trendData = ref([]);
const passRateData = ref({
  passed: 0,
  failed: 0,
  skipped: 0
});
const coverageData = ref({
  totalApis: 0,
  coveredApis: 0,
  coverageRate: 0,
  methodCoverage: []
});
const groupData = ref([]);
const topFailedApis = ref([]);
const recentExecutions = ref([]);
const scheduledExecutions = ref([]);
const healthData = ref([]);

async function getDashboardData() {
  try{
    const response = await DashboardApi.getDashboardData();
    dashboardData.value = response.data;
  }catch (e) {
    message.error('获取仪表盘统计数据失败',e)
  }
}

async function getTrendData() {
  try{
    const response = await DashboardApi.getTrendData();
    trendData.value = response.data;
  }catch (e) {
    message.error('获取测试执行趋势数据失败',e)
  }
}

async function getPassRateData() {
  try{
    const response = await DashboardApi.getPassRateData();
    passRateData.value = response.data;
  }catch (e) {
    message.error('获取测试通过率数据失败',e)
  }
}

async function getCoverageData() {
  try{
    const response = await DashboardApi.getCoverageData();
    coverageData.value = response.data;
  }catch (e) {
    message.error('获取接口覆盖率数据失败',e)
  }
}

async function getGroupData() {
  try{
    const response = await DashboardApi.getGroupData();
    groupData.value = response.data;
  }catch (e) {
    message.error('获取分组统计数据失败',e)
  }
}

async function getTopFailedApis() {
  try{
    const response = await DashboardApi.getTopFailedApis();
    topFailedApis.value = response.data;
  }catch (e) {
    message.error('获取失败率最高的接口失败',e)
  }
}

async function getRecentExecutions() {
  try{
    const response = await DashboardApi.getRecentExecutions();
    recentExecutions.value = response.data;
  }catch (e) {
    message.error('获取最近执行记录失败',e)
  }
}

async function getScheduledExecutions() {
  try{
    const response = await DashboardApi.getScheduledExecutions();
    scheduledExecutions.value = response.data;
  }catch (e) {
    message.error('获取计划执行记录失败',e)
  }
}

async function getHealthData() {
  try{
    const response = await DashboardApi.getHealthData();
    healthData.value = response.data;
  }catch (e) {
    message.error('获取接口健康度数据失败',e)
  }
}

// 加载数据
onMounted(async () => {
  await Promise.all([
      getDashboardData(),
      getCoverageData(),
      getGroupData(),
      getPassRateData(),
      getRecentExecutions(),
      getScheduledExecutions(),
      getTopFailedApis(),
      getTrendData(),
      getHealthData()
  ]);
});
</script>

<style scoped>
.api-test-dashboard {
  padding: 24px;
}
.stat-cards {
  margin-bottom: 24px;
}
</style>