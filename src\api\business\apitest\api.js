import {getRequest, postRequest} from "/@/lib/axios.js";

export const Api = {

    pageQuery(params){
        return getRequest("/api_test/api/page",params)
    },
    queryApiTree(params){
        return getRequest("/api_test/api/tree",params)
    },
    getByGroupId(groupId){
        return getRequest(`/api_test/api/group_id/${groupId}`)
    },
    search(keyword){
        return getRequest(`/api_test/api/search`,{keyword:keyword})
    },
    apiProxy(data){
        return postRequest("/api_test/api/proxy",data)
    },
    get(params){
        return getRequest("/api_test/api",params)
    },
    update(data){
        return postRequest("/api_test/api/update",data)
    },
    add(data){
        return postRequest("/api_test/api/add",data)
    },
    importOpenai(data){
        return postRequest("/api_test/upload/openapi",data)
    }
}