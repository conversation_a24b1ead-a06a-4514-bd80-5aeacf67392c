<!-- components/RequestHeadersEditor.vue -->
<template>
  <div class="headers-editor">
    <div class="editor-header">
      <a-input
        placeholder="过滤请求头..."
        v-model:value="filterText"
        allow-clear
        class="filter-input"
      >
        <template #prefix>
          <search-outlined />
        </template>
      </a-input>

      <div class="header-actions">
        <a-tooltip title="批量编辑">
          <a-button @click="showBulkEdit" size="small">
            <code-outlined />
          </a-button>
        </a-tooltip>
        <a-dropdown>
          <a-button size="small">
            <template #icon><down-outlined /></template>
            添加常用头
          </a-button>
          <template #overlay>
            <a-menu @click="addCommonHeader">
              <a-menu-item key="accept">Accept</a-menu-item>
              <a-menu-item key="accept-encoding">Accept-Encoding</a-menu-item>
              <a-menu-item key="accept-language">Accept-Language</a-menu-item>
              <a-menu-item key="cache-control">Cache-Control</a-menu-item>
              <a-menu-item key="content-type">Content-Type</a-menu-item>
              <a-menu-item key="user-agent">User-Agent</a-menu-item>
              <a-menu-item key="authorization">Authorization</a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </div>

    <div class="content-type-selector">
      <span class="selector-label">Content-Type:</span>
      <a-select
        v-model:value="localContentType"
        style="width: 300px"
        @change="updateContentType"
      >
        <a-select-option :value="null">None</a-select-option>
        <a-select-option value="application/json">application/json</a-select-option>
        <a-select-option value="application/x-www-form-urlencoded">application/x-www-form-urlencoded</a-select-option>
        <a-select-option value="multipart/form-data">multipart/form-data</a-select-option>
        <a-select-option value="text/plain">text/plain</a-select-option>
        <a-select-option value="application/xml">application/xml</a-select-option>
      </a-select>
    </div>

    <a-table
      :dataSource="filteredHeaders"
      :columns="columns"
      :pagination="false"
      size="small"
      bordered
      :scroll="{ y: 300 }"
      rowKey="id"
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.dataIndex === 'enabled'">
          <a-checkbox v-model:checked="record.enabled" @change="emitUpdate" />
        </template>

        <template v-else-if="column.dataIndex === 'name'">
          <a-input
            v-model:value="record.name"
            placeholder="请求头名称"
            @change="emitUpdate"
          />
        </template>

        <template v-else-if="column.dataIndex === 'value'">
          <a-input
            v-model:value="record.value"
            placeholder="请求头值"
            @change="emitUpdate"
          />
        </template>

        <template v-else-if="column.dataIndex === 'description'">
          <a-input
            v-model:value="record.description"
            placeholder="描述"
            @change="emitUpdate"
          />
        </template>

        <template v-else-if="column.dataIndex === 'action'">
          <a-space>
            <a-tooltip title="复制">
              <a-button type="text" size="small" @click="copyHeader(record)">
                <copy-outlined />
              </a-button>
            </a-tooltip>
            <a-tooltip title="删除">
              <a-button type="text" danger size="small" @click="removeHeader(index)">
                <delete-outlined />
              </a-button>
            </a-tooltip>
          </a-space>
        </template>
      </template>
    </a-table>

    <div class="editor-footer">
      <a-button type="dashed" block @click="addHeader">
        <plus-outlined /> 添加请求头
      </a-button>
    </div>

    <!-- 批量编辑模态框 -->
    <a-modal
      v-model:visible="bulkEditVisible"
      title="批量编辑请求头"
      @ok="handleBulkEditOk"
      width="600px"
    >
      <a-alert
        message="每行一个请求头，格式为: 请求头名称: 请求头值"
        type="info"
        show-icon
        style="margin-bottom: 16px"
      />
      <a-textarea
        v-model:value="bulkEditText"
        :rows="12"
        placeholder="Content-Type: application/json
Authorization: Bearer token
Accept: application/json"
      />
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits, watch } from 'vue';
import { message } from 'ant-design-vue';
import { v4 as uuidv4 } from 'uuid';
import {
  SearchOutlined,
  PlusOutlined,
  DeleteOutlined,
  CopyOutlined,
  CodeOutlined,
  DownOutlined
} from '@ant-design/icons-vue';

const props = defineProps({
  headers: {
    type: Array,
    default: () => []
  },
  contentType: {
    type: String,
    default: 'application/json'
  }
});

const emit = defineEmits(['update:headers', 'update:content-type']);

// 为每个请求头添加唯一ID
const ensureIds = (headers) => {
  if (!headers) return
  return headers.map(header => ({
    ...header,
    id: header.id || uuidv4(),
    enabled: header.enabled !== false
  }));
};

// 本地状态
const localHeaders = props.headers ? ref(ensureIds(props.headers)) : ref([]);
const localContentType = ref(props.contentType);
const filterText = ref('');
const bulkEditVisible = ref(false);
const bulkEditText = ref('');

// 监听props变化
watch(() => props.headers, (newHeaders) => {
  localHeaders.value = ensureIds(newHeaders);
}, { deep: true });

watch(() => props.contentType, (newContentType) => {
  localContentType.value = newContentType;
});

// 过滤后的请求头
const filteredHeaders = computed(() => {
  if (!filterText.value) return localHeaders.value;

  const search = filterText.value.toLowerCase();
  return localHeaders.value.filter(header =>
    (header.name && header.name.toLowerCase().includes(search)) ||
    (header.value && header.value.toLowerCase().includes(search)) ||
    (header.description && header.description.toLowerCase().includes(search))
  );
});

// 表格列定义
const columns = [
  { title: '', dataIndex: 'enabled', width: 50, fixed: 'left' },
  { title: '名称', dataIndex: 'name', width: 150 },
  { title: '值', dataIndex: 'value', width: 200 },
  { title: '描述', dataIndex: 'description' },
  { title: '操作', dataIndex: 'action', width: 100, fixed: 'right' }
];

// 添加请求头
const addHeader = () => {
  localHeaders.value.push({
    id: uuidv4(),
    name: '',
    value: '',
    description: '',
    enabled: true
  });
  emitUpdate();
};

// 删除请求头
const removeHeader = (index) => {
  localHeaders.value.splice(index, 1);
  emitUpdate();
};

// 复制请求头
const copyHeader = (record) => {
  localHeaders.value.push({
    ...record,
    id: uuidv4()
  });
  emitUpdate();
  message.success('请求头已复制');
};

// 触发更新
const emitUpdate = () => {
  emit('update:headers', localHeaders.value);
};

// 更新Content-Type
const updateContentType = (value) => {
  emit('update:content-type', value);

  // // 更新或添加Content-Type请求头
  // const contentTypeHeader = localHeaders.value.find(h =>
  //   h.name.toLowerCase() === 'content-type'
  // );
  //
  // if (contentTypeHeader) {
  //   contentTypeHeader.value = value;
  // } else {
  //   localHeaders.value.push({
  //     id: uuidv4(),
  //     name: 'Content-Type',
  //     value: value,
  //     description: '指定请求体的MIME类型',
  //     enabled: true
  //   });
  // }
  //
  // emitUpdate();
};

// 显示批量编辑
const showBulkEdit = () => {
  bulkEditText.value = localHeaders.value
  .filter(header => header.name && header.enabled)
  .map(header => `${header.name}: ${header.value || ''}`)
  .join('\n');
  bulkEditVisible.value = true;
};

// 处理批量编辑确认
const handleBulkEditOk = () => {
  try {
    const newHeaders = bulkEditText.value
      .split('\n')
      .filter(line => line.trim())
      .map(line => {
        const colonIndex = line.indexOf(':');
        if (colonIndex === -1) throw new Error(`格式错误: ${line}`);

        const name = line.substring(0, colonIndex).trim();
        const value = line.substring(colonIndex + 1).trim();

        return {
          id: uuidv4(),
          name,
          value,
          description: '',
          enabled: true
        };
      });

    // 保留原有未包含在批量编辑中的请求头，但标记为禁用
    const existingNames = new Set(newHeaders.map(h => h.name.toLowerCase()));
    const keptHeaders = localHeaders.value
      .filter(h => !existingNames.has(h.name.toLowerCase()))
      .map(h => ({ ...h, enabled: false }));

    localHeaders.value = [...newHeaders, ...keptHeaders];
    emitUpdate();
    bulkEditVisible.value = false;
    message.success('请求头已更新');
  } catch (error) {
    message.error('请求头格式错误，请检查');
  }
};

// 添加常用请求头
const addCommonHeader = (e) => {
  const key = e.key;
  const commonHeaders = {
    'accept': {
      name: 'Accept',
      value: 'application/json, text/plain, */*',
      description: '指定客户端能够接收的内容类型'
    },
    'accept-encoding': {
      name: 'Accept-Encoding',
      value: 'gzip, deflate, br',
      description: '指定客户端能够接收的内容编码方式'
    },
    'accept-language': {
      name: 'Accept-Language',
      value: 'zh-CN,zh;q=0.9,en;q=0.8',
      description: '指定客户端能够接收的语言'
    },
    'cache-control': {
      name: 'Cache-Control',
      value: 'no-cache',
      description: '指定请求和响应的缓存机制'
    },
    'content-type': {
      name: 'Content-Type',
      value: localContentType.value,
      description: '指定请求体的MIME类型'
    },
    'user-agent': {
      name: 'User-Agent',
      value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      description: '浏览器标识'
    },
    'authorization': {
      name: 'Authorization',
      value: 'Bearer {{token}}',
      description: '授权信息'
    }
  };

  if (commonHeaders[key]) {
    // 检查是否已存在
    const existingIndex = localHeaders.value.findIndex(
      h => h.name.toLowerCase() === commonHeaders[key].name.toLowerCase()
    );

    if (existingIndex >= 0) {
      // 更新已存在的请求头
      localHeaders.value[existingIndex] = {
        ...localHeaders.value[existingIndex],
        ...commonHeaders[key],
        enabled: true
      };
    } else {
      // 添加新请求头
      localHeaders.value.push({
        id: uuidv4(),
        ...commonHeaders[key],
        enabled: true
      });
    }

    emitUpdate();
    message.success(`添加请求头: ${commonHeaders[key].name}`);
  }
};
</script>

<style scoped>
.headers-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.filter-input {
  width: 250px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.content-type-selector {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.selector-label {
  margin-right: 12px;
  font-weight: 500;
}

.editor-footer {
  margin-top: 16px;
}
</style>