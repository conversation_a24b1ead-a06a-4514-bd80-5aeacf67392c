<template>
  <div class="code-editor-container" :style="{ height: `${height}px` }">
    <textarea
      v-if="!hasMonaco"
      ref="textareaRef"
      :value="modelValue"
      @input="handleInput"
      class="code-editor-textarea"
      :placeholder="placeholder"
      :readonly="readOnly"
    ></textarea>
    <div v-else ref="monacoRef" class="monaco-editor-container"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  language: {
    type: String,
    default: 'javascript'
  },
  height: {
    type: Number,
    default: 300
  },
  readOnly: {
    type: Boolean,
    default: false
  },
  placeholder: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:modelValue']);

const monacoRef = ref(null);
const textareaRef = ref(null);
const hasMonaco = ref(false);
let editor = null;

const handleInput = (e) => {
  emit('update:modelValue', e.target.value);
};

// 简单的基于textarea的回退实现
watch(() => props.modelValue, (newValue) => {
  if (editor) {
    const currentValue = editor.getValue();
    if (newValue !== currentValue) {
      editor.setValue(newValue);
    }
  }
});

onMounted(async () => {
  try {
    // 尝试动态加载Monaco编辑器
    // 注意：需要安装monaco-editor
    const monaco = await import('monaco-editor');

    hasMonaco.value = true;

    // 需要等待DOM更新完成
    setTimeout(() => {
      if (monacoRef.value) {
        editor = monaco.editor.create(monacoRef.value, {
          value: props.modelValue,
          language: props.language,
          theme: 'vs-dark',
          automaticLayout: true,
          minimap: { enabled: false },
          readOnly: props.readOnly,
          fontSize: 14,
          scrollBeyondLastLine: false,
          contextmenu: true
        });

        editor.onDidChangeModelContent(() => {
          const value = editor.getValue();
          emit('update:modelValue', value);
        });
      }
    }, 0);
  } catch (e) {
    console.error('Monaco编辑器加载失败，使用textarea回退:', e);
    hasMonaco.value = false;
  }
});

onBeforeUnmount(() => {
  if (editor) {
    editor.dispose();
    editor = null;
  }
});
</script>

<style scoped>
.code-editor-container {
  width: 100%;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  overflow: hidden;
}

.monaco-editor-container {
  width: 100%;
  height: 100%;
}

.code-editor-textarea {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  resize: none;
  padding: 8px;
  font-family: monospace;
  font-size: 14px;
  line-height: 1.5;
  background-color: #1e1e1e;
  color: #d4d4d4;
}
</style>