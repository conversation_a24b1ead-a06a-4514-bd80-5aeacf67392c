<template>
  <a-card title="分组测试统计" :bordered="false">
    <div ref="groupHeatmapChart" style="height: 400px"></div>
  </a-card>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  groupData: {
    type: Array,
    required: true
  }
});

const groupHeatmapChart = ref(null);
let chart = null;

const initChart = () => {
  if (!groupHeatmapChart.value) return;

  chart = echarts.init(groupHeatmapChart.value);

  // 准备数据
  const groupNames = props.groupData.map(item => item.name);
  const apiCountData = props.groupData.map(item => [0, item.index, item.apiCount]);
  const caseCountData = props.groupData.map(item => [1, item.index, item.caseCount]);
  const coverageData = props.groupData.map(item => [2, item.index, item.coverageRate * 100]);
  const passRateData = props.groupData.map(item => [3, item.index, item.passRate * 100]);

  const option = {
    tooltip: {
      position: 'top',
      formatter: function (params) {
        const metrics = ['接口数', '用例数', '覆盖率(%)', '通过率(%)'];
        const metricName = metrics[params.data[0]];
        const groupName = groupNames[params.data[1]];
        const value = params.data[2];
        return `${groupName}<br>${metricName}: ${value}`;
      }
    },
    grid: {
      height: '50%',
      top: '10%'
    },
    xAxis: {
      type: 'category',
      data: ['接口数', '用例数', '覆盖率(%)', '通过率(%)'],
      splitArea: {
        show: true
      }
    },
    yAxis: {
      type: 'category',
      data: groupNames,
      splitArea: {
        show: true
      }
    },
    visualMap: {
      min: 0,
      max: 100,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '15%'
    },
    series: [{
      name: '分组统计',
      type: 'heatmap',
      data: [...apiCountData, ...caseCountData, ...coverageData, ...passRateData],
      label: {
        show: true
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  };

  chart.setOption(option);
};

watch(() => props.groupData, () => {
  if (chart) {
    initChart();
  }
}, { deep: true });

onMounted(() => {
  initChart();
  window.addEventListener('resize', () => {
    chart?.resize();
  });
});
</script>