.login-container {
  display: flex;
  height: 100vh;
  width: 100%;
  overflow: hidden;
}
/* 左侧背景区域 */
.login-background {
  flex: 1;
  background-image: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-background::before {
  content: '';
  position: absolute;
  top: -10%;
  left: -10%;
  width: 120%;
  height: 120%;
  background-image: url('/src/assets/pattern.svg');
  background-size: cover;
  opacity: 0.1;
  animation: float 20s infinite linear;
}

@keyframes float {
  0% {
    transform: rotate(0deg) scale(1);
  }
  50% {
    transform: rotate(5deg) scale(1.1);
  }
  100% {
    transform: rotate(0deg) scale(1);
  }
}

.background-overlay {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 2rem;
  color: white;
}

.brand-container {
  text-align: center;
  margin-bottom: 3rem;
}

.logo-container {
  width: 80px;
  height: 80px;
  margin: 0 auto 1rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.logo {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
}

.brand-name {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(to right, #fff, #e0e0e0);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.brand-slogan {
  font-size: 1.2rem;
  opacity: 0.8;
  margin-top: 0.5rem;
}

.feature-list {
  width: 100%;
  max-width: 400px;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.feature-item:nth-child(1) {
  animation-delay: 0.2s;
}

.feature-item:nth-child(2) {
  animation-delay: 0.4s;
}

.feature-item:nth-child(3) {
  animation-delay: 0.6s;
}

.feature-item .anticon {
  margin-right: 12px;
  font-size: 1.2rem;
  color: #4ade80;
}
/* 右侧登录表单区域 - 调整宽度 */
.login-form-container {
  width: 550px; /* 从 480px 增加到 550px */
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: -4px 0 24px rgba(0, 0, 0, 0.05);
}

.login-form-wrapper {
  width: 100%;
  max-width: 420px; /* 从 360px 增加到 420px */
  padding: 2rem;
}

.welcome-text {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #333;
}

.login-subtitle {
  color: #666;
  margin-bottom: 2rem;
}

.login-form {
  margin-bottom: 1.5rem;
}

.login-form :deep(.ant-input-affix-wrapper) {
  border-radius: 8px;
  padding: 12px 16px;
  border: 1px solid #e8e8e8;
  transition: all 0.3s;
}

.login-form :deep(.ant-input-affix-wrapper:hover),
.login-form :deep(.ant-input-affix-wrapper:focus) {
  border-color: #764ba2;
  box-shadow: 0 0 0 2px rgba(118, 75, 162, 0.1);
}

.login-form :deep(.ant-input) {
  padding: 8px 0;
}

.login-form :deep(.ant-form-item) {
  margin-bottom: 24px;
}

.captcha-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.captcha-input {
  flex: 1;
}

.captcha-image {
  width: 120px;
  height: 40px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s;
}

.captcha-image:hover {
  border-color: #764ba2;
}

.captcha-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.demo-info {
  font-size: 12px;
  color: #999;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 6px;
}

.code-btn {
  width: 120px;
  height: 40px;
  border-radius: 8px;
  font-size: 14px;
}
.login-button {
  height: 48px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(118, 75, 162, 0.3);
  transition: all 0.3s;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(118, 75, 162, 0.4);
}

.divider {
  position: relative;
  text-align: center;
  margin: 24px 0;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: #e8e8e8;
}

.divider-text {
  position: relative;
  padding: 0 16px;
  background-color: #fff;
  color: #888;
  font-size: 0.9rem;
}

.social-login {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 24px;
}

.social-button {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e8e8e8;
  transition: all 0.3s;
}

.social-button:hover {
  transform: translateY(-2px);
}

.github {
  color: #24292e;
}

.github:hover {
  background-color: #24292e;
  color: white;
}

.wechat {
  color: #07c160;
}

.wechat:hover {
  background-color: #07c160;
  color: white;
}

.dingtalk {
  color: #1890ff;
}

.dingtalk:hover {
  background-color: #1890ff;
  color: white;
}

.register-link {
  text-align: center;
  color: #666;
}

.register-link a {
  color: #764ba2;
  font-weight: 600;
  cursor: pointer;
}

.register-link a:hover {
  text-decoration: underline;
}

/* 响应式适配 */
@media (max-width: 992px) {
  .login-background {
    display: none;
  }

  .login-form-container {
    width: 100%;
  }
}

@media (max-width: 576px) {
  .login-form-wrapper {
    padding: 1.5rem;
  }

  .welcome-text {
    font-size: 1.8rem;
  }
}

