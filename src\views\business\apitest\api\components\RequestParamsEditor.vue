<!-- components/RequestParamsEditor.vue -->
<template>
  <div class="params-editor">
    <div class="editor-header">
      <a-input
        placeholder="过滤参数..."
        v-model:value="filterText"
        allow-clear
        class="filter-input"
      >
        <template #prefix>
          <search-outlined />
        </template>
      </a-input>

      <div class="header-actions">
        <a-tooltip title="批量编辑">
          <a-button @click="showBulkEdit" size="small">
            <code-outlined />
          </a-button>
        </a-tooltip>
      </div>
    </div>

    <a-table
      :dataSource="filteredParams"
      :columns="columns"
      :pagination="false"
      size="small"
      bordered
      :scroll="{ y: 300 }"
      rowKey="id"
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.dataIndex === 'enabled'">
          <a-checkbox v-model:checked="record.enabled" @change="emitUpdate"/>
        </template>

        <template v-else-if="column.dataIndex === 'name'">
          <a-input
            v-model:value="record.name"
            placeholder="参数名"
            @change="() => emitUpdate()"
          />
        </template>

        <template v-else-if="column.dataIndex === 'value'">
          <a-input
            v-model:value="record.value"
            placeholder="参数值"
            @change="() => emitUpdate()"
          />
        </template>

        <template v-else-if="column.dataIndex === 'description'">
          <a-input
            v-model:value="record.description"
            placeholder="描述"
            @change="() => emitUpdate()"
          />
        </template>

        <template v-else-if="column.dataIndex === 'action'">
          <a-space>
            <a-tooltip title="复制">
              <a-button type="text" size="small" @click="copyParam(record)">
                <copy-outlined />
              </a-button>
            </a-tooltip>
            <a-tooltip title="删除">
              <a-button type="text" danger size="small" @click="removeParam(index)">
                <delete-outlined />
              </a-button>
            </a-tooltip>
          </a-space>
        </template>
      </template>
    </a-table>

    <div class="editor-footer">
      <a-button type="dashed" block @click="addParam">
        <plus-outlined /> 添加参数
      </a-button>
    </div>

    <!-- 批量编辑模态框 -->
    <a-modal
      v-model:visible="bulkEditVisible"
      title="批量编辑参数"
      @ok="handleBulkEditOk"
      width="600px"
    >
      <a-alert
        message="每行一个参数，格式为: 参数名=参数值"
        type="info"
        show-icon
        style="margin-bottom: 16px"
      />
      <a-textarea
        v-model:value="bulkEditText"
        :rows="12"
        placeholder="name=value
token=123456
userId=10001"
      />
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits, watch } from 'vue';
import { message } from 'ant-design-vue';
import {
  SearchOutlined,
  PlusOutlined,
  DeleteOutlined,
  CopyOutlined,
  CodeOutlined
} from '@ant-design/icons-vue';

const props = defineProps({
  params: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:params']);

// 本地状态
const localParams = ref(JSON.parse(JSON.stringify(props.params || [])))
const filterText = ref('');
const bulkEditVisible = ref(false);
const bulkEditText = ref('');

// 监听props变化
watch(() => props.params, (newParams) => {
  localParams.value = JSON.parse(JSON.stringify(newParams || []))
}, { deep: true });

// 过滤后的参数
const filteredParams = computed(() => {
  if (!filterText.value) return localParams.value;

  const search = filterText.value.toLowerCase();
  return localParams.value.filter(param =>
    (param.name && param.name.toLowerCase().includes(search)) ||
    (param.value && param.value.toLowerCase().includes(search)) ||
    (param.description && param.description.toLowerCase().includes(search))
  );
});

// 表格列定义
const columns = [
  { title: '', dataIndex: 'enabled', width: 50, fixed: 'left' },
  { title: '参数名', dataIndex: 'name', width: 150 },
  { title: '参数值', dataIndex: 'value', width: 200 },
  { title: '描述', dataIndex: 'description' },
  { title: '操作', dataIndex: 'action', width: 100, fixed: 'right' }
];

// 添加参数
const addParam = () => {
  localParams.value.push({
    name: '',
    value: '',
    description: '',
    enabled: true
  });
  emitUpdate();
};

// 删除参数
const removeParam = (index) => {
  localParams.value.splice(index, 1);
  emitUpdate();
};

// 复制参数
const copyParam = (record) => {
  localParams.value.push({
    ...record,
  });
  emitUpdate();
  message.success('参数已复制');
};

// 触发更新
const emitUpdate = () => {
  emit('update:params', localParams.value);
};

// 显示批量编辑
const showBulkEdit = () => {
  // 将参数转换为文本格式
  bulkEditText.value = localParams.value
    .filter(param => param.name)
    .map(param => `${param.name}=${param.value || ''}`)
    .join('\n');

  bulkEditVisible.value = true;
};

// 处理批量编辑确认
const handleBulkEditOk = () => {
  try {
    // 解析文本为参数
    const newParams = bulkEditText.value
      .split('\n')
      .filter(line => line.trim())
      .map(line => {
        const [name, ...valueParts] = line.split('=');
        const value = valueParts.join('='); // 处理值中可能包含=的情况

        return {
          name: name.trim(),
          value: value || '',
          description: '',
          enabled: true
        };
      });

    // 更新参数
    localParams.value = newParams;
    emitUpdate();
    bulkEditVisible.value = false;
    message.success('批量参数已更新');
  } catch (error) {
    message.error('参数格式错误，请检查');
  }
};
</script>

<style scoped>
.params-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.filter-input {
  width: 250px;
}

.editor-footer {
  margin-top: 16px;
}

:deep(.ant-table-small) {
  font-size: 12px;
}
</style>