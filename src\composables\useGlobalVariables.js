import { ref, reactive, computed, readonly } from 'vue';
import { message } from 'ant-design-vue';
import { globalVariableApi } from '/@/api/business/apitest/global-variable-api.js';

// 全局变量状态
const globalVariables = ref([]);
const loading = ref(false);
const error = ref(null);

/**
 * 全局变量管理 Composable
 * 提供统一的全局变量管理功能
 */
export function useGlobalVariables() {
  // 获取全局变量列表
  const fetchGlobalVariables = async () => {
    try {
      loading.value = true;
      error.value = null;
      const response = await globalVariableApi.get();
      globalVariables.value = response.data || [];
      return globalVariables.value;
    } catch (err) {
      error.value = err.message || '获取全局变量失败';
      message.error(error.value);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  // 添加全局变量
  const addGlobalVariable = async (variable) => {
    try {
      // 验证必填字段
      if (!variable.variableName || !variable.variableName.trim()) {
        message.error('变量名不能为空');
        return false;
      }

      if (!variable.variableValue || !variable.variableValue.trim()) {
        message.error('变量值不能为空');
        return false;
      }

      // 检查是否已存在同名变量
      const existing = globalVariables.value.find(v => v.variableName === variable.variableName);
      if (existing) {
        message.error('变量名已存在');
        return false;
      }

      const newVariable = {
        variableName: variable.variableName.trim(),
        variableValue: variable.variableValue.trim(),
        category: variable.category || '',
        description: variable.description || '',
        status: variable.status !== false
      };

      await globalVariableApi.add(newVariable);
      globalVariables.value.push(newVariable);
      message.success('全局变量已添加');
      return true;
    } catch (err) {
      message.error('添加全局变量失败');
      return false;
    }
  };

  // 更新全局变量
  const updateGlobalVariable = async (variable) => {
    try {
      if (!variable.variableId) {
        message.error('变量ID不能为空');
        return false;
      }

      if (!variable.variableName || !variable.variableName.trim()) {
        message.error('变量名不能为空');
        return false;
      }

      if (!variable.variableValue || !variable.variableValue.trim()) {
        message.error('变量值不能为空');
        return false;
      }

      await globalVariableApi.update(variable);
      
      // 更新本地状态
      const index = globalVariables.value.findIndex(v => v.variableId === variable.variableId);
      if (index >= 0) {
        globalVariables.value[index] = { ...variable };
      }
      
      message.success('全局变量已更新');
      return true;
    } catch (err) {
      message.error('更新全局变量失败');
      return false;
    }
  };

  // 删除全局变量
  const deleteGlobalVariable = async (variableId) => {
    try {
      await globalVariableApi.delete([variableId]);
      
      // 更新本地状态
      const index = globalVariables.value.findIndex(v => v.variableId === variableId);
      if (index >= 0) {
        globalVariables.value.splice(index, 1);
      }
      
      message.success('全局变量已删除');
      return true;
    } catch (err) {
      message.error('删除全局变量失败');
      return false;
    }
  };

  // 批量删除全局变量
  const deleteGlobalVariables = async (variableIds) => {
    try {
      await globalVariableApi.delete(variableIds);
      
      // 更新本地状态
      globalVariables.value = globalVariables.value.filter(
        v => !variableIds.includes(v.variableId)
      );
      
      message.success(`已删除 ${variableIds.length} 个全局变量`);
      return true;
    } catch (err) {
      message.error('批量删除全局变量失败');
      return false;
    }
  };

  // 批量导入全局变量
  const importGlobalVariables = (text) => {
    try {
      const variables = text
        .split('\n')
        .filter(line => line.trim() && line.includes('='))
        .map(line => {
          const [name, ...valueParts] = line.split('=');
          return {
            variableName: name.trim(),
            variableValue: valueParts.join('=').trim(),
            category: '',
            description: '',
            status: true
          };
        });

      if (variables.length === 0) {
        message.error('没有找到有效的变量');
        return [];
      }

      // 过滤掉重复的变量名
      const uniqueVariables = variables.filter(newVar => 
        !globalVariables.value.some(existingVar => 
          existingVar.variableName === newVar.variableName
        )
      );

      if (uniqueVariables.length === 0) {
        message.error('所有变量都已存在');
        return [];
      }

      return uniqueVariables;
    } catch (err) {
      message.error('导入失败，请检查格式');
      return [];
    }
  };

  // 导出全局变量
  const exportGlobalVariables = () => {
    if (globalVariables.value.length === 0) {
      message.error('没有可导出的变量');
      return null;
    }

    const exportData = globalVariables.value.map(v => ({
      variableName: v.variableName,
      variableValue: v.variableValue,
      category: v.category,
      description: v.description
    }));

    return JSON.stringify(exportData, null, 2);
  };

  // 搜索全局变量
  const searchGlobalVariables = (searchText) => {
    if (!searchText || !searchText.trim()) {
      return globalVariables.value;
    }

    const text = searchText.toLowerCase();
    return globalVariables.value.filter(variable => 
      variable.variableName.toLowerCase().includes(text) ||
      variable.variableValue.toLowerCase().includes(text) ||
      (variable.category && variable.category.toLowerCase().includes(text)) ||
      (variable.description && variable.description.toLowerCase().includes(text))
    );
  };

  // 获取全局变量值
  const getGlobalVariable = (name) => {
    const variable = globalVariables.value.find(v => v.variableName === name);
    return variable ? variable.variableValue : null;
  };

  // 按分类分组
  const groupedByCategory = computed(() => {
    const groups = {};
    globalVariables.value.forEach(variable => {
      const category = variable.category || '未分类';
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(variable);
    });
    return groups;
  });

  // 获取所有分类
  const categories = computed(() => {
    const cats = new Set(globalVariables.value.map(v => v.category || '未分类'));
    return Array.from(cats);
  });

  // 统计信息
  const statistics = computed(() => ({
    total: globalVariables.value.length,
    active: globalVariables.value.filter(v => v.status).length,
    inactive: globalVariables.value.filter(v => !v.status).length,
    categories: categories.value.length
  }));

  return {
    // 状态
    globalVariables: readonly(globalVariables),
    loading: readonly(loading),
    error: readonly(error),
    
    // 计算属性
    groupedByCategory,
    categories,
    statistics,
    
    // 方法
    fetchGlobalVariables,
    addGlobalVariable,
    updateGlobalVariable,
    deleteGlobalVariable,
    deleteGlobalVariables,
    importGlobalVariables,
    exportGlobalVariables,
    searchGlobalVariables,
    getGlobalVariable
  };
}
