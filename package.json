{"name": "smartadmin", "version": "3.0.0", "author": {"name": "1024创新实验室（1024lab）", "email": "<EMAIL>", "url": "https://www.1024lab.net"}, "license": "MIT", "homepage": "https://smartadmin.1024lab.net", "scripts": {"localhost": "vite --mode localhost", "dev": "vite", "build:test": "vite build  --base=/admin/ --mode test", "build:pre": "vite build  --mode pre", "build:prod": "vite build  --mode production"}, "type": "module", "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@wangeditor-next/editor": "5.6.34", "@wangeditor-next/editor-for-vue": "5.1.14", "ant-design-vue": "4.2.6", "axios": "^1.10.0", "clipboard": "2.0.11", "crypto-js": "4.1.1", "dayjs": "1.10.5", "decimal.js": "10.3.1", "diff": "5.2.0", "diff2html": "3.4.47", "echarts": "5.4.3", "highlight.js": "11.8.0", "jsoneditor": "^10.2.0", "lodash": "4.17.21", "lunar-javascript": "1.6.12", "mitt": "3.0.1", "monaco-editor": "^0.33.0", "monaco-editor-vue3": "^0.1.10", "nprogress": "0.2.0", "pinia": "2.1.7", "qs": "^6.14.0", "sm-crypto": "0.3.13", "sortablejs": "1.15.0", "ua-parser-js": "1.0.35", "uuid": "^11.1.0", "v-viewer": "~1.6.4", "vite-plugin-monaco-editor": "^1.1.0", "vue": "^3.5.18", "vue-draggable-next": "^2.2.1", "vue-i18n": "9.13.1", "vue-monaco": "^1.2.2", "vue-router": "4.3.2", "vue3-json-viewer": "^2.4.1", "vuedraggable": "^2.24.3"}, "devDependencies": {"@vitejs/plugin-vue": "5.0.4", "@vue/compiler-sfc": "3.4.27", "eslint": "^8.16.0", "eslint-config-prettier": "~9.0.0", "eslint-plugin-prettier": "~5.0.0", "eslint-plugin-vue": "~9.17.0", "less": "~4.2.0", "less-loader": "~11.1.3", "prettier": "~3.0.2", "rimraf": "~5.0.1", "stylelint": "~14.8.5", "stylelint-config-prettier": "~9.0.3", "stylelint-config-standard": "~25.0.0", "stylelint-order": "~5.0.0", "terser": "~5.29.2", "vite": "5.2.12", "vue-eslint-parser": "~9.4.2"}, "engines": {"node": ">=18"}, "repository": {"type": "git", "url": "https://github.com/1024-lab/smart-admin.git"}}