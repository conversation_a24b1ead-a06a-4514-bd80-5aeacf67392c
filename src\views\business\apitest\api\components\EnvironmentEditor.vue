<!-- components/EnvironmentEditor.vue -->
<template>
  <div class="environment-editor">
    <a-form layout="vertical">
      <a-form-item label="环境名称">
        <a-select
          v-model:value="selectedEnvId"
          @change="handleEnvironmentChange"
          placeholder="请选择环境"
          style="width: 100%"
          :loading="loading"
          :dropdownMatchSelectWidth="false"
        >
          <a-select-option v-for="env in environments" :key="env.envId" :value="env.envId">
            <span class="env-option">
              <component :is="getEnvIcon(env.name)" />
              <span>{{ env.name }}</span>
            </span>
          </a-select-option>
        </a-select>
      </a-form-item>
    </a-form>

    <div class="env-variables-header">
      <div class="header-title">环境变量</div>
      <div class="header-actions">
        <a-tooltip title="批量编辑">
          <a-button size="small" @click="showBulkEdit" :disabled="!currentEnvironment">
            <code-outlined />
          </a-button>
        </a-tooltip>
        <a-tooltip title="导入">
          <a-button size="small" @click="showImport" :disabled="!currentEnvironment">
            <import-outlined />
          </a-button>
        </a-tooltip>
        <a-tooltip title="导出">
          <a-button size="small" @click="handleExport" :disabled="!currentEnvironment">
            <export-outlined />
          </a-button>
        </a-tooltip>
      </div>
    </div>

    <a-table
      :dataSource="currentEnvironmentVariables"
      :columns="columns"
      :pagination="false"
      size="small"
      bordered
      :loading="loading"
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.dataIndex === 'variableName'">
          <a-input
            v-model:value="record.variableName"
            placeholder="变量名"
            @blur="handleVariableChange"
          />
        </template>

        <template v-else-if="column.dataIndex === 'variableValue'">
          <a-input
            v-model:value="record.variableValue"
            placeholder="变量值"
            :type="visibleValues[record.variableName] ? 'text' : 'password'"
            @blur="handleVariableChange"
          >
            <template #addonAfter>
              <eye-outlined
                v-if="visibleValues[record.variableName]"
                @click="toggleValueVisibility(record.variableName)"
              />
              <eye-invisible-outlined
                v-else
                @click="toggleValueVisibility(record.variableName)"
              />
            </template>
          </a-input>
        </template>

        <template v-else-if="column.dataIndex === 'action'">
          <a-space>
            <a-tooltip title="删除">
              <a-button type="text" danger size="small" @click="handleRemoveVariable(record.variableName)">
                <delete-outlined />
              </a-button>
            </a-tooltip>
          </a-space>
        </template>
      </template>
    </a-table>

    <div class="editor-footer">
      <a-button type="dashed" block @click="handleAddVariable" :disabled="!currentEnvironment">
        <plus-outlined /> 添加变量
      </a-button>
    </div>

    <!-- 批量编辑模态框 -->
    <a-modal
      v-model:visible="bulkEditVisible"
      title="批量编辑环境变量"
      @ok="handleBulkEditOk"
      width="600px"
    >
      <a-alert
        message="每行一个变量，格式为: 变量名=变量值"
        type="info"
        show-icon
        style="margin-bottom: 16px"
      />
      <a-textarea
        v-model:value="bulkEditText"
        :rows="12"
        placeholder="baseUrl=https://api.example.com
apiVersion=v1
token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
      />
    </a-modal>

    <!-- 导入模态框 -->
    <a-modal
      v-model:visible="importVisible"
      title="导入环境变量"
      @ok="handleImportOk"
      width="600px"
    >
      <a-alert
        message="支持两种格式：1. 每行一个变量(变量名=变量值) 2. JSON格式"
        type="info"
        show-icon
        style="margin-bottom: 16px"
      />
      <a-textarea
        v-model:value="importText"
        :rows="12"
        placeholder="格式1:
baseUrl=https://api.example.com
apiVersion=v1

格式2:
{
  &quot;name&quot;: &quot;开发环境&quot;,
  &quot;variables&quot;: [
    {&quot;name&quot;: &quot;baseUrl&quot;, &quot;value&quot;: &quot;https://api.dev.example.com&quot;},
    {&quot;name&quot;: &quot;apiVersion&quot;, &quot;value&quot;: &quot;v1&quot;}
  ]
}"
      />
    </a-modal>
  </div>
</template>

<script setup>
import {ref, onMounted, watch, computed} from 'vue';
import { message } from 'ant-design-vue';
import {
  PlusOutlined,
  DeleteOutlined,
  CodeOutlined,
  ImportOutlined,
  ExportOutlined,
  EyeOutlined,
  EyeInvisibleOutlined
} from '@ant-design/icons-vue';
import { getEnvIcon } from "/@/api/business/apitest/environment-api.js";
const props = defineProps({
  envData: {
    type: Array,
    default: () => []
  },
  activeEnvKey: {
    type: Number,
    default: null
  }
});

const emit = defineEmits(['update:env', 'update:active-env-key']);

// 本地状态管理
const environments = ref([]);
const currentEnvironment = ref({});
const loading = ref(false);
const selectedEnvId = ref(null);

// 控制值的可见性
const visibleValues = ref({});

// 模态框状态
const bulkEditVisible = ref(false);
const bulkEditText = ref('');
const importVisible = ref(false);
const importText = ref('');

// 计算属性
const environmentOptions = computed(() =>
  environments.value.map(env => ({
    label: env.name,
    value: env.envId,
    env
  }))
);

const currentEnvironmentId = computed(() =>
  currentEnvironment.value ? currentEnvironment.value.envId : null
);

const currentEnvironmentVariables = computed(() =>
  currentEnvironment.value ? (currentEnvironment.value.variables || []) : []
);

// 表格列定义
const columns = [
  { title: '变量名', dataIndex: 'variableName', width: '40%' },
  { title: '变量值', dataIndex: 'variableValue', width: '50%' },
  { title: '操作', dataIndex: 'action', width: '10%' }
];

// 处理环境切换
const handleEnvironmentChange = (envId) => {
  const env = environments.value.find(e => e.envId === envId);
  if (env) {
    currentEnvironment.value = env;
    selectedEnvId.value = envId;
    emit('update:active-env-key', envId);
    emit('update:env', env);
  }
};

// 处理变量变化
const handleVariableChange = () => {
  if (currentEnvironment.value) {
    emit('update:env', currentEnvironment.value);
  }
};

// 添加变量
const handleAddVariable = () => {
  if (!currentEnvironment.value.variables) {
    currentEnvironment.value.variables = [];
  }
  currentEnvironment.value.variables.push({ variableName: '', variableValue: '' });
};

// 删除变量
const handleRemoveVariable = (name) => {
  if (currentEnvironment.value.variables) {
    const index = currentEnvironment.value.variables.findIndex(v => v.variableName === name);
    if (index >= 0) {
      currentEnvironment.value.variables.splice(index, 1);
      handleVariableChange();
    }
  }
};

// 切换值的可见性
const toggleValueVisibility = (key) => {
  visibleValues.value[key] = !visibleValues.value[key];
};

// 显示批量编辑
const showBulkEdit = () => {
  if (!currentEnvironment.value) {
    message.error('请先选择环境');
    return;
  }

  bulkEditText.value = currentEnvironmentVariables.value
    .filter(variable => variable.variableName)
    .map(variable => `${variable.variableName}=${variable.variableValue || ''}`)
    .join('\n');

  bulkEditVisible.value = true;
};

// 处理批量编辑确认
const handleBulkEditOk = () => {
  try {
    const variables = bulkEditText.value
      .split('\n')
      .filter(line => line.trim() && line.includes('='))
      .map(line => {
        const [name, ...valueParts] = line.split('=');
        return {
          name: name.trim(),
          value: valueParts.join('=').trim()
        };
      });

    if (variables.length === 0) {
      message.error('没有找到有效的变量');
      return;
    }

    currentEnvironment.value.variables = variables;
    bulkEditVisible.value = false;
    message.success('环境变量已更新');
    emit('update:env', currentEnvironment.value);
  } catch (error) {
    message.error('导入失败，请检查格式');
  }
};

// 显示导入
const showImport = () => {
  if (!currentEnvironment.value) {
    message.error('请先选择环境');
    return;
  }
  importText.value = '';
  importVisible.value = true;
};

// 处理导入确认
const handleImportOk = () => {
  try {
    // 尝试JSON格式
    const imported = JSON.parse(importText.value);
    if (imported.variables && Array.isArray(imported.variables)) {
      currentEnvironment.value.variables = imported.variables;
      importVisible.value = false;
      message.success('环境变量已导入');
      emit('update:env', currentEnvironment.value);
      return;
    }
  } catch (e) {
    // JSON解析失败，尝试简单格式
    try {
      const variables = importText.value
        .split('\n')
        .filter(line => line.trim() && line.includes('='))
        .map(line => {
          const [name, ...valueParts] = line.split('=');
          return {
            name: name.trim(),
            value: valueParts.join('=').trim()
          };
        });

      if (variables.length === 0) {
        message.error('没有找到有效的变量');
        return;
      }

      currentEnvironment.value.variables = variables;
      importVisible.value = false;
      message.success('环境变量已导入');
      emit('update:env', currentEnvironment.value);
    } catch (error) {
      message.error('导入失败，请检查格式');
    }
  }
};

// 导出变量
const handleExport = () => {
  if (!currentEnvironment.value) {
    message.error('请先选择环境');
    return;
  }

  if (!currentEnvironment.value.variables || currentEnvironment.value.variables.length === 0) {
    message.error('没有可导出的变量');
    return;
  }

  const exportData = {
    name: currentEnvironment.value.variableName,
    variables: currentEnvironment.value.variables
  };

  const data = JSON.stringify(exportData, null, 2);
  const blob = new Blob([data], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `${currentEnvironment.value.variableName || 'environment'}.json`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
  message.success('环境变量已导出');
};
// 初始化
onMounted(() => {
  // 使用传入的环境数据
  if (props.envData && props.envData.length > 0) {
    environments.value = props.envData;
    selectedEnvId.value = props.activeEnvKey;

    // 设置当前环境
    if (props.activeEnvKey) {
      const targetEnv = props.envData.find(env => env.envId === props.activeEnvKey);
      if (targetEnv) {
        currentEnvironment.value = targetEnv;
      }
    } else if (environments.value.length > 0) {
      currentEnvironment.value = environments.value[0];
      selectedEnvId.value = environments.value[0].envId;
    }
  }
});

// 监听props变化
watch(() => props.envData, (newData) => {
  if (newData && newData.length > 0) {
    environments.value = newData;
  }
}, { deep: true });

watch(() => props.activeEnvKey, (newKey) => {
  if (newKey && newKey !== selectedEnvId.value) {
    selectedEnvId.value = newKey;
    const targetEnv = environments.value.find(env => env.envId === newKey);
    if (targetEnv) {
      currentEnvironment.value = targetEnv;
    }
  }
});

// 监听当前环境变化，向父组件发送更新
watch(currentEnvironment, (newEnv) => {
  if (newEnv) {
    emit('update:env', newEnv);
  }
}, { deep: true });
</script>

<style scoped>
.environment-editor {
  display: flex;
  flex-direction: column;
}

.env-variables-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.header-title {
  font-weight: 500;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.editor-footer {
  margin-top: 16px;
}
</style>