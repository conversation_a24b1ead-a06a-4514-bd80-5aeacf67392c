import {CodeOutlined, DatabaseOutlined} from "@ant-design/icons-vue";

const customScript = {
  label: '自定义脚本',
  value: 'script',
  description: '执行自定义JavaScript脚本',
  icon: CodeOutlined,
  color: '#722ed1',
  template: {
    type: 'script',
    code:'python',
  }
}

const databaseAction = {
  label: '数据库操作',
  value: 'database',
  description: '执行数据库查询或更新操作',
  icon: DatabaseOutlined,
  color: '#52c41a',
  template: {
    type: 'database',
    database:{
      databaseType: '',
      databaseHost: '',
      databasePort: '',
      databaseUsername: '',
      databasePassword: '',
      defaultDb: '',
      url: 'mysql://root:123456@127.0.0.1:3306/test'
    },
    operations: [
      {
        sql: 'select * from user where id = ?',
        params: {},
        saveAs: 'user',
      }
    ]
  }
}

const extractVariable = {
  label: '变量提取',
  value: 'extract',
  description: '执行响应变量提取',
  icon: CodeOutlined,
  color: '#722ed1',
  template: {
    type: 'extract_variable',
    extracts: [
      {
        name:'',
        path:'$.data',
        type:'jsonpath',
        default:''
      }

    ]
  }
}

const assertValidate = {
  label: '断言',
  value: 'assert',
  description: '断言',
  icon: CodeOutlined,
  color: '#722ed1',
  template: {
    type: 'assertion',
    assertions: [
      {
        name:'',
        path:'$.var',
        expected:'张三',
        operator:'equals'
      }

    ]
  }
}

export const prefixOperations = [
  customScript,
  databaseAction
];



export const suffixOperations = [
  customScript,
  databaseAction,
  extractVariable,
  assertValidate
];


