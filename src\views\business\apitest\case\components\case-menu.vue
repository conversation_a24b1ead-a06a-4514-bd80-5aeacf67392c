<template>
  <div class="api-explorer">
    <!-- 搜索框 -->
    <div class="search-container">
      <a-input-search
        v-model:value="searchValue"
        placeholder="搜索用例或分组"
        style="margin-bottom: 8px"
        @search="onSearch"
        @change="onSearchChange"
        allowClear
      />
    </div>

    <a-tree
      class="case-tree"
      :tree-data="treeData"
      :field-names="fieldNames"
      :expandedKeys="openKeys"
      :selectedKeys="selectedKeys"
      draggable
      blockNode
      showIcon
      @drop="onDrop"
      @select="onClick"
      @expand="onExpand"
    >
      <template #icon="{ dataRef }">
        <folder-outlined v-if="!dataRef.caseId" class="tree-icon folder-icon" />
        <file-outlined v-else class="tree-icon case-icon" />
      </template>

      <template #title="{ dataRef, key:treeKey }">
        <div
          class="tree-node-container"
          :class="{ 'is-case': dataRef.caseId, 'is-group': !dataRef.caseId }"
          @mouseenter="handleNodeHover(dataRef, true)"
          @mouseleave="handleNodeHover(dataRef, false)"
        >
          <!-- 用例节点 -->
          <template v-if="dataRef.caseId">
            <a-tooltip :title="dataRef.name || dataRef.caseName" placement="topLeft">
              <span class="tree-node-title case-title">{{ dataRef.name || dataRef.caseName }}</span>
            </a-tooltip>
          </template>

          <!-- 分组节点 -->
          <template v-else>
            <a-tooltip :title="dataRef.name" placement="topLeft">
              <span class="tree-node-title group-title">{{ dataRef.name }}</span>
            </a-tooltip>
            <span v-if="getCaseCount(dataRef) > 0" class="case-count">
              ({{ getCaseCount(dataRef) }})
            </span>
            <!-- 悬停时显示的操作按钮 -->
            <div
              v-if="hoveredNode === dataRef"
              class="tree-node-actions"
              @click.stop
              @mouseenter="handleDropdownHover(dataRef, true)"
              @mouseleave="handleDropdownHover(dataRef, false)"
            >
              <a-dropdown
                :trigger="['click']"
                placement="bottomRight"
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                @visibleChange="handleVisibleChange"
                :destroyPopupOnHide="true"
                @click="async (e) => await handleAddApi(dataRef)"
              >
                <a class="ant-dropdown-link" @click.prevent.stop>
                  <plus-outlined />
                </a>
              </a-dropdown>
              <a-dropdown
                :trigger="['click']"
                placement="bottomRight"
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                @visibleChange="handleVisibleChange"
                :destroyPopupOnHide="true"
              >
                <a class="ant-dropdown-link" @click.prevent.stop>
                  <more-outlined />
                </a>
                <template #overlay>
                  <a-menu @click="(e) => handleGroupMenuClick(e, dataRef)">
                    <a-menu-item key="copy">
                      <copy-outlined />
                      添加子目录
                    </a-menu-item>
                    <a-menu-item key="delete">
                      <delete-outlined />
                      删除
                    </a-menu-item>
                    <a-menu-item key="import">
                      <import-outlined />
                      导入
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </div>
          </template>
        </div>
      </template>
    </a-tree>
  </div>
</template>

<script setup>
import {ref, onMounted} from 'vue';
import {CaseApi} from "/src/api/business/apitest/case-api.js";
import {message} from 'ant-design-vue';
import {
  FolderOutlined,
  FileOutlined,
  CopyOutlined,
  DeleteOutlined,
  PlusOutlined,
  MoreOutlined, ImportOutlined
} from '@ant-design/icons-vue';
import CaseGroupDropdown from "/@/views/business/apitest/case/components/case-group-dropdown.vue";

const emits = defineEmits(["onClick", "refresh"]);

// 状态管理
const openKeys = ref([]);
const selectedKeys = ref([]);
const treeData = ref([]);
const originalTreeData = ref([]); // 保存原始树数据
const searchValue = ref('');
const hoveredNode = ref(null);
const hoverTimer = ref(null);
const pendingHoverNode = ref(null); // 待悬停的节点
const modalOpen = ref(false); // 添加模态框状态

// 计算分组下的用例数量
const getCaseCount = (groupNode) => {
  let count = 0;

  // 计算直接子用例
  if (groupNode.cases) {
    count += groupNode.cases.length;
  }

  // 递归计算子分组的用例
  if (groupNode.children) {
    groupNode.children.forEach(child => {
      if (child.caseId) {
        count += 1;
      } else {
        count += getCaseCount(child);
      }
    });
  }

  return count;
};

// 处理节点悬停
const handleNodeHover = (dataRef, isHover) => {
  // 清除之前的定时器
  if (hoverTimer.value) {
    clearTimeout(hoverTimer.value);
    hoverTimer.value = null;
  }

  if (isHover && !dataRef.caseId) {
    // 立即显示当前节点的操作按钮，无延迟
    hoveredNode.value = dataRef;
    pendingHoverNode.value = null;
  } else if (!isHover && !modalOpen.value) {
    // 鼠标离开时，设置待处理节点
    pendingHoverNode.value = dataRef;

    // 极短延迟后隐藏，如果在此期间鼠标进入其他节点，则会被取消
    hoverTimer.value = setTimeout(() => {
      if (!modalOpen.value && pendingHoverNode.value === dataRef) {
        hoveredNode.value = null;
        pendingHoverNode.value = null;
      }
    }, 30); // 非常短的延迟，仅用于避免闪烁
  }
};

// 处理dropdown区域悬停
const handleDropdownHover = (dataRef, isHover) => {
  if (hoverTimer.value) {
    clearTimeout(hoverTimer.value);
    hoverTimer.value = null;
  }

  if (isHover) {
    hoveredNode.value = dataRef;
  } else if (!modalOpen.value) {
    // 只有在模态框未打开时才延迟隐藏，减少延迟时间
    hoverTimer.value = setTimeout(() => {
      if (!modalOpen.value) {
        hoveredNode.value = null;
      }
    }, 100); // 从 300ms 减少到 100ms
  }
};

// 处理模态框状态变化
const handleModalStateChange = (isOpen) => {
  modalOpen.value = isOpen;
  console.log('Modal state changed:', isOpen);

  // 如果模态框关闭，延迟一段时间后检查是否需要隐藏 dropdown
  if (!isOpen) {
    setTimeout(() => {
      if (!modalOpen.value) {
        // 检查鼠标是否还在节点区域内，如果不在则隐藏
        // 这里可以根据实际需要调整逻辑
      }
    }, 100);
  }
};

// 处理新增用例
const handleAddCase = (groupId) => {
  console.log('新增用例到分组:', groupId);
  // 这里可以触发新增用例的逻辑
};

// 处理重命名分组
const handleRenameGroup = (data) => {
  console.log('重命名分组:', data);
  // 这里可以调用API更新分组名称
  emits('refresh');
};

// 字段映射配置
const fieldNames = {
  children: 'children',
  title: 'name',
  key: 'key'
};

// 处理树数据，添加key和isLeaf属性
const processTreeData = (data) => {
  if (!data) return [];

  return data.map(item => {
    // 创建新对象，避免修改原始数据
    const node = {...item};

    // 为分组添加key
    if (node.groupId) {
      node.key = `group-${node.groupId}`;
      // 处理子分组
      if (node.children && node.children.length > 0) {
        node.children = processTreeData(node.children);
      }

      // 处理用例
      if (node.cases && node.cases.length > 0) {
        const caseNodes = node.cases.map(caseItem => ({
          ...caseItem,
          key: `case-${caseItem.caseId}`,
          name: caseItem.caseName, // 使用caseName作为显示文本
          isLeaf: true // 标记为叶子节点
        }));

        // 如果已有children，则合并，否则创建新数组
        if (node.children) {
          node.children = [...node.children, ...caseNodes];
        } else {
          node.children = caseNodes;
        }
      }

      // 如果没有子节点和用例，则标记为叶子节点
      if ((!node.children || node.children.length === 0) &&
          (!node.cases || node.cases.length === 0)) {
        node.isLeaf = true;
      }
    }

    return node;
  });
};

const fetchMenuTree = async () => {
  try {
    // 实际项目中应该使用API调用
    const response = await CaseApi.tree()
    const processedData = processTreeData(response.data);
    originalTreeData.value = processedData; // 保存原始数据
    treeData.value = processedData;

    // 默认展开第一级
    openKeys.value = processedData.map(node => node.key);
  } catch (error) {
    console.error('获取菜单树出错:', error);
  }
};


// 处理点击事件
function onClick(selectedKeys, info) {
  if (selectedKeys.length === 0) return;

  const key = selectedKeys[0];
  emits('onClick', key);
}

// 处理展开事件
function onExpand(expandedKeys) {
  openKeys.value = expandedKeys;
}

// 搜索功能
const onSearch = (value) => {
  searchValue.value = value;
  performSearch(value);
};

const onSearchChange = (e) => {
  const value = e.target.value;
  if (!value) {
    // 清空搜索时恢复原始数据
    performSearch('');
  }
};

// 处理分组菜单点击
const handleGroupMenuClick = async (e, dataRef) => {
  switch (e.key) {
    case 'copy':
      await handleAddChildren(dataRef)
      break;
    case 'delete':
      await handleDelete(dataRef.groupId)
      break;
    case 'import':
      handleImport(dataRef);
      break;
  }

};

const performSearch = (searchText) => {
  if (!searchText) {
    // 恢复原始数据
    treeData.value = [...originalTreeData.value];
    openKeys.value = originalTreeData.value.map(node => node.key);
    return;
  }

  // 递归搜索树节点
  const searchTree = (nodes, searchValue) => {
    const result = [];

    for (const node of nodes) {
      const nodeClone = { ...node };
      let shouldInclude = false;

      // 检查当前节点是否匹配（分组名称或用例名称）
      const nodeName = node.name || node.caseName || '';
      if (nodeName.toLowerCase().includes(searchValue.toLowerCase())) {
        shouldInclude = true;
      }

      // 检查子节点
      if (node.children && node.children.length > 0) {
        const filteredChildren = searchTree(node.children, searchValue);
        if (filteredChildren.length > 0) {
          nodeClone.children = filteredChildren;
          shouldInclude = true;
        }
      }

      if (shouldInclude) {
        result.push(nodeClone);
      }
    }

    return result;
  };

  // 执行搜索
  const filteredData = searchTree(originalTreeData.value, searchText);
  treeData.value = filteredData;

  // 展开所有匹配的节点
  const getAllKeys = (nodes) => {
    const keys = [];
    const traverse = (nodeList) => {
      nodeList.forEach(node => {
        if (!node.caseId) { // 只展开分组节点
          keys.push(node.key);
        }
        if (node.children) {
          traverse(node.children);
        }
      });
    };
    traverse(nodes);
    return keys;
  };

  openKeys.value = getAllKeys(filteredData);
};

// 处理拖拽事件
const onDrop = async (info) => {
  const dropKey = info.node.key;
  const dragKey = info.dragNode.key;
  const dropPos = info.node.pos.split('-');
  const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1]);

  // 解析拖拽节点和目标节点的信息
  const isDragCase = dragKey.startsWith('case-');
  const isDropCase = dropKey.startsWith('case-');
  const dragId = parseInt(dragKey.split('-')[1]);
  const dropId = parseInt(dropKey.split('-')[1]);

  console.log('拖拽信息:', {
    dragKey,
    dropKey,
    dropPos,
    dropPosition,
    isDragCase,
    isDropCase,
    dragId,
    dropId
  });

  try {
    // 不允许将分组拖到用例下
    if (!isDragCase && isDropCase) {
      message.error('不能将分组拖到用例下');
      return;
    }

    // 克隆数据
    const data = [...treeData.value];

    // 查找被拖拽节点
    let dragObj;
    const loop = (data, key, callback) => {
      for (let i = 0; i < data.length; i++) {
        if (data[i].key === key) {
          return callback(data, i, data[i]);
        }
        if (data[i].children) {
          loop(data[i].children, key, callback);
        }
      }
    };

    // 删除拖拽的节点
    loop(data, dragKey, (list, index, item) => {
      dragObj = item;
      list.splice(index, 1);
    });

    if (!dragObj) {
      return;
    }

    // 根据放置位置处理
    if (!info.dropToGap) {
      // 放到节点上 - 成为子节点
      loop(data, dropKey, (list, index, item) => {
        // 如果目标是用例，不允许放入
        if (isDropCase) {
          message.error('不能将节点拖到用例下');
          return;
        }

        // 目标是分组，放入其中
        item.children = item.children || [];

        // 更新拖拽节点的父级ID
        if (isDragCase) {
          dragObj.groupId = dropId;
        } else {
          dragObj.parentId = dropId;
        }

        // 放在开头
        item.children.unshift(dragObj);
      });
    } else if (dropPosition === -1) {
      // 放到节点前面
      loop(data, dropKey, (list, index, item) => {
        // 更新拖拽节点的父级ID
        if (isDragCase) {
          // 如果是用例，父级是当前节点的父级
          dragObj.groupId = isDropCase ? item.groupId : item.parentId;
        } else {
          // 如果是分组，父级是当前节点的父级
          dragObj.parentId = item.parentId;
        }

        list.splice(index, 0, dragObj);
      });
    } else {
      // 放到节点后面
      loop(data, dropKey, (list, index, item) => {
        // 更新拖拽节点的父级ID
        if (isDragCase) {
          // 如果是用例，父级是当前节点的父级
          dragObj.groupId = isDropCase ? item.groupId : item.parentId;
        } else {
          // 如果是分组，父级是当前节点的父级
          dragObj.parentId = item.parentId;
        }

        list.splice(index + 1, 0, dragObj);
      });
    }

    // 更新树数据
    treeData.value = data;

    message.success('更新菜单顺序成功');
  } catch (error) {
    console.error('更新菜单顺序失败:', error);
    message.error('更新菜单顺序失败');

    // 如果失败，重新加载树
    await fetchMenuTree();
  }
};

defineExpose(['fetchMenuTree', 'performSearch'])

// 组件挂载后获取数据
onMounted(() => {
  fetchMenuTree();
});
</script>

<style scoped>
.api-explorer {
  height: 100%;
  overflow: auto;
  background: #fafafa;
}

.search-container {
  padding: 8px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.case-tree {
  width: 100%;
  background: transparent;
}

/* 树节点容器 - 移除悬停动画 */
.tree-node-container {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 2px 0;
  border-radius: 4px;
  min-height: 24px;
  position: relative;
  transition: background-color 0.05s ease-out; /* 减少过渡时间 */
}

.tree-node-container:hover {
  background-color: #f0f0f0;
}

/* 用例节点样式 */
.tree-node-container.is-case {
  gap: 8px;
}

/* 分组节点样式 */
.tree-node-container.is-group {
  justify-content: space-between;
}

/* 树图标样式 */
.tree-icon {
  font-size: 14px;
}

.folder-icon {
  color: #faad14;
}

.case-icon {
  color: #52c41a;
}

/* 节点标题 */
.tree-node-title {
  font-size: 13px;
  line-height: 1.4;
  cursor: pointer;
}

/* 用例标题 */
.case-title {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 分组标题 */
.group-title {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
}

/* 用例数量显示 */
.case-count {
  color: #999;
  font-size: 11px;
  white-space: nowrap;
  margin-left: 8px;
  flex-shrink: 0;
}

/* 节点操作按钮 - 移除过渡动画 */
.tree-node-actions {
  display: flex;
  opacity: 0;
  margin-left: 8px;
  flex-shrink: 0;
  padding: 2px;
  border-radius: 2px;
  position: relative;
  z-index: 10;
}

.tree-node-container:hover .tree-node-actions,
.tree-node-actions:hover {
  opacity: 1;
}

/* dropdown按钮样式 */
.tree-node-actions .ant-dropdown-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 2px;
  transition: background-color 0.05s; /* 减少过渡时间 */
}

.tree-node-actions .ant-dropdown-link:hover {
  background-color: #e6f7ff;
}

/* 自定义ant-design样式 */
:deep(.ant-tree-node-content-wrapper) {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0 4px !important;
  border-radius: 4px;
  line-height: 24px;
}

:deep(.ant-tree-node-content-wrapper:hover) {
  background-color: transparent !important;
}

:deep(.ant-tree-node-content-wrapper.ant-tree-node-selected) {
  background-color: #e6f7ff !important;
}

:deep(.ant-tree-title) {
  width: 100%;
  margin-left: 0 !important;
}

/* 确保图标和内容对齐 */
:deep(.ant-tree-iconEle) {
  width: 16px;
  height: 16px;
  line-height: 16px;
  text-align: center;
  margin-right: 8px;
}

/* 拖拽样式 */
:deep(.ant-tree-drop-indicator) {
  background-color: #1890ff;
}

:deep(.ant-tree-treenode-dragging) {
  background-color: #e6f7ff;
  border: 1px dashed #1890ff;
}

/* 树节点间距调整 */
:deep(.ant-tree-treenode) {
  padding: 1px 0;
}

:deep(.ant-tree-child-tree) {
  margin-left: 24px;
}

/* 选中状态样式 */
:deep(.ant-tree-node-selected .tree-node-title) {
  color: #1890ff;
  font-weight: 500;
}

/* 确保展开/收起图标对齐 */
:deep(.ant-tree-switcher) {
  width: 16px;
  height: 24px;
  line-height: 24px;
  text-align: center;
}

/* 修复节点内容对齐 */
:deep(.ant-tree-node-content-wrapper) {
  position: relative;
}

:deep(.ant-tree-node-content-wrapper .ant-tree-iconEle) {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}

:deep(.ant-tree-node-content-wrapper .ant-tree-title) {
  margin-left: 24px !important;
}
</style>