<template>
  <div class="api-platform-container">
    <!-- 顶部工具栏 -->
    <div class="api-toolbar">
      <div class="api-toolbar-left">
        <a-dropdown>
          <a-button class="method-button" :style="{ backgroundColor: getMethodColor(apiData.method, true), color: '#fff' }">
            {{ apiData.method.toUpperCase() }}
            <down-outlined />
          </a-button>
          <template #overlay>
            <a-menu @click="handleMethodChange">
              <a-menu-item v-for="method in methodOptions" :key="method.value">
                <span :style="{ color: method.color }">{{ method.label }}</span>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>

        <a-input
          v-model:value="apiData.url"
          placeholder="输入请求URL (例如: https://api.example.com/v1/users)"
          class="url-input"
          @pressEnter="sendRequest"
          allow-clear
        >
          <template #addonBefore>
            <a-select v-model:value="apiData.protocol" class="protocol-select">
              <a-select-option value="http">HTTP</a-select-option>
              <a-select-option value="https">HTTPS</a-select-option>
              <a-select-option value="ws">WS</a-select-option>
              <a-select-option value="wss">WSS</a-select-option>
            </a-select>
          </template>
        </a-input>

        <a-button
          type="primary"
          @click="sendRequest"
          :loading="sending"
          class="send-button"
        >
          <thunderbolt-outlined /> 发送
        </a-button>

        <a-button
          type="default"
          @click="saveApi"
          :loading="saving"
          class="save-button"
        >
          <save-outlined /> 保存
        </a-button>
      </div>

      <div class="api-toolbar-right">
        <a-button-group>
          <a-button @click="showHistory">
            <history-outlined /> 历史记录
          </a-button>
          <a-button @click="showEnvironments">
            <environment-outlined /> 环境
          </a-button>
          <a-dropdown>
            <a-button>
              <more-outlined />
            </a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item key="copy">
                  <copy-outlined /> 复制为cURL
                </a-menu-item>
                <a-menu-item key="code">
                  <code-outlined /> 生成代码
                </a-menu-item>
                <a-menu-item key="export">
                  <export-outlined /> 导出
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </a-button-group>
      </div>
    </div>

    <!-- 请求与响应区域 -->
    <div class="api-content">
      <!-- 请求配置区域 -->
      <div class="request-section">
        <!-- 请求信息标签页 -->
        <a-tabs v-model:activeKey="activeRequestTab">
          <a-tab-pane key="params" tab="查询参数">
            <request-params-editor v-model:params="apiData.params" />
          </a-tab-pane>

          <a-tab-pane key="headers" tab="请求头">
            <request-headers-editor
              v-model:headers="apiData.headers"
              :content-type="apiData.contentType"
              @update:content-type="apiData.contentType = $event"
            />
          </a-tab-pane>

          <a-tab-pane key="body" tab="请求体">
            <request-body-editor
              v-model:body="apiData.requestBody"
              v-model:content-type="apiData.contentType"
            />
          </a-tab-pane>

          <a-tab-pane key="prefix" tab="前置操作">
            <operations-modal operation-title="前置操作" :operation-options="prefixOperations" v-model:operations="apiData.prefixOperations"/>
          </a-tab-pane>

          <a-tab-pane key="suffix" tab="后置操作">
            <operations-modal operation-title="后置操作" :operation-options="suffixOperations" :operations="apiData.suffixOperations"/>
          </a-tab-pane>

          <a-tab-pane key="auth" tab="认证">
            <auth-editor v-model:auth-type="apiData.authType" v-model:auth-config="apiData.authConfig" />
          </a-tab-pane>

          <a-tab-pane key="settings" tab="设置">
            <request-settings-editor v-model:settings="apiData.settings" />
          </a-tab-pane>
        </a-tabs>
      </div>

      <!-- 分割线 -->
      <a-divider class="resize-divider" />

      <!-- 响应区域 -->
      <div class="response-section">
        <div class="response-header">
          <div class="response-status" v-if="response">
            <a-tag :color="getStatusColor(response.status)" class="status-tag">
              {{ response.status }}
            </a-tag>
            <span class="status-text">{{ response.statusText }}</span>
            <span class="response-time">{{ response.time }}ms</span>
            <span class="response-size">{{ getResponseSize() }}</span>
          </div>
          <div class="response-actions">
            <a-button-group>
              <a-tooltip title="以新窗口打开">
                <a-button size="small" @click="openInNewWindow">
                  <export-outlined />
                </a-button>
              </a-tooltip>
              <a-tooltip title="下载响应">
                <a-button size="small" @click="downloadResponse">
                  <download-outlined />
                </a-button>
              </a-tooltip>
              <a-tooltip title="清除响应">
                <a-button size="small" @click="clearResponse">
                  <clear-outlined />
                </a-button>
              </a-tooltip>
            </a-button-group>
          </div>
        </div>

        <a-tabs v-model:activeKey="activeResponseTab">
          <a-tab-pane key="body" tab="响应体">
            <response-body-viewer
              :response="response"
              :active-view="responseViewMode"
              @change-view="responseViewMode = $event"
            />
          </a-tab-pane>

          <a-tab-pane key="headers" tab="响应头">
            <response-headers-viewer :headers="response?.headers" />
          </a-tab-pane>

          <a-tab-pane key="cookies" tab="Cookies">
            <response-cookies-viewer :cookies="responseCookies" />
          </a-tab-pane>

          <a-tab-pane key="timeline" tab="时间轴">
            <response-timeline-viewer :response="response" />
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>

    <!-- 侧边抽屉 -->
    <a-drawer
      title="API详细信息"
      :visible="drawerVisible"
      @close="drawerVisible = false"
      width="500"
      :footer-style="{ textAlign: 'right' }"
    >
      <a-form
        ref="basicForm"
        :model="apiData"
        layout="vertical"
      >
        <a-form-item label="接口名称" name="name" :rules="[{ required: true }]">
          <a-input v-model:value="apiData.name" placeholder="请输入接口名称" />
        </a-form-item>

        <a-form-item label="所属模块" name="groupId" :rules="[{ required: true }]">
          <group-tree-cascader v-model:group-id="apiData.groupId" />
        </a-form-item>

        <a-form-item label="接口描述" name="desc">
          <a-textarea
            v-model:value="apiData.desc"
            placeholder="请输入接口描述"
            :rows="4"
          />
        </a-form-item>

        <a-form-item label="标签" name="tags">
          <a-select
            v-model:value="apiData.tags"
            mode="tags"
            :token-separators="[',']"
            placeholder="添加标签"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item label="状态" name="status">
          <a-radio-group v-model:value="apiData.status">
            <a-radio-button :value="1">已发布</a-radio-button>
            <a-radio-button :value="2">已废弃</a-radio-button>
            <a-radio-button :value="3">草稿</a-radio-button>
          </a-radio-group>
        </a-form-item>
      </a-form>

      <template #footer>
        <a-space>
          <a-button @click="drawerVisible = false">取消</a-button>
          <a-button type="primary" @click="saveApiDetails">保存</a-button>
        </a-space>
      </template>
    </a-drawer>

    <!-- 历史记录抽屉 -->
    <a-drawer
      title="请求历史记录"
      :visible="historyDrawerVisible"
      @close="historyDrawerVisible = false"
      width="600"
    >
      <a-empty v-if="requestHistory.length === 0" />
      <a-list
        v-else
        :dataSource="requestHistory"
        :pagination="{ pageSize: 10 }"
        size="small"
      >
        <template #renderItem="{ item }">
          <a-list-item>
            <a-list-item-meta
              :title="item.url"
              :description="`${new Date(item.timestamp).toLocaleString()} · ${item.method.toUpperCase()} · ${item.status || 'N/A'}`"
            >
              <template #avatar>
                <a-tag :color="getMethodColor(item.method)">
                  {{ item.method.toUpperCase() }}
                </a-tag>
              </template>
            </a-list-item-meta>
            <template #actions>
              <a @click="loadHistoryRequest(item)">加载</a>
            </template>
          </a-list-item>
        </template>
      </a-list>
    </a-drawer>

    <!-- 环境设置抽屉 -->
    <a-drawer
      title="环境变量"
      :visible="envDrawerVisible"
      @close="envDrawerVisible = false"
      width="600"
    >
      <a-tabs>
        <a-tab-pane key="current" tab="当前环境">
          <environment-editor :env="currentEnvironment" @update:env="updateEnvironment" />
        </a-tab-pane>
        <a-tab-pane key="globals" tab="全局变量">
          <globals-editor :globals="globalVariables" @update:globals="updateGlobals" />
        </a-tab-pane>
      </a-tabs>
    </a-drawer>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, defineAsyncComponent } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import axios from 'axios';
import {
  DownOutlined,
  ThunderboltOutlined,
  SaveOutlined,
  HistoryOutlined,
  EnvironmentOutlined,
  MoreOutlined,
  CopyOutlined,
  CodeOutlined,
  ExportOutlined,
  DownloadOutlined,
  ClearOutlined
} from '@ant-design/icons-vue';
import {getRequest, request} from "/@/lib/axios.js";
import {Api} from "/@/api/business/apitest/api.js";
import OperationsModal from "/@/views/business/apitest/case/components/operations-modal.vue";
import {prefixOperations, suffixOperations} from "/@/views/business/apitest/case/components/operationColumns.js";
import GroupTreeCascader from "/@/views/business/apitest/group/group-tree-cascader.vue";
import {ContentTypeEnum as contentTypeEnum} from "/@/views/business/apitest/api/enum/content-type-enum.js";

// 懒加载组件
const RequestParamsEditor = defineAsyncComponent(() =>
  import('./RequestParamsEditor.vue')
);
const RequestHeadersEditor = defineAsyncComponent(() =>
  import('./RequestHeadersEditor.vue')
);
const RequestBodyEditor = defineAsyncComponent(() =>
  import('./RequestBodyEditor.vue')
);
const AuthEditor = defineAsyncComponent(() =>
  import('./AuthEditor.vue')
);
const RequestSettingsEditor = defineAsyncComponent(() =>
  import('./RequestSettingsEditor.vue')
);
const ResponseBodyViewer = defineAsyncComponent(() =>
  import('./ResponseBodyViewer.vue')
);
const ResponseHeadersViewer = defineAsyncComponent(() =>
  import('./ResponseHeadersViewer.vue')
);
const ResponseCookiesViewer = defineAsyncComponent(() =>
  import('./ResponseCookiesViewer.vue')
);
const ResponseTimelineViewer = defineAsyncComponent(() =>
  import('./ResponseTimelineViewer.vue')
);
const EnvironmentEditor = defineAsyncComponent(() =>
  import('./EnvironmentEditor.vue')
);
const GlobalsEditor = defineAsyncComponent(() =>
  import('./GlobalsEditor.vue')
);

// 路由相关
const route = useRoute();
const router = useRouter();
const apiId = computed(() => route.query.apiId);
const isEdit = computed(() => !!apiId.value);

// 表单引用
const basicForm = ref(null);
const saving = ref(false);
const sending = ref(false);

// 抽屉可见性
const drawerVisible = ref(false);
const historyDrawerVisible = ref(false);
const envDrawerVisible = ref(false);

// 活动标签页
const activeRequestTab = ref('params');
const activeResponseTab = ref('body');
const responseViewMode = ref('pretty');

// 请求历史记录
const requestHistory = ref([]);

// 环境变量
const currentEnvironment = ref({
  name: '开发环境',
  variables: [
    { key: 'baseUrl', value: 'https://api.dev.example.com' },
    { key: 'apiVersion', value: 'v1' }
  ]
});
const globalVariables = ref([
  { key: 'token', value: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' }
]);

// 模块树数据
const moduleTree = ref([]);

// HTTP方法选项
const methodOptions = [
  { label: 'GET', value: 'get', color: '#108ee9' },
  { label: 'POST', value: 'post', color: '#87d068' },
  { label: 'PUT', value: 'put', color: '#2db7f5' },
  { label: 'DELETE', value: 'delete', color: '#f50' },
  { label: 'PATCH', value: 'patch', color: '#722ed1' },
  { label: 'OPTIONS', value: 'options', color: '#faad14' },
  { label: 'HEAD', value: 'head', color: '#fadb14' }
];

// API数据

const apiData = reactive({
  name: '',
  groupId: null,
  method: 'get',
  protocol: 'http',
  url: '',
  desc: '',
  tags: [],
  status: 1,
  headers: [],
  params: [],
  prefixOperations:[],
  suffixOperations:[],
  authType: null,
  authConfig: null,
  contentType: null,
  requestBody: {},
  settings: {
    followRedirects: true,
    timeout: 30000,
    verify: true
  }
});

// 响应数据
const response = ref(null);
const formattedResponseBody = computed(() => {
  if (!response.value || !response.value.body) return '';
  try {
    if (isJsonResponse()) {
      return JSON.stringify(JSON.parse(response.value.body), null, 2);
    } else {
      return response.value.body;
    }
  } catch (e) {
    return response.value.body;
  }
});

// 响应cookies转换为表格数据
const responseCookies = computed(() => {
  if (!response.value || !response.value.cookies) return [];
  return Object.entries(response.value.cookies).map(([name, details]) => ({
    name,
    ...details
  }));
});

// 判断响应类型是否为JSON
const isJsonResponse = () => {
  if (!response.value) return false;
  const contentType = response.value.headers['content-type'] || '';
  return contentType.includes('application/json');
};

// 获取状态码颜色
const getStatusColor = (status) => {
  if (!status) return 'default';
  if (status >= 200 && status < 300) return 'success';
  if (status >= 300 && status < 400) return 'processing';
  if (status >= 400 && status < 500) return 'warning';
  if (status >= 500) return 'error';
  return 'default';
};

// 获取方法颜色
const getMethodColor = (method, isBg = false) => {
  const colors = {
    get: isBg ? '#108ee9' : '#108ee9',
    post: isBg ? '#87d068' : '#87d068',
    put: isBg ? '#2db7f5' : '#2db7f5',
    delete: isBg ? '#f50' : '#f50',
    patch: isBg ? '#722ed1' : '#722ed1',
    options: isBg ? '#faad14' : '#faad14',
    head: isBg ? '#fadb14' : '#fadb14'
  };
  return colors[method?.toLowerCase()] || (isBg ? '#888' : '#888');
};

// 计算响应大小
const getResponseSize = () => {
  if (!response.value || !response.value.body) return '0 B';

  const bytes = new TextEncoder().encode(response.value.body).length;

  if (bytes < 1024) return `${bytes} B`;
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(2)} KB`;
  return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
};

// 处理方法变更
const handleMethodChange = (e) => {
  apiData.method = e.key;
};

// 显示API详情抽屉
const showApiDetails = () => {
  drawerVisible.value = true;
};

// 保存API详情
const saveApiDetails = async () => {
  try {
    await basicForm.value.validate();
    if(isEdit.value){
      await Api.update({apiId: apiId.value, ...apiData});
    }else{
      await Api.add(apiData);
    }
    drawerVisible.value = false;
    message.success('API详情已更新');
  } catch (error) {
    console.error('保存API详情失败:', error);
  }
};

// 显示历史记录
const showHistory = () => {
  console.log('显示历史记录抽屉被调用');
  historyDrawerVisible.value = true;
};

// 加载历史请求
const loadHistoryRequest = (item) => {
  // 加载历史请求数据到当前表单
  apiData.method = item.method;
  apiData.url = item.url;
  // 其他字段...

  historyDrawerVisible.value = false;
  message.success('已加载历史请求');
};

// 显示环境设置
const showEnvironments = () => {
  envDrawerVisible.value = true;
};

// 更新环境变量
const updateEnvironment = (env) => {
  currentEnvironment.value = env;
};

// 更新全局变量
const updateGlobals = (globals) => {
  globalVariables.value = globals;
};

// 在新窗口打开响应
const openInNewWindow = () => {
  if (!response.value || !response.value.body) return;

  const newWindow = window.open();
  newWindow.document.write(response.value.body);
  newWindow.document.close();
};

// 下载响应
const downloadResponse = () => {
  if (!response.value || !response.value.body) return;

  const blob = new Blob([response.value.body], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `response-${new Date().getTime()}.txt`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

// 应用环境变量到URL
const applyEnvironmentVariables = (url) => {
  if (!url) return url;

  let result = url;

  // 应用环境变量
  currentEnvironment.value.variables.forEach(variable => {
    const pattern = new RegExp(`\\{\\{\\s*${variable.key}\\s*\\}\\}`, 'g');
    result = result.replace(pattern, variable.value);
  });

  // 应用全局变量
  globalVariables.value.forEach(variable => {
    const pattern = new RegExp(`\\{\\{\\s*${variable.key}\\s*\\}\\}`, 'g');
    result = result.replace(pattern, variable.value);
  });

  return result;
};

// 发送请求
const sendRequest = async () => {
  try {
    sending.value = true;

    // 应用环境变量
    const processedUrl = applyEnvironmentVariables(apiData.url);

    // 处理文件上传（如果有）
    let fileList = [];
    if (apiData.contentType === 'multipart/form-data' && apiData.fileList && apiData.fileList.length > 0) {
      for (const file of apiData.fileList) {
        if (file.originFileObj) {
          // 上传文件并获取服务器路径
          const formData = new FormData();
          formData.append('file', file.originFileObj);

          const uploadRes = await axios.post('/api/upload-file', formData);
          fileList.push({
            name: file.name,
            file_path: uploadRes.data.file_path,
            enabled: file.enabled !== false
          });
        }
      }
    }
    const processFormList = (form) => {
      const formObject = {};
      form.forEach(item => {
        if (item.enabled !== false) {
          formObject[item.name] = item.value;
        }
      });
      return formObject;
    };
    // 构建代理请求数据

    const proxyData = {
      method: apiData.method,
      url: apiData.protocol +"://" + processedUrl,
      headers: processFormList(apiData.headers),
      params: processFormList(apiData.params),
      contentType: apiData.contentType,
      authType: apiData.authType,
      authConfig: apiData.authConfig,
      settings: apiData.settings
    };
    if ([contentTypeEnum.FORM].includes(apiData.contentType)) {
      proxyData.data = processFormList(apiData.requestBody.data);
    }else if (contentTypeEnum.JSON===apiData.contentType) {
      proxyData.json = apiData.requestBody.json;
    }else if ([contentTypeEnum.XML].includes(apiData.contentType)) {
      proxyData.content = apiData.requestBody.content;
    }else if (contentTypeEnum.MULTIPART === apiData.contentType){
      proxyData.files = apiData.requestBody.files;
    }
    // 发送API请求
    const startTime = Date.now();
    const apiResponse = await Api.apiProxy(proxyData);
    const endTime = Date.now();

    // 添加到历史记录
    requestHistory.value.unshift({
      method: apiData.method,
      url: processedUrl,
      timestamp: Date.now(),
      status: apiResponse.data.status
    });

    // 处理响应
    response.value = {
      status: apiResponse.data.status,
      statusText: apiResponse.data.statusText,
      headers: apiResponse.data.headers,
      body: apiResponse.data.data,
      time: apiResponse.data.time || (endTime - startTime),
      cookies: apiResponse.data.cookies || {},
      timeline: [
        { phase: 'Socket Creation', duration: 5 },
        { phase: 'DNS Lookup', duration: 15 },
        { phase: 'Initial Connection', duration: 30 },
        { phase: 'SSL Handshake', duration: 50 },
        { phase: 'Request Sent', duration: 10 },
        { phase: 'Waiting (TTFB)', duration: apiResponse.data.time - 110 || (endTime - startTime - 110) },
        { phase: 'Content Download', duration: 5 }
      ]
    };

    message.success(`请求成功 (${response.value.time}ms)`);
  } catch (error) {
    console.error('请求失败:', error);
    message.error('请求失败: ' + (error.message || '未知错误'));

  } finally {
    sending.value = false;
  }
};

// 构建请求参数
const buildRequestParams = (url) => {
  // 构建URL（包含查询参数）
  let fullUrl = url;
  if (!fullUrl.startsWith('http://') && !fullUrl.startsWith('https://')) {
    fullUrl = `${apiData.protocol}://${url}`;
  }
  if (apiData.params.length > 0) {
    const queryParams = new URLSearchParams();
    apiData.params.forEach(param => {
      if (param.name && param.enabled !== false) {
        queryParams.append(param.name, param.value || '');
      }
    });

    // 添加查询参数
    const queryString = queryParams.toString();
    if (queryString) {
      const hasQueryString = fullUrl.includes('?');
      fullUrl += hasQueryString ? '&' : '?';
      fullUrl += queryString;
    }
  }

  // 构建请求头
  const headers = {};
  apiData.headers.forEach(header => {
    if (header.name && header.enabled !== false) {
      headers[header.name] = applyEnvironmentVariables(header.value || '');
    }
  });

  // 设置Content-Type
  if (apiData.contentType && apiData.method !== 'get') {
    headers['Content-Type'] = apiData.contentType;
  }

  // 处理认证信息
  if (apiData.authType === 'basic') {
    const token = btoa(`${apiData.authConfig.username}:${apiData.authConfig.password}`);
    headers['Authorization'] = `Basic ${token}`;
  } else if (apiData.authType === 'bearer') {
    headers['Authorization'] = `Bearer ${applyEnvironmentVariables(apiData.authConfig.token)}`;
  } else if (apiData.authType === 'apikey' && apiData.authConfig.keyIn === 'header') {
    headers[apiData.authConfig.keyName] = applyEnvironmentVariables(apiData.authConfig.keyValue);
  }

  // 构建请求体
  let data = null;
  if (apiData.method !== 'get') {
    if (apiData.contentType === 'application/json') {
      try {
        let bodyText = applyEnvironmentVariables(apiData.requestBody);
        data = JSON.parse(bodyText);
      } catch (e) {
        throw new Error('JSON格式错误: ' + e.message);
      }
    } else if (apiData.contentType === 'application/x-www-form-urlencoded') {
      const formData = new URLSearchParams();
      apiData.formData.forEach(item => {
        if (item.name && item.enabled !== false) {
          formData.append(item.name, applyEnvironmentVariables(item.value || ''));
        }
      });
      data = formData;
    } else if (apiData.contentType === 'multipart/form-data') {
      const formData = new FormData();
      apiData.formData.forEach(item => {
        if (item.name && item.enabled !== false) {
          formData.append(item.name, applyEnvironmentVariables(item.value || ''));
        }
      });
      apiData.fileList.forEach(file => {
        if (file.enabled !== false) {
          formData.append(file.name, file.originFileObj);
        }
      });
      data = formData;
    } else {
      data = applyEnvironmentVariables(apiData.requestBody);
    }
  }

  // API Key查询参数处理
  if (apiData.authType === 'apikey' && apiData.authConfig.keyIn === 'query') {
    const hasQueryString = fullUrl.includes('?');
    fullUrl += hasQueryString ? '&' : '?';
    fullUrl += `${apiData.authConfig.keyName}=${encodeURIComponent(applyEnvironmentVariables(apiData.authConfig.keyValue))}`;
  }

  return {
    method: apiData.method,
    url: fullUrl,
    headers,
    data,
    timeout: apiData.settings.timeout,
    validateStatus: null,
    maxRedirects: apiData.settings.followRedirects ? 5 : 0
  };
};

// 解析Cookie
const parseCookies = (cookieHeader) => {
  if (!cookieHeader) return {};

  const cookies = {};
  const cookieStrings = Array.isArray(cookieHeader) ? cookieHeader : [cookieHeader];

  cookieStrings.forEach(cookieStr => {
    const parts = cookieStr.split(';');
    const cookiePart = parts[0].trim();
    const eqPos = cookiePart.indexOf('=');

    if (eqPos > 0) {
      const name = cookiePart.substring(0, eqPos);
      const value = cookiePart.substring(eqPos + 1);

      cookies[name] = {
        value,
        domain: extractCookieAttribute(parts, 'domain'),
        path: extractCookieAttribute(parts, 'path'),
        expires: extractCookieAttribute(parts, 'expires')
      };
    }
  });

  return cookies;
};

// 提取Cookie属性
const extractCookieAttribute = (parts, attrName) => {
  const part = parts.find(p => p.trim().toLowerCase().startsWith(attrName + '='));
  return part ? part.split('=')[1].trim() : '';
};

// 清除响应数据
const clearResponse = () => {
  response.value = null;
};

// 保存API
const saveApi = async () => {
  try {
    drawerVisible.value = true; // 打开详情抽屉让用户填写详细信息
  } catch (error) {
    console.error('保存失败:', error);
    message.error('保存失败: ' + (error.message || '未知错误'));
  }
};

// 保存完整API
const saveCompleteApi = async () => {
  try {
    await basicForm.value.validate();

    saving.value = true;

    // 构建保存数据
    const saveData = {
      ...apiData,
      // 处理特殊字段
      requestBody: apiData.contentType === 'application/json' ?
        JSON.stringify(JSON.parse(apiData.requestBody)) : apiData.requestBody
    };

    // 调用API保存
    const result = await axios({
      method: isEdit.value ? 'put' : 'post',
      url: isEdit.value ? `/api/apis/${apiId.value}` : '/api/apis',
      data: saveData
    });

    message.success(isEdit.value ? '接口更新成功' : '接口创建成功');

    // 返回列表页
    router.push('/api/list');
  } catch (error) {
    console.error('保存失败:', error);
    message.error('保存失败: ' + (error.message || '未知错误'));
  } finally {
    saving.value = false;
  }
};

// 返回上一页
const goBack = () => {
  router.back();
};

// 加载API数据
const loadApiData = async () => {
  if (!isEdit.value) return;

  try {
    const params = {apiId: apiId.value}
    // 调用API获取接口数据
    const response = await Api.get(params);
    console.log("查看参数",params)
    const apiDetail = response.data[0];

    // 更新表单数据
    Object.assign(apiData, apiDetail);

    // // 处理特殊字段
    // if (apiData.contentType === 'application/json' && typeof apiData.requestBody === 'object') {
    //   apiData.requestBody = JSON.stringify(apiData.requestBody, null, 2);
    // }

    // // 确保数组字段初始化
    // if (!Array.isArray(apiData.headers)) apiData.headers = [];
    // if (!Array.isArray(apiData.params)) apiData.params = [];
    // if (!Array.isArray(apiData.formData)) apiData.formData = [];
    // if (!Array.isArray(apiData.fileList)) apiData.fileList = [];
    // if (!Array.isArray(apiData.tags)) apiData.tags = [];

  } catch (error) {
    console.error('加载接口数据失败:', error);
    message.error('加载接口数据失败: ' + (error.message || '未知错误'));
  }
};

// 加载请求历史
const loadRequestHistory = async () => {
  try {
    // 这里可以从本地存储或服务器加载
  } catch (error) {
    console.error('加载请求历史失败:', error);
    // 使用模拟数据
    requestHistory.value = [
      {
        method: 'get',
        url: 'https://api.example.com/users',
        timestamp: Date.now() - 3600000,
        status: 200
      },
      {
        method: 'post',
        url: 'https://api.example.com/auth/login',
        timestamp: Date.now() - 7200000,
        status: 200
      },
      {
        method: 'put',
        url: 'https://api.example.com/users/123',
        timestamp: Date.now() - 86400000,
        status: 204
      }
    ];
  }
};

// 加载环境变量
const loadEnvironments = async () => {
  try {
    // 从本地存储或服务器加载
    const storedEnv = localStorage.getItem('currentEnvironment');
    const storedGlobals = localStorage.getItem('globalVariables');

    if (storedEnv) {
      currentEnvironment.value = JSON.parse(storedEnv);
    }

    if (storedGlobals) {
      globalVariables.value = JSON.parse(storedGlobals);
    }
  } catch (error) {
    console.error('加载环境变量失败:', error);
  }
};

// 保存环境变量
const saveEnvironments = () => {
  try {
    localStorage.setItem('currentEnvironment', JSON.stringify(currentEnvironment.value));
    localStorage.setItem('globalVariables', JSON.stringify(globalVariables.value));
  } catch (error) {
    console.error('保存环境变量失败:', error);
  }
};

// 复制为cURL
const copyAsCurl = () => {
  try {
    const url = `${apiData.protocol}://${apiData.url}`;
    let curlCommand = `curl -X ${apiData.method.toUpperCase()} "${url}"`;

    // 添加请求头
    apiData.headers.forEach(header => {
      if (header.name && header.enabled !== false) {
        curlCommand += ` -H "${header.name}: ${header.value}"`;
      }
    });

    // 添加请求体
    if (apiData.method !== 'get' && apiData.method !== 'head') {
      if (apiData.contentType === 'application/json') {
        curlCommand += ` -d '${apiData.requestBody}'`;
      } else if (apiData.contentType === 'application/x-www-form-urlencoded') {
        const formData = apiData.formData
          .filter(item => item.name && item.enabled !== false)
          .map(item => `${item.name}=${encodeURIComponent(item.value || '')}`)
          .join('&');
        curlCommand += ` -d '${formData}'`;
      }
    }

    // 复制到剪贴板
    navigator.clipboard.writeText(curlCommand);
    message.success('已复制cURL命令到剪贴板');
  } catch (error) {
    console.error('复制cURL失败:', error);
    message.error('复制失败: ' + (error.message || '未知错误'));
  }
};

// 初始化
onMounted(async () => {
  await Promise.all([
    loadApiData(),
    loadRequestHistory(),
    loadEnvironments()
  ]);
});
</script>

<style scoped>
.api-platform-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.api-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #fff;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.api-toolbar-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.method-button {
  min-width: 90px;
  text-align: left;
  font-weight: 500;
  border: none;
}

.url-input {
  margin: 0 12px;
  flex: 1;
}

.protocol-select {
  width: 90px;
}

.send-button {
  margin-right: 8px;
}

.api-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.request-section {
  flex: 1;
  padding: 16px;
  background-color: #fff;
  min-height: 250px;
  overflow: auto;
}

.resize-divider {
  cursor: row-resize;
  margin: 0;
  user-select: none;
  position: relative;
}

.resize-divider::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 30px;
  height: 5px;
  background-color: #e8e8e8;
  border-radius: 3px;
}

.response-section {
  flex: 1;
  padding: 16px;
  background-color: #fff;
  overflow: auto;
  display: flex;
  flex-direction: column;
}

.response-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.response-status {
  display: flex;
  align-items: center;
}

.status-tag {
  font-weight: bold;
  font-size: 14px;
}

.status-text {
  margin-left: 8px;
  font-weight: 500;
}

.response-time {
  margin-left: 16px;
  color: #666;
  font-size: 13px;
}

.response-size {
  margin-left: 16px;
  color: #666;
  font-size: 13px;
}

.response-actions {
  display: flex;
  align-items: center;
}

/* 增强标签页样式 */
:deep(.ant-tabs-tab) {
  padding: 12px 16px;
  font-size: 14px;
}

:deep(.ant-tabs-tab-active) {
  font-weight: 500;
}

:deep(.ant-tabs-content) {
  height: 100%;
  overflow: auto;
}

/* 增强响应体样式 */
:deep(.monaco-editor) {
  border: 1px solid #e8e8e8;
  border-radius: 2px;
}

:deep(.ant-drawer-body) {
  padding: 24px;
}

/* 响应查看器样式 */
:deep(.response-viewer) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
  font-size: 13px;
  line-height: 1.5;
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 2px;
  background-color: #fafafa;
  overflow: auto;
  height: 400px;
}

/* 响应切换视图按钮组 */
:deep(.response-view-switcher) {
  margin-bottom: 12px;
}

/* 历史记录样式 */
:deep(.ant-list-item-meta-title) {
  margin-bottom: 4px;
  font-size: 14px;
  font-weight: 500;
}

:deep(.ant-list-item-meta-description) {
  font-size: 12px;
}

/* 环境变量编辑器样式 */
:deep(.env-editor-table) {
  margin-bottom: 16px;
}

/* 美化表单 */
:deep(.ant-form-item-label > label) {
  font-weight: 500;
}

/* 响应Timeline */
:deep(.timeline-bar) {
  height: 24px;
  margin: 8px 0;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

:deep(.timeline-segment) {
  height: 100%;
  position: absolute;
  top: 0;
}

:deep(.timeline-dns) {
  background-color: #faad14;
}

:deep(.timeline-connect) {
  background-color: #1890ff;
}

:deep(.timeline-ssl) {
  background-color: #722ed1;
}

:deep(.timeline-wait) {
  background-color: #52c41a;
}

:deep(.timeline-download) {
  background-color: #13c2c2;
}

/* 响应为空时的样式 */
:deep(.response-empty) {
  padding: 48px 0;
  text-align: center;
  color: #999;
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
}
</style>