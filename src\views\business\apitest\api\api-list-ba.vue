<template>
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="产品线" class="smart-query-form-item">
        <a-input style="width: 300px" v-model:value="queryForm.productLineName" placeholder="产品线" />
      </a-form-item>
      <a-form-item label="模块" class="smart-query-form-item">
        <a-input style="width: 300px" v-model:value="queryForm.moduleName" placeholder="模块" />
      </a-form-item>
      <a-form-item label="接口名称" class="smart-query-form-item">
        <a-input style="width: 300px" v-model:value="queryForm.name" placeholder="接口名称" />
      </a-form-item>
      <a-form-item label="状态" style="width: 150px" class="smart-query-form-item">
        <a-select v-model:value="queryForm.status">
          <a-select-option :value=1>启用</a-select-option>
          <a-select-option :value=0>禁用</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item class="smart-query-form-item smart-margin-left10">
        <a-button-group>
          <a-button type="primary" @click="onSearch">
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </a-button>
          <a-button @click="resetQuery">
            <template #icon>
              <ReloadOutlined />
            </template>
            重置
          </a-button>
        </a-button-group>
      </a-form-item>
    </a-row>
  </a-form>

  <a-card size="small" :bordered="false" :hoverable="true">
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="add" type="primary">
          <template #icon>
            <PlusOutlined />
          </template>
          新建接口
        </a-button>
        <a-button @click="exportExcel()" type="primary">
          <template #icon>
            <FileExcelOutlined />
          </template>
          导入
        </a-button>
      </div>
    </a-row>

    <a-table
      :scroll="{ x: 1300 }"
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="id"
      :pagination="false"
      :loading="tableLoading"
      bordered
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button @click="router.push({ path:'/api/detail',query: { apiId: record.apiId }})" size="small" type="link">编辑</a-button>
            <a-button @click="confirmDelete(record.enterpriseId)" size="small" danger type="link">删除</a-button>
          </div>
        </template>
      </template>
    </a-table>
    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="ajaxQuery"
        @showSizeChange="ajaxQuery"
        :show-total="(total) => `共${total}条`"
      />
    </div>
  </a-card>
</template>
<script setup>
  import { reactive, ref, onMounted, h} from 'vue';
  import { message, Modal, Tag} from 'ant-design-vue';
  import { PAGE_SIZE, PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import { useRouter } from 'vue-router';
  import { smartSentry } from '/@/lib/smart-sentry';
  import { Api } from '/@/api/business/apitest/api.js';

  const columns = ref([
      {
      title: '产品线',
      dataIndex: 'productLineName',
      minWidth: 20,
      ellipsis: true,
    },
      {
      title: '模块',
      dataIndex: 'moduleName',
      minWidth: 20,
      ellipsis: true,
    },
      {
      title: '功能点',
      dataIndex: 'featureName',
      minWidth: 20,
      ellipsis: true,
    },
    {
      title: '接口名称',
      dataIndex: 'name',
      minWidth: 20,
      ellipsis: true,
    },
      {
      title: '接口类型',
      dataIndex: 'protocol',
      minWidth: 20,
      ellipsis: true,
    },
    {
      title: '接口地址',
      dataIndex: 'url',
      minWidth: 20,
      ellipsis: true,
    },
    {
      title: '请求方法',
      dataIndex: 'method',
      width: 100,
      customRender: ({ text }) => {
        const colorMap = {
          'GET': '#108ee9',      // 蓝色
          'POST': '#87d068',     // 绿色
          'PUT': '#2db7f5',      // 浅蓝色
          'DELETE': '#f50',      // 红色
          'PATCH': '#722ed1',    // 紫色
          'OPTIONS': '#faad14',  // 黄色
          'HEAD': '#fadb14'      // 浅黄色
        };
        return h(Tag, { color: colorMap[text.toUpperCase()] || '#d9d9d9' }, () => text.toUpperCase());
      },
    },
    {
      title: '数据类型',
      minWidth: 100,
      dataIndex: 'contentType',
      ellipsis: true,
    },
    // {
    //   title: '请求头',
    //   width: 120,
    //   dataIndex: 'headers',
    //   ellipsis: true,
    // },
    // {
    //   title: '请求数据',
    //   minWidth: 100,
    //   dataIndex: 'data',
    //   ellipsis: true,
    //   customRender: ({ text }) => {
    //     return JSON.stringify(text);
    //   },
    // },
    {
      title: '备注',
      minWidth: 50,
      dataIndex: 'desc',
    },
      {
      title: '状态',
      minWidth: 50,
      dataIndex: 'status',
      customRender: ({ text }) => {
        console.log(text)
        return text === 1 ? '启用' : '禁用';
      },
    },
    {
      title: '创建用户',
      minWidth: 60,
      dataIndex: 'createUserId',
    },
    {
      title: '更新用户',
      dataIndex: 'updateUserId',
      minWidth: 20,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      minWidth: 100,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      minWidth: 100,
    },
    {
      title: '操作',
      dataIndex: 'action',
      minWidth: 100,
    },
  ]);

  // --------------------------- 查询 ---------------------------

  const queryFormState = {
    productLineName: '',
    moduleName: '',
    name: '',
    status: undefined,
    pageNum: 1,
    pageSize: PAGE_SIZE,
  };
  const queryForm = reactive({ ...queryFormState });
  const tableLoading = ref(false);
  const tableData = ref([]);
  const total = ref(0);

  // 日期选择
  let searchDate = ref();

  function onSearch() {
    queryForm.pageNum = 1;
    ajaxQuery();
  }

  function resetQuery() {
    searchDate.value = [];
    Object.assign(queryForm, queryFormState);
    ajaxQuery();
  }

  async function ajaxQuery() {
    try {
      tableLoading.value = true;
      let responseModel = await Api.pageQuery(queryForm);
      const list = responseModel.data.items;
      total.value = responseModel.data.total;
      tableData.value = list;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  // --------------------------- 删除 ---------------------------

  function confirmDelete(enterpriseId) {
    Modal.confirm({
      title: '确定要删除吗？',
      content: '删除后，该信息将不可恢复',
      okText: '删除',
      okType: 'danger',
      onOk() {
        console.log('OK');
      },
      cancelText: '取消',
      onCancel() {},
    });
  }

  // --------------------------- 增加、修改、详情 ---------------------------

  let router = useRouter();
  const operateRef = ref();
  const scanDataOperateModal = ref();
  const showDrawer = async (record) => {
    scanDataOperateModal.value.showDrawer(record);
  };

  function add() {
    router.push({path:"/api/detail",query:{apiId:''}})
  }
  onMounted(ajaxQuery);
</script>
