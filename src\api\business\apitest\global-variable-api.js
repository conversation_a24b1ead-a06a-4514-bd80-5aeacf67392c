import {getRequest, postRequest} from "/@/lib/axios.js";

export const globalVariableApi = {
    pageQuery: (param) => {
        return getRequest('/api_test/global_variable/page', param);
    },
    add: (data) => {
        return postRequest('/api_test/global_variable/add', data);
    },
    update: (data) => {
        return postRequest('/api_test/global_variable/update', data);
    },
    delete: (ids) => {
        return getRequest('/api_test/global_variable/delete',  {variableIds: ids});
    },
    get: (params) => {
        return getRequest('/api_test/global_variable/get', params);
    }
}