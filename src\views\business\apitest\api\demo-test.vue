<template>
  <a-menu
    mode="inline"
    :defaultOpenKeys="defaultOpenKeys"
    @click="handleMenuClick"
  >
    <template v-for="item in menuItems">
      <!-- 目录项 -->
      <a-sub-menu v-if="item.type === 'directory'" :key="item.key">
        <template #title>
          <a-icon type="folder" />
          <span>{{ item.title }}</span>
        </template>

        <!-- 子目录中的API项 -->
        <a-menu-item
          v-for="api in item.children.filter(child => child.type === 'api')"
          :key="api.key"
        >
          <a-tag :color="getMethodColor(api.method)">{{ api.method }}</a-tag>
          {{ api.title }}
        </a-menu-item>

        <!-- 子目录中的目录项 -->
        <a-sub-menu
            v-for="subDir in item.children.filter(child => child.type === 'directory')"
            :key="subDir.key"
        >
          <template #title>
            <a-icon type="folder"/>
            <span>{{ subDir.title }}</span>
          </template>
          <!-- 递归渲染子项 -->
        </a-sub-menu>
      </a-sub-menu>

      <!-- API项 -->
      <a-menu-item v-else-if="item.type === 'api'" :key="item.key">
        <a-tag :color="getMethodColor(item.method)">{{ item.method }}</a-tag>
        {{ item.title }}
      </a-menu-item>
    </template>
  </a-menu>
</template>

<script>
export default {
  data() {
    return {
      defaultOpenKeys: ['dir-1'],
      menuItems: [
        {
          key: 'dir-1',
          type: 'directory',
          title: '项目表',
          children: [
            {key: 'api-1', type: 'api', title: '示例接口', method: 'GET'},
            {key: 'api-2', type: 'api', title: '未命名接口', method: 'GET'},
            {key: 'api-3', type: 'api', title: '登录', method: 'GET'}
          ]
        },
        {
          key: 'dir-2',
          type: 'directory',
          title: '数据模型',
          children: []
        }
      ]
    };
  },
  methods: {
    handleMenuClick({key}) {
      // 处理菜单点击事件
      const item = this.findItemByKey(this.menuItems, key);
      if (item && item.type === 'api') {
        // 处理API点击
      }
    },
    findItemByKey(items, key) {
      for (const item of items) {
        if (item.key === key) return item;
        if (item.children) {
          const found = this.findItemByKey(item.children, key);
          if (found) return found;
        }
      }
      return null;
    },
    getMethodColor(method) {
      const colors = {
        'GET': 'green',
        'POST': 'blue',
        'PUT': 'orange',
        'DELETE': 'red'
      };
      return colors[method] || 'default';
    }
  }
}
</script>