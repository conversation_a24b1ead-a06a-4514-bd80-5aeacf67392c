<template>
  <a-row :gutter="16">
    <a-col :span="12">
      <a-card title="失败率最高的接口" :bordered="false">
        <a-table :columns="failureColumns" :data-source="topFailedApis" :pagination="false">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'failRate'">
              <a-progress
                :percent="record.failRate * 100"
                status="exception"
                size="small"
                :format="percent => `${percent.toFixed(1)}%`"
              />
            </template>
          </template>
        </a-table>
      </a-card>
    </a-col>
    <a-col :span="12">
      <a-card
        title="最近执行记录"
        :bordered="false"
        :tabList="[
          { key: 'recent', tab: '最近执行' },
          { key: 'scheduled', tab: '计划执行' }
        ]"
        v-model:activeTabKey="activeTabKey"
      >
        <a-table
          v-if="activeTabKey === 'recent'"
          :columns="executionColumns"
          :data-source="recentExecutions"
          :pagination="{ pageSize: 5 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ record.status }}
              </a-tag>
            </template>
            <template v-if="column.key === 'passRate'">
              <a-progress
                :percent="record.passRate * 100"
                :status="getProgressStatus(record.passRate)"
                size="small"
                :format="percent => `${percent.toFixed(1)}%`"
              />
            </template>
          </template>
        </a-table>
        <a-table
          v-else
          :columns="scheduledColumns"
          :data-source="scheduledExecutions"
          :pagination="{ pageSize: 5 }"
        />
      </a-card>
    </a-col>
  </a-row>
</template>

<script setup>
import { ref } from 'vue';

const props = defineProps({
  topFailedApis: {
    type: Array,
    required: true
  },
  recentExecutions: {
    type: Array,
    required: true
  },
  scheduledExecutions: {
    type: Array,
    required: true
  }
});

const activeTabKey = ref('recent');

const failureColumns = [
  {
    title: '接口名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '路径',
    dataIndex: 'url',
    key: 'url',
    ellipsis: true,
  },
  {
    title: '失败率',
    dataIndex: 'failRate',
    key: 'failRate',
  },
  {
    title: '执行次数',
    dataIndex: 'executionCount',
    key: 'executionCount',
  }
];

const executionColumns = [
  {
    title: '执行名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '环境',
    dataIndex: 'environment',
    key: 'environment',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '通过率',
    dataIndex: 'passRate',
    key: 'passRate',
  },
  {
    title: '执行时间',
    dataIndex: 'executeTime',
    key: 'executeTime',
  }
];

const scheduledColumns = [
  {
    title: '计划名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '环境',
    dataIndex: 'environment',
    key: 'environment',
  },
  {
    title: '计划时间',
    dataIndex: 'scheduledTime',
    key: 'scheduledTime',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
  }
];

const getStatusColor = (status) => {
  const colors = {
    'passed': 'success',
    'failed': 'error',
    'running': 'processing',
    'pending': 'warning'
  };
  return colors[status] || 'default';
};

const getProgressStatus = (rate) => {
  if (rate >= 0.8) return 'success';
  if (rate >= 0.6) return 'normal';
  return 'exception';
};
</script>