import {getRequest} from "/@/lib/axios.js";

export const DashboardApi = {
    getDashboardData(){
        return getRequest('/api_test/dashboard/statistics');
    },
    getTrendData(){
        return getRequest('/api_test/dashboard/trend');
    },
    getPassRateData(){
        return getRequest('/api_test/dashboard/pass-rate');
    },
    getCoverageData(){
        return getRequest('/api_test/dashboard/coverage');
    },
    getGroupData(){
        return getRequest('/api_test/dashboard/group-stats');
    },
    getTopFailedApis(){
        return getRequest('/api_test/dashboard/top-failed-apis');
    },
    getRecentExecutions(){
        return getRequest('/api_test/dashboard/recent-executions');
    },
    getScheduledExecutions(){
        return getRequest('/api_test/dashboard/scheduled-executions');
    },
    getHealthData(){
        return getRequest('/api_test/dashboard/health-data');
    }
};