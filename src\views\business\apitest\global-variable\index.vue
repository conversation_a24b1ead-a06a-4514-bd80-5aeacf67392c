<template>
  <div class="global-variables-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <a-page-header title="全局变量管理" sub-title="配置和管理全局变量">
        <template #extra>
          <a-button type="primary" @click="showVariableModal(null)">
            <template #icon><plus-outlined /></template>
            新增变量
          </a-button>
        </template>
      </a-page-header>
    </div>

    <!-- 搜索和过滤 -->
    <a-card :bordered="false" class="search-card">
      <a-form layout="inline">
        <a-form-item label="变量名称">
          <a-input
            v-model:value="queryForm.variableName"
            placeholder="搜索变量名称"
            allowClear
          />
        </a-form-item>
        <a-form-item label="变量值">
          <a-input
            v-model:value="queryForm.variableValue"
            placeholder="搜索变量名称"
            allowClear
          />
        </a-form-item>
        <a-form-item label="分类">
          <a-select
            v-model:value="queryForm.category"
            placeholder="选择分类"
            style="width: 180px"
            allowClear
          >
            <a-select-option v-for="category in categories" :key="category.id" :value="category.name">
              {{ category.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="变量状态">
          <a-select v-model:value="queryForm.status" placeholder="选择状态" style="width: 180px" allowClear>
            <a-select-option :value="true">启用</a-select-option>
            <a-select-option :value="false">禁用</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="fetchVariables">
            <template #icon><search-outlined /></template>
            搜索
          </a-button>
        </a-form-item>
        <a-form-item>
          <a-button @click="resetSearch">
            <template #icon><clear-outlined /></template>
            重置
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 变量列表 -->
    <a-card :bordered="false" class="variables-card">
      <template #title>
        <div class="card-title">
          <code-outlined />
          <span>全局变量列表</span>
        </div>
      </template>
      <template #extra>
        <a-button type="primary" size="small" @click="refreshVariables">
          <template #icon><reload-outlined /></template>
          刷新
        </a-button>
      </template>

      <a-table
        :dataSource="variableList"
        :loading="loading"
        rowKey="id"
        :pagination="{ pageSize: 10 }"
      >
        <a-table-column key="variableName" title="变量名称" dataIndex="variableName" />
        <a-table-column key="variableValue" title="变量值" dataIndex="variableValue" />
        <a-table-column key="category" title="分类" dataIndex="category">
          <template #default="{ text }">
            <a-tag :color="getCategoryColor(text)">{{ text }}</a-tag>
          </template>
        </a-table-column>
        <a-table-column key="status" title="状态" dataIndex="status">
          <template #default="{ text }">
            {{text ? '启用' : '禁用'}}
          </template>
        </a-table-column>
        <a-table-column key="desc" title="描述" dataIndex="description" />
        <a-table-column key="action" title="操作" width="200px">
          <template #default="{ record }">
            <a-space>
              <a-button type="primary" size="small" @click="showVariableModal(record)">
                <template #icon><edit-outlined /></template>
                编辑
              </a-button>
              <a-popconfirm
                title="确定要删除此变量吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="deleteVariable([record.variableId])"
              >
                <a-button type="danger" size="small">
                  <template #icon><delete-outlined /></template>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </a-table-column>
      </a-table>
    </a-card>

    <!-- 变量编辑对话框 -->
    <a-modal
      v-model:visible="variableModalVisible"
      :title="currentVariable.variableId ? '编辑变量' : '新增变量'"
      @ok="handleVariableOk"
      :destroyOnClose="true"
      width="600px"
    >
      <a-form :model="currentVariable" layout="vertical">
        <a-form-item label="变量名称" required>
          <a-input
            v-model:value="currentVariable.variableName"
            placeholder="变量名称，例如：API_KEY"
          />
        </a-form-item>

        <a-form-item label="变量值" required>
          <a-input
            v-model:value="currentVariable.variableValue"
            placeholder="变量值"
          />
        </a-form-item>

        <a-form-item label="分类">
          <a-select
            v-model:value="currentVariable.category"
            placeholder="选择分类"
            style="width: 100%"
          >
            <a-select-option v-for="category in categories" :key="category.id" :value="category.name">
              {{ category.name }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="描述">
          <a-textarea
            v-model:value="currentVariable.description"
            placeholder="变量描述"
            :rows="3"
          />
        </a-form-item>
        <a-form-item label="状态">
          <a-switch v-model:checked="currentVariable.status" />
        </a-form-item>
      </a-form>

      <div class="usage-help">
        <a-alert
          type="info"
          show-icon
          message="使用方法"
          description="在测试用例中，可以通过 ${global:变量名} 的形式引用全局变量"
        />
      </div>
    </a-modal>

    <!-- 分类管理对话框 -->
    <a-modal
      v-model:visible="categoryModalVisible"
      title="分类管理"
      @ok="handleCategoryOk"
      :destroyOnClose="true"
      width="500px"
    >
      <div v-for="(category, index) in categoryList" :key="`category-${index}`" class="category-item">
        <a-row :gutter="16" style="margin-bottom: 8px;">
          <a-col :span="18">
            <a-input
              v-model:value="category.name"
              placeholder="分类名称"
            />
          </a-col>
          <a-col :span="6">
            <a-button
              type="danger"
              @click="removeCategory(index)"
              style="width: 100%"
            >
              <template #icon><delete-outlined /></template>
              删除
            </a-button>
          </a-col>
        </a-row>
      </div>
      <a-button
        type="dashed"
        style="width: 100%; margin-top: 8px;"
        @click="addCategory"
      >
        <template #icon><plus-outlined /></template>
        添加分类
      </a-button>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, onMounted } from 'vue';
import {
  PlusOutlined, EditOutlined, DeleteOutlined, CodeOutlined,
  SearchOutlined, ClearOutlined, ReloadOutlined, SettingOutlined
} from '@ant-design/icons-vue';
import {globalVariableApi} from "/@/api/business/apitest/global-variable-api.js";
import {PAGE_SIZE} from "/@/constants/common-const.js";

export default defineComponent({
  components: {
    PlusOutlined,
    EditOutlined,
    DeleteOutlined,
    CodeOutlined,
    SearchOutlined,
    ClearOutlined,
    ReloadOutlined,
    SettingOutlined
  },
  setup() {
    // 变量列表
    const variableList = ref([]);
    const loading = ref(false);
    const pageQueryState = {
      pageNum: 1,
      pageSize: PAGE_SIZE,
      variableName:undefined,
      variableValue:undefined,
      category: undefined,
      status:undefined
    }
    const queryForm = reactive({...pageQueryState})
    // 当前编辑的变量
    const currentVariable = ref({
      variableId: null,
      variableName: '',
      variableValue: '',
      category: '',
      status:undefined,
      description: ''
    });

    // 搜索过滤
    const searchName = ref('');
    const selectedCategory = ref(null);

    // 分类
    const categories = ref([
      { id: 1, name: '认证' },
      { id: 2, name: '配置' },
      { id: 3, name: '业务参数' },
      { id: 4, name: '系统参数' }
    ]);

    // 分类管理
    const categoryList = ref([]);

    // 对话框显示状态
    const variableModalVisible = ref(false);
    const categoryModalVisible = ref(false);

    // 初始化数据
    onMounted(() => {
      fetchVariables();
    });

    // 获取变量列表
    const fetchVariables = async () => {
      loading.value = true;
      try {
        // 模拟API调用
        const response = await globalVariableApi.pageQuery(queryForm)

        // 示例数据
        variableList.value = response.data.items;
      } catch (error) {
        console.error('获取变量列表失败:', error);
      } finally {
        loading.value = false;
      }
    };

    // 重置搜索
    const resetSearch = () => {
      Object.assign(queryForm, pageQueryState);
      fetchVariables();
    };

    // 显示变量编辑对话框
    const showVariableModal = (variable) => {
      if (variable) {
        // 编辑模式
        currentVariable.value = JSON.parse(JSON.stringify(variable));
      } else {
        // 新增模式
        currentVariable.value = {
          id: null,
          name: '',
          value: '',
          category: categories.value.length > 0 ? categories.value[0].name : '',
          desc: '',
          status:true
        };
      }

      variableModalVisible.value = true;
    };

    // 保存变量
    const handleVariableOk = async () => {
      if (!currentVariable.value.variableName || !currentVariable.value.variableValue) {
        // 应该使用消息提示
        alert('请填写变量名称和变量值');
        return;
      }

      try {

        if (currentVariable.value.variableId) {
          // 编辑模式
          await globalVariableApi.update(currentVariable.value)
          await fetchVariables()
        } else {
          // 新增模式
          await globalVariableApi.add(currentVariable.value)
          await fetchVariables()
        }

      } catch (error) {
        console.error('保存变量失败:', error);
      }finally {
        variableModalVisible.value = false;
      }
    };

    // 删除变量
    const deleteVariable = async (ids) => {
      try {
        // 模拟API调用
        await globalVariableApi.delete(ids)
        await fetchVariables()

      } catch (error) {
        console.error('删除变量失败:', error);
      }
    };

    // 刷新变量列表
    const refreshVariables = () => {
      fetchVariables();
    };

    // 获取分类颜色
    const getCategoryColor = (category) => {
      const categoryMap = {
        '认证': 'green',
        '配置': 'blue',
        '业务参数': 'purple',
        '系统参数': 'orange'
      };

      return categoryMap[category] || 'default';
    };

    // 显示分类管理对话框
    const showCategoryModal = () => {
      categoryList.value = categories.value.map(c => ({ ...c }));
      categoryModalVisible.value = true;
    };

    // 添加分类
    const addCategory = () => {
      categoryList.value.push({
        id: Date.now(),
        name: ''
      });
    };

    // 移除分类
    const removeCategory = (index) => {
      categoryList.value.splice(index, 1);
    };

    // 保存分类
    const handleCategoryOk = async () => {
      try {
        // 验证分类名称不为空
        const emptyCategory = categoryList.value.find(c => !c.name.trim());
        if (emptyCategory) {
          // 应该使用消息提示
          alert('分类名称不能为空');
          return;
        }

        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500));

        // 更新分类列表
        categories.value = [...categoryList.value];
        categoryModalVisible.value = false;
      } catch (error) {
        console.error('保存分类失败:', error);
      }
    };

    // 格式化日期时间
    const formatDateTime = (dateStr) => {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      return date.toLocaleString();
    };

    return {
      variableList,
      loading,
      queryForm,
      currentVariable,
      searchName,
      selectedCategory,
      categories,
      categoryList,
      variableModalVisible,
      categoryModalVisible,
      fetchVariables,
      resetSearch,
      showVariableModal,
      handleVariableOk,
      deleteVariable,
      refreshVariables,
      getCategoryColor,
      showCategoryModal,
      addCategory,
      removeCategory,
      handleCategoryOk,
      formatDateTime
    };
  }
});
</script>

<style scoped>
.global-variables-container {
  padding: 20px;
  background-color: #f0f2f5;
  min-height: calc(100vh - 64px);
}

.page-header {
  margin-bottom: 20px;
  background-color: white;
  padding: 16px;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.search-card {
  margin-bottom: 20px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.variables-card {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.card-title {
  display: flex;
  align-items: center;
  font-weight: 500;
}

.card-title .anticon {
  margin-right: 8px;
  font-size: 16px;
}

.category-item {
  margin-bottom: 8px;
}

.usage-help {
  margin-top: 16px;
}
</style>