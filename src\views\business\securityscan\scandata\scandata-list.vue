

<template>
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="产品名称" class="smart-query-form-item">
        <a-input style="width: 300px" v-model:value="queryForm.productName" placeholder="产品名称" />
      </a-form-item>
      <a-form-item label="产品线" class="smart-query-form-item">
        <a-input style="width: 300px" v-model:value="queryForm.productLines" placeholder="产品名称" />
      </a-form-item>
      <a-form-item label="服务名称" class="smart-query-form-item">
        <a-input style="width: 300px" v-model:value="queryForm.serviceName" placeholder="产品名称" />
      </a-form-item>
      <a-form-item label="是否扫描" class="smart-query-form-item">
        <a-select
          v-model:value="queryForm.isre"
          placeholder="请选择是否扫描"
          style="width: 300px"
        >
          <a-select-option :value="1">是</a-select-option>
          <a-select-option :value="0">否</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item class="smart-query-form-item smart-margin-left10">
        <a-button-group>
          <a-button type="primary" @click="onSearch">
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </a-button>
          <a-button @click="resetQuery">
            <template #icon>
              <ReloadOutlined />
            </template>
            重置
          </a-button>
        </a-button-group>
      </a-form-item>
    </a-row>
  </a-form>

  <a-card size="small" :bordered="false" :hoverable="true">
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="add" type="primary">
          <template #icon>
            <PlusOutlined />
          </template>
          新建扫描数据
        </a-button>
        <a-button @click="exportExcel()" type="primary">
          <template #icon>
            <FileExcelOutlined />
          </template>
          导入
        </a-button>
      </div>
    </a-row>

    <a-table
      :scroll="{ x: 1300 }"
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="id"
      :pagination="false"
      :loading="tableLoading"
      bordered
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button @click="detail(record.enterpriseId)" size="small" type="link">{{record.isre?'禁用':'启用'}}</a-button>
            <a-button @click="showDrawer(record)" size="small" type="link">编辑</a-button>
            <a-button @click="confirmDelete(record.enterpriseId)" size="small" danger type="link">删除</a-button>
          </div>
        </template>
      </template>
    </a-table>
    <ScandataOperateModal ref="scanDataOperateModal"/>
    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="ajaxQuery"
        @showSizeChange="ajaxQuery"
        :show-total="(total) => `共${total}条`"
      />
    </div>
  </a-card>
</template>
<script setup>
  import { reactive, ref, onMounted } from 'vue';
  import { message, Modal } from 'ant-design-vue';
  import { PAGE_SIZE, PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import { useRouter } from 'vue-router';
  import { smartSentry } from '/@/lib/smart-sentry';
  import {scanDataApi} from "/@/api/business/securityscan/scandata-api.js";
  import ScandataOperateModal from "/src/views/business/securityscan/scandata/components/scandata-operate-modal.vue";

  // --------------------------- 企业表格 列 ---------------------------

  const columns = ref([
    {
      title: '产品名称',
      dataIndex: 'productName',
      minWidth: 20,
      ellipsis: true,
    },
    {
      title: '产品线',
      dataIndex: 'productLines',
      minWidth: 20,
      ellipsis: true,
    },
    {
      title: '服务名称',
      dataIndex: 'serviceName',
      width: 100,
    },
    {
      title: 'Git仓库地址',
      minWidth: 100,
      dataIndex: 'gitPath',
      ellipsis: true,
    },
    {
      title: 'Git类型',
      width: 120,
      dataIndex: 'gitType',
      ellipsis: true,
    },
    {
      title: '分支名称',
      minWidth: 100,
      dataIndex: 'typeName',
      ellipsis: true,
    },
    {
      title: '指定文件夹',
      minWidth: 50,
      dataIndex: 'commandDirectory',
    },
    {
      title: '镜像号',
      minWidth: 60,
      dataIndex: 'imgNum',
    },
    {
      title: '是否扫描',
      dataIndex: 'isre',
      minWidth: 20,
      customRender: ({ text }) => text ? '是' : '否'
    },
    {
      title: '操作',
      dataIndex: 'action',
      // fixed: 'right',
      minWidth: 100,
    },
  ]);

  // --------------------------- 查询 ---------------------------

  const queryFormState = {
    productName: '',
    pageNum: 1,
    pageSize: PAGE_SIZE,
    productLines:'',
    serviceName:'',
    isre:undefined
  };
  const queryForm = reactive({ ...queryFormState });
  const tableLoading = ref(false);
  const tableData = ref([]);
  const total = ref(0);

  // 日期选择
  let searchDate = ref();


  function onSearch() {
    queryForm.pageNum = 1;
    ajaxQuery();
  }

  function resetQuery() {
    searchDate.value = [];
    Object.assign(queryForm, queryFormState);
    ajaxQuery();
  }

  async function ajaxQuery() {
    try {
      tableLoading.value = true;
      let responseModel = await scanDataApi.pageQuery(queryForm);
      const list = responseModel.data.items;
      total.value = responseModel.data.total;
      tableData.value = list;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  // --------------------------- 删除 ---------------------------

  function confirmDelete(enterpriseId) {
    Modal.confirm({
      title: '确定要删除吗？',
      content: '删除后，该信息将不可恢复',
      okText: '删除',
      okType: 'danger',
      onOk() {
        console.log('OK');
      },
      cancelText: '取消',
      onCancel() {},
    });
  }

  // --------------------------- 增加、修改、详情 ---------------------------

  let router = useRouter();
  const operateRef = ref();
  const scanDataOperateModal = ref()
  const showDrawer = async (record) => {
    scanDataOperateModal.value.showDrawer(record);
  }

  function add() {
    scanDataOperateModal.value.showDrawer();
  }
  onMounted(ajaxQuery);
</script>
