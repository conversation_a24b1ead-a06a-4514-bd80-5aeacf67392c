<template>
  <a-dropdown
    :trigger="['click']"
    placement="bottomRight"
    :getPopupContainer="(triggerNode) => triggerNode.parentNode"
    v-model:visible="dropdownVisible"
    @visibleChange="handleVisibleChange"
    :destroyPopupOnHide="false"
  >
    <a class="ant-dropdown-link" @click.prevent.stop>
      <MoreOutlined />
    </a>
    <template #overlay>
      <a-menu @click="handleMenuClick">
        <a-menu-item key="add">
          <PlusOutlined />
          新增用例
        </a-menu-item>
        <a-menu-item key="copy">
          <CopyOutlined />
          复制分组
        </a-menu-item>
        <a-menu-item key="rename">
          <EditOutlined />
          重命名
        </a-menu-item>
        <a-menu-item key="delete">
          <DeleteOutlined />
          删除分组
        </a-menu-item>
      </a-menu>
    </template>
  </a-dropdown>

  <!-- 重命名模态框 -->
  <a-modal
    v-model:open="renameModalVisible"
    title="重命名分组"
    @ok="handleRenameConfirm"
    @cancel="handleRenameCancel"
    :maskClosable="false"
    :keyboard="false"
  >
    <div class="rename-modal-content">
      <a-form-item label="分组名称">
        <a-input
          v-model:value="newGroupName"
          placeholder="请输入新的分组名称"
          @pressEnter="handleRenameConfirm"
        />
      </a-form-item>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  MoreOutlined,
  PlusOutlined,
  CopyOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'

const props = defineProps({
  groupId: {
    type: Number,
    required: true
  },
  groupName: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['refresh', 'modalStateChange', 'addCase', 'renameGroup'])

const renameModalVisible = ref(false)
const newGroupName = ref('')
const dropdownVisible = ref(false)

const handleVisibleChange = (visible) => {
  console.log('Dropdown visible:', visible)
  dropdownVisible.value = visible
}

const handleMenuClick = ({ key }) => {
  console.log('Menu clicked:', key)
  
  switch (key) {
    case 'add':
      handleAddCase()
      break
    case 'copy':
      handleCopy()
      break
    case 'rename':
      handleRename()
      break
    case 'delete':
      handleDelete()
      break
  }
}

const handleAddCase = () => {
  console.log('新增用例到分组:', props.groupId)
  emit('addCase', props.groupId)
  message.info('新增用例功能待实现')
}

const handleCopy = () => {
  console.log('复制分组:', props.groupId)
  message.info('复制分组功能待实现')
}

const handleRename = () => {
  console.log('重命名分组:', props.groupId)
  newGroupName.value = props.groupName
  renameModalVisible.value = true
  // 通知父组件模态框状态变化
  emit('modalStateChange', true)
}

const handleDelete = () => {
  console.log('删除分组:', props.groupId)
  message.info('删除分组功能待实现')
}

const handleRenameCancel = () => {
  renameModalVisible.value = false
  newGroupName.value = ''
  // 通知父组件模态框状态变化
  emit('modalStateChange', false)
}

const handleRenameConfirm = () => {
  if (!newGroupName.value.trim()) {
    message.warning('请输入分组名称')
    return
  }

  console.log('确认重命名:', {
    groupId: props.groupId,
    oldName: props.groupName,
    newName: newGroupName.value
  })
  
  emit('renameGroup', {
    groupId: props.groupId,
    newName: newGroupName.value
  })
  
  message.success('重命名成功')
  renameModalVisible.value = false
  newGroupName.value = ''
  // 通知父组件模态框状态变化
  emit('modalStateChange', false)
}

// 监听模态框状态变化，确保在模态框打开时 dropdown 保持可见
watch(renameModalVisible, (newValue) => {
  if (newValue) {
    // 模态框打开时，确保 dropdown 保持可见
    dropdownVisible.value = true
  }
})
</script>

<style scoped>
.ant-dropdown-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 2px;
  color: #666;
  text-decoration: none;
}

.ant-dropdown-link:hover {
  background-color: #e6f7ff;
  color: #1890ff;
}

/* 确保图标不会抖动 */
.ant-dropdown-link .anticon {
  font-size: 12px;
  transition: none;
}

.rename-modal-content {
  padding: 16px 0;
}
</style>
