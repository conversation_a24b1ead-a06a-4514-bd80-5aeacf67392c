<!-- components/ResponseBodyViewer.vue -->
<template>
  <div class="response-body-viewer">
    <div class="viewer-toolbar">
      <a-radio-group v-model:value="localActiveView" @change="handleViewChange" button-style="solid" size="small">
        <a-radio-button value="pretty">格式化</a-radio-button>
        <a-radio-button value="raw">原始</a-radio-button>
        <a-radio-button value="preview" :disabled="!canPreview">预览</a-radio-button>
      </a-radio-group>

      <a-space>
        <template v-if="isJsonResponse && localActiveView === 'pretty'">
          <a-tooltip title="格式化">
            <a-button size="small" @click="formatJson">
              <format-painter-outlined />
            </a-button>
          </a-tooltip>
          <a-tooltip title="压缩">
            <a-button size="small" @click="compactJson">
              <small-dash-outlined />
            </a-button>
          </a-tooltip>
          <a-tooltip title="复制">
            <a-button size="small" @click="copyResponse">
              <copy-outlined />
            </a-button>
          </a-tooltip>
          <a-tooltip title="查找">
            <a-button size="small" @click="showSearch = !showSearch">
              <search-outlined />
            </a-button>
          </a-tooltip>
        </template>
      </a-space>
    </div>

    <div v-if="showSearch" class="search-bar">
      <a-input
        v-model:value="searchText"
        placeholder="搜索内容..."
        allow-clear
        @pressEnter="searchInResponse"
      >
        <template #suffix>
          <a-space>
            <span class="search-count" v-if="searchResults.length > 0">
              {{ currentSearchIndex + 1 }}/{{ searchResults.length }}
            </span>
            <up-outlined
              class="search-arrow"
              :class="{ disabled: searchResults.length === 0 }"
              @click="navigateSearch(-1)"
            />
            <down-outlined
              class="search-arrow"
              :class="{ disabled: searchResults.length === 0 }"
              @click="navigateSearch(1)"
            />
          </a-space>
        </template>
      </a-input>
    </div>

    <div class="viewer-content">
      <!-- 格式化视图 -->
      <div v-if="localActiveView === 'pretty'" class="pretty-view">
        <div
          ref="codeViewer"
          class="code-viewer"
          v-html="highlightedCode"
        ></div>
      </div>

      <!-- 原始视图 -->
      <div v-else-if="localActiveView === 'raw'" class="raw-view">
        <a-textarea
          v-model:value="responseBody"
          :rows="20"
          readonly
        />
      </div>

      <!-- 预览视图 -->
      <div v-else-if="localActiveView === 'preview'" class="preview-view">
        <iframe
          v-if="isHtmlResponse"
          :srcdoc="responseBody"
          class="preview-frame"
        ></iframe>
        <div v-else-if="isImageResponse" class="image-preview">
          <img :src="imageDataUrl" alt="Image Preview" />
        </div>
        <a-empty v-else description="无法预览此类型的响应" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, defineProps, defineEmits } from 'vue';
import { message } from 'ant-design-vue';
import hljs from 'highlight.js';
import 'highlight.js/styles/atom-one-dark.css';
import {
  FormatPainterOutlined,
  SmallDashOutlined,
  CopyOutlined,
  SearchOutlined,
  UpOutlined,
  DownOutlined
} from '@ant-design/icons-vue';

const props = defineProps({
  response: {
    type: Object,
    default: null
  },
  activeView: {
    type: String,
    default: 'pretty'
  }
});

const emit = defineEmits(['change-view']);

// 本地状态
const localActiveView = ref(props.activeView);
const showSearch = ref(false);
const searchText = ref('');
const searchResults = ref([]);
const currentSearchIndex = ref(-1);
const codeViewer = ref(null);

// 监听属性变化
watch(() => props.activeView, (newValue) => {
  localActiveView.value = newValue;
});

// 响应体内容
const responseBody = computed(() => {
  if (!props.response || !props.response.body) return '';
  return props.response.body;
});

// 响应类型判断
const contentType = computed(() => {
  if (!props.response || !props.response.headers) return '';
  return (props.response.headers['content-type'] || '').toLowerCase();
});

const isJsonResponse = computed(() => {
  return contentType.value.includes('application/json');
});

const isHtmlResponse = computed(() => {
  return contentType.value.includes('text/html');
});

const isImageResponse = computed(() => {
  return contentType.value.includes('image/');
});

const canPreview = computed(() => {
  return isHtmlResponse.value || isImageResponse.value;
});

// 图片数据URL
const imageDataUrl = computed(() => {
  if (!isImageResponse.value || !responseBody.value) return '';

  // 构建 data URL
  return `data:${contentType.value};base64,${responseBody.value}`;
});

// 高亮处理后的代码
const highlightedCode = computed(() => {
  if (!responseBody.value) return '';

  try {
    let code = responseBody.value;
    let language = 'plaintext';

    if (isJsonResponse.value) {
      // 格式化JSON
      const parsed = JSON.parse(code);
      code = JSON.stringify(parsed, null, 2);
      language = 'json';
    } else if (contentType.value.includes('xml')) {
      language = 'xml';
    } else if (contentType.value.includes('html')) {
      language = 'html';
    } else if (contentType.value.includes('javascript')) {
      language = 'javascript';
    } else if (contentType.value.includes('css')) {
      language = 'css';
    }

    const highlighted = hljs.highlight(code, { language }).value;
    return `<pre><code class="hljs language-${language}">${highlighted}</code></pre>`;
  } catch (e) {
    return `<pre><code>${responseBody.value}</code></pre>`;
  }
});

// 处理视图切换
const handleViewChange = (e) => {
  emit('change-view', e.target.value);
};

// 格式化JSON
const formatJson = () => {
  if (!isJsonResponse.value) return;

  try {
    const parsed = JSON.parse(responseBody.value);
    props.response.body = JSON.stringify(parsed, null, 2);
    message.success('JSON已格式化');
  } catch (error) {
    message.error('JSON格式错误');
  }
};

// 压缩JSON
const compactJson = () => {
  if (!isJsonResponse.value) return;

  try {
    const parsed = JSON.parse(responseBody.value);
    props.response.body = JSON.stringify(parsed);
    message.success('JSON已压缩');
  } catch (error) {
    message.error('JSON格式错误');
  }
};

// 复制响应内容
const copyResponse = () => {
  navigator.clipboard.writeText(responseBody.value)
    .then(() => {
      message.success('响应内容已复制到剪贴板');
    })
    .catch(() => {
      message.error('复制失败，请手动复制');
    });
};

// 在响应中搜索
const searchInResponse = () => {
  if (!searchText.value || !responseBody.value) {
    searchResults.value = [];
    currentSearchIndex.value = -1;
    return;
  }

  // 简单实现：找出所有匹配项的索引
  const text = responseBody.value.toLowerCase();
  const searchTerm = searchText.value.toLowerCase();
  const results = [];
  let index = -1;

  while ((index = text.indexOf(searchTerm, index + 1)) !== -1) {
    results.push(index);
  }

  searchResults.value = results;
  currentSearchIndex.value = results.length > 0 ? 0 : -1;

  // 高亮第一个匹配项
  highlightCurrentSearch();
};

// 高亮当前搜索结果
const highlightCurrentSearch = () => {
  if (!codeViewer.value || currentSearchIndex.value < 0 || searchResults.value.length === 0) return;

  nextTick(() => {
    // 移除之前的高亮
    const prevHighlights = codeViewer.value.querySelectorAll('.search-highlight');
    prevHighlights.forEach(el => {
      const parent = el.parentNode;
      parent.replaceChild(document.createTextNode(el.textContent), el);
    });

    // 创建新的高亮
    const container = codeViewer.value;
    const textNodes = getTextNodes(container);
    let charCount = 0;
    let targetIndex = searchResults.value[currentSearchIndex.value];
    let targetLength = searchText.value.length;

    for (const node of textNodes) {
      const nodeLength = node.textContent.length;

      // 检查当前节点是否包含目标索引
      if (targetIndex >= charCount && targetIndex < charCount + nodeLength) {
        const startOffset = targetIndex - charCount;

        // 处理跨节点的情况
        if (startOffset + targetLength <= nodeLength) {
          // 目标完全在当前节点内
          highlightNodePortion(node, startOffset, targetLength);
        } else {
          // 目标跨越多个节点（简化处理）
          const firstPortion = nodeLength - startOffset;
          highlightNodePortion(node, startOffset, firstPortion);
        }

        // 滚动到高亮位置
        const highlight = container.querySelector('.search-highlight');
        if (highlight) {
          highlight.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });
        }

        break;
      }

      charCount += nodeLength;
    }
  });
};

// 获取所有文本节点
const getTextNodes = (node) => {
  const textNodes = [];

  const walkTree = (node) => {
    if (node.nodeType === 3) { // 文本节点
      textNodes.push(node);
    } else {
      for (const child of node.childNodes) {
        walkTree(child);
      }
    }
  };

  walkTree(node);
  return textNodes;
};

// 高亮节点中的一部分文本
const highlightNodePortion = (textNode, start, length) => {
  const text = textNode.textContent;
  const before = text.substring(0, start);
  const highlight = text.substring(start, start + length);
  const after = text.substring(start + length);

  const parent = textNode.parentNode;

  // 创建前部分
  if (before) {
    parent.insertBefore(document.createTextNode(before), textNode);
  }

  // 创建高亮部分
  const highlightSpan = document.createElement('span');
  highlightSpan.className = 'search-highlight';
  highlightSpan.textContent = highlight;
  parent.insertBefore(highlightSpan, textNode);

  // 创建后部分
  if (after) {
    parent.insertBefore(document.createTextNode(after), textNode);
  }

  // 移除原文本节点
  parent.removeChild(textNode);
};

// 搜索结果导航
const navigateSearch = (direction) => {
  if (searchResults.value.length === 0) return;

  // 更新索引
  const newIndex = currentSearchIndex.value + direction;
  if (newIndex < 0) {
    currentSearchIndex.value = searchResults.value.length - 1;
  } else if (newIndex >= searchResults.value.length) {
    currentSearchIndex.value = 0;
  } else {
    currentSearchIndex.value = newIndex;
  }

  // 高亮当前搜索结果
  highlightCurrentSearch();
};

// 监听搜索文本变化
watch(searchText, (newValue) => {
  if (newValue) {
    searchInResponse();
  } else {
    searchResults.value = [];
    currentSearchIndex.value = -1;

    // 清除高亮
    if (codeViewer.value) {
      const highlights = codeViewer.value.querySelectorAll('.search-highlight');
      highlights.forEach(el => {
        const parent = el.parentNode;
        parent.replaceChild(document.createTextNode(el.textContent), el);
      });
    }
  }
});
</script>

<style scoped>
.response-body-viewer {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.viewer-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.search-bar {
  margin-bottom: 12px;
}

.search-count {
  font-size: 12px;
  color: #666;
  margin-right: 4px;
}

.search-arrow {
  cursor: pointer;
  color: #1890ff;
}

.search-arrow.disabled {
  color: #d9d9d9;
  cursor: not-allowed;
}

.viewer-content {
  flex: 1;
  overflow: auto;
}

.pretty-view {
  height: 100%;
}

.code-viewer {
  height: 100%;
  overflow: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
  font-size: 13px;
  line-height: 1.5;
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 2px;
  background-color: #fafafa;
}

.raw-view {
  height: 100%;
}

.raw-view textarea {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
  font-size: 13px;
}

.preview-view {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-frame {
  flex: 1;
  border: 1px solid #e8e8e8;
  border-radius: 2px;
}

.image-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  border: 1px solid #e8e8e8;
  border-radius: 2px;
  padding: 16px;
  background-color: #fafafa;
}

.image-preview img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

:deep(.search-highlight) {
  background-color: #ffec3d;
  color: #000;
  border-radius: 2px;
}
</style>