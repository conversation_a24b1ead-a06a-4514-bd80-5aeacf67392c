<template>
  <a-tree-select
    v-model:value="groupId"
    :treeDefaultExpandedKeys="selectedKeys"
    :tree-data="groupTree"
    :field-names="fieldNames"
    @select="onSelect"
  />
</template>

<script setup>
import {computed, onMounted, ref, watch} from "vue";
import {GroupApi} from "/@/api/business/apitest/group-api.js";
import {message} from "ant-design-vue";

const props = defineProps({
  groupType:{
    type: String,
    default: null
  },
  groupKey:{
    type: [Number,null],
    required: false,
  }
})
const emits = defineEmits(['update:group-key'])
const groupId = ref(props.groupKey)
const groupTree = ref([])
const fieldNames = {
  children: 'children',
  label: 'name',
  value: 'groupId'
};
const processTreeDataWithPaths = (tree,targetKey, path=[]) => {
  for (const node of tree){
    if (node.groupId===targetKey){
      return [...path]
    }
    if (node.children && node.children.length > 0){
      const result = processTreeDataWithPaths(node.children, targetKey, [...path, node.groupId]);
      if (result) return result;
    }
  }
  return [];
};

const selectedKeys = ref(processTreeDataWithPaths(groupTree.value,props.groupKey))

function onSelect(value, node, extra) {
  emits('update:group-key',value)
}

const loadGroupTree = async () => {
  try{
    const res = await GroupApi.getGroupTree({groupType:props.groupType})
    groupTree.value = res.data
  }catch (e) {
    message.error("获取目录失败")
  }
}

watch(()=>props.groupType,async (newValue)=>{
  await loadGroupTree()
})
watch(()=>[groupTree.value,props.groupKey],()=>{
  selectedKeys.value = processTreeDataWithPaths(groupTree.value,props.groupKey)
})

onMounted(async ()=>{
  await loadGroupTree()
})

</script>