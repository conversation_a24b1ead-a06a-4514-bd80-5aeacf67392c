<!-- components/ResponseTimelineViewer.vue -->
<template>
  <div class="timeline-viewer">
    <a-empty v-if="!response || !response.timeline" description="没有时间轴数据" />

    <template v-else>
      <div class="timeline-summary">
        <div class="summary-item">
          <div class="summary-label">总响应时间</div>
          <div class="summary-value">{{ response.time }} ms</div>
        </div>
        <div class="summary-item">
          <div class="summary-label">TTFB</div>
          <div class="summary-value">{{ getTTFB() }} ms</div>
          <div class="summary-description">首字节时间</div>
        </div>
        <div class="summary-item">
          <div class="summary-label">DNS 查询</div>
          <div class="summary-value">{{ getDNSTime() }} ms</div>
        </div>
        <div class="summary-item">
          <div class="summary-label">TLS 握手</div>
          <div class="summary-value">{{ getTLSTime() }} ms</div>
        </div>
      </div>

      <div class="timeline-bars">
        <div class="timeline-label">时间轴</div>
        <div class="timeline-bar">
          <div
            v-for="(phase, index) in response.timeline"
            :key="index"
            class="timeline-segment"
            :class="getPhaseClass(phase.phase)"
            :style="getPhaseStyle(phase)"
            :title="`${phase.phase}: ${phase.duration}ms`"
          ></div>
        </div>
        <div class="timeline-legend">
          <div class="legend-item">
            <div class="legend-color dns"></div>
            <div class="legend-label">DNS 查询</div>
          </div>
          <div class="legend-item">
            <div class="legend-color connect"></div>
            <div class="legend-label">TCP 连接</div>
          </div>
          <div class="legend-item">
            <div class="legend-color ssl"></div>
            <div class="legend-label">TLS 握手</div>
          </div>
          <div class="legend-item">
            <div class="legend-color wait"></div>
            <div class="legend-label">等待响应</div>
          </div>
          <div class="legend-item">
            <div class="legend-color download"></div>
            <div class="legend-label">下载内容</div>
          </div>
        </div>
      </div>

      <a-table
        :dataSource="response.timeline"
        :columns="columns"
        :pagination="false"
        size="small"
        bordered
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'duration'">
            {{ record.duration }} ms
          </template>
          <template v-else-if="column.dataIndex === 'percentage'">
            <a-progress
              :percent="getPhasePercentage(record)"
              :stroke-color="getPhaseColor(record.phase)"
              :show-info="false"
              size="small"
              style="width: 100px"
            />
            {{ getPhasePercentage(record).toFixed(1) }}%
          </template>
        </template>
      </a-table>
    </template>
  </div>
</template>

<script setup>
import { defineProps, computed } from 'vue';

const props = defineProps({
  response: {
    type: Object,
    default: null
  }
});

// 表格列定义
const columns = [
  { title: '阶段', dataIndex: 'phase', width: '40%' },
  { title: '耗时', dataIndex: 'duration', width: '20%' },
  { title: '占比', dataIndex: 'percentage', width: '40%' }
];

// 获取首字节时间
const getTTFB = () => {
  if (!props.response || !props.response.timeline) return 0;

  // TTFB通常是DNS + 连接 + SSL + 发送请求 + 等待响应的前部分
  const ttfbPhases = ['DNS Lookup', 'Initial Connection', 'SSL Handshake', 'Request Sent', 'Waiting (TTFB)'];

  let ttfb = 0;
  props.response.timeline.forEach(phase => {
    if (ttfbPhases.includes(phase.phase)) {
      ttfb += phase.duration;
    }
  });

  return ttfb;
};

// 获取DNS查询时间
const getDNSTime = () => {
  if (!props.response || !props.response.timeline) return 0;

  const dnsPhase = props.response.timeline.find(phase => phase.phase === 'DNS Lookup');
  return dnsPhase ? dnsPhase.duration : 0;
};

// 获取TLS握手时间
const getTLSTime = () => {
  if (!props.response || !props.response.timeline) return 0;

  const tlsPhase = props.response.timeline.find(phase => phase.phase === 'SSL Handshake');
  return tlsPhase ? tlsPhase.duration : 0;
};

// 获取阶段的CSS类
const getPhaseClass = (phase) => {
  const classMap = {
    'DNS Lookup': 'dns',
    'Initial Connection': 'connect',
    'SSL Handshake': 'ssl',
    'Request Sent': 'send',
    'Waiting (TTFB)': 'wait',
    'Content Download': 'download'
  };

  return classMap[phase] || '';
};

// 获取阶段的颜色
const getPhaseColor = (phase) => {
  const colorMap = {
    'DNS Lookup': '#faad14',
    'Initial Connection': '#1890ff',
    'SSL Handshake': '#722ed1',
    'Request Sent': '#fa8c16',
    'Waiting (TTFB)': '#52c41a',
    'Content Download': '#13c2c2'
  };

  return colorMap[phase] || '#d9d9d9';
};

// 获取阶段的样式
const getPhaseStyle = (phase) => {
  if (!props.response || !props.response.time || !phase.duration) return {};

  const totalTime = props.response.time;
  const width = (phase.duration / totalTime) * 100;

  // 计算位置（根据timeline数组中的顺序）
  let position = 0;
  for (let i = 0; i < props.response.timeline.length; i++) {
    const current = props.response.timeline[i];
    if (current === phase) break;
    position += (current.duration / totalTime) * 100;
  }

  return {
    width: `${width}%`,
    left: `${position}%`,
    backgroundColor: getPhaseColor(phase.phase)
  };
};

// 计算阶段占总时间的百分比
const getPhasePercentage = (phase) => {
  if (!props.response || !props.response.time || !phase.duration) return 0;

  return (phase.duration / props.response.time) * 100;
};
</script>

<style scoped>
.timeline-viewer {
  display: flex;
  flex-direction: column;
}

.timeline-summary {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.summary-item {
  flex: 1;
  min-width: 120px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 4px;
  margin-right: 8px;
  margin-bottom: 8px;
}

.summary-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.summary-value {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.summary-description {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.timeline-bars {
  margin-bottom: 24px;
}

.timeline-label {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.timeline-bar {
  height: 24px;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  margin-bottom: 8px;
}

.timeline-segment {
  height: 100%;
  position: absolute;
  top: 0;
}

.timeline-legend {
  display: flex;
  flex-wrap: wrap;
  font-size: 12px;
  color: #666;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 4px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 4px;
}

.legend-color.dns {
  background-color: #faad14;
}

.legend-color.connect {
  background-color: #1890ff;
}

.legend-color.ssl {
  background-color: #722ed1;
}

.legend-color.send {
  background-color: #fa8c16;
}

.legend-color.wait {
  background-color: #52c41a;
}

.legend-color.download {
  background-color: #13c2c2;
}
</style>