<template>
  <a-modal
  title="集合用例"
  :open="open"
  @cancel="onCancel"
  @ok="save"
  width="800px"
  >
    <a-card size="small" :bordered="false" :hoverable="true">
      <a-row class="smart-table-btn-block">
        <div class="smart-table-operate-block">
          <a-button @click="addCaseData" type="primary">
            <template #icon>
              <PlusOutlined />
            </template>
            添加用例
          </a-button>
        </div>
      </a-row>
      <a-table
      :columns="columns"
      :dataSource="tableData"
      :pagination="false"
      :rowKey="record => record.id"
      :rowSelection="{
        type: 'radio',
        selectedRowKeys: [selectedCaseId],
        onChange: (selectedRowKeys, selectedRows) => {
          setSelectedCaseId(selectedRows[0].id)
        }
      }"
      >
        <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button @click="confirmDelete(record.suiteId)" size="small" danger type="link">删除</a-button>
          </div>
        </template>
        <template v-if="column.dataIndex === 'tags'">
          <Tag v-for="tag in record.tags" :key="tag" color="blue">{{tag}}</Tag>
        </template>
      </template>
    </a-table>
    </a-card>
    <case-tree-modal ref="caseTreeRef"/>
  </a-modal>
</template>

<script setup>

import {h, onMounted, ref} from "vue";
import {message, Tag} from "ant-design-vue";
import {CaseSuiteApi} from "/@/api/business/apitest/case-suite-api.js";
import CaseTreeModal from "/@/views/business/apitest/case/components/case-tree-modal.vue";

const currentSuiteId = ref(null)
const caseTreeRef = ref(null)
const tableData = ref([])
const open = ref(false)
const columns = ref([
    {
      title: '用例名称',
      dataIndex: 'caseName',
      minWidth: 20,
      ellipsis: true,
    },
      {
      title: '优先级',
      minWidth: 100,
      dataIndex: 'priority',
      ellipsis: true,
      customRender: ({text, record}) => {
        const colors = {
          1: '#f5222d', // 高-红色
          2: '#faad14', // 中-黄色
          3: '#52c41a'  // 低-绿色
        };
        const priority = {
          1: '高',
          2: '中',
          3: '低'
        }
        return h(Tag, { color: colors[text] || '#d9d9d9' }, () => priority[text]);
      },
    },
    {
      title: '标签',
      minWidth: 120,
      dataIndex: 'tags',
      ellipsis: true
    },
      {
      title: '描述',
      minWidth: 100,
      dataIndex: 'desc',
      ellipsis: true,
    },
    {
      title: '状态',
      minWidth: 120,
      dataIndex: 'status',
      ellipsis: true,
      customRender: ({text, record}) => {
        return text === true ? '启用' : '禁用';
      },
    }
])

async function loadData(suiteId) {
  if (!suiteId) return
  try{
    const response = await CaseSuiteApi.get(suiteId)
    tableData.value = response.data
  }catch (e) {
    message.error('加载集合用例失败')
  }
}

function callBack(caseData){
  console.log('回调数据', caseData)
  console.log('tableData', tableData.value)
  for(const item of tableData.value){
    if(item.caseId === caseData.caseId){
      message.error('该用例已存在')
      return
    }
  }
  tableData.value.push(caseData)
}
const addCaseData = () => {
  caseTreeRef.value.show(callBack)
}
const addSuiteData = async () => {
  if (!currentSuiteId.value) return
  const caseIDs = tableData.value.map(item => item.caseId)
  await CaseSuiteApi.add({suiteId: currentSuiteId.value, caseIds: caseIDs})
}

const onCancel = () => {
  open.value = false
}
const save = async () => {
  await addSuiteData()
  onCancel()
}
const showModal = async (suiteId) => {
  currentSuiteId.value = suiteId
  await loadData(suiteId)
  open.value = true
}
defineExpose({showModal})

</script>