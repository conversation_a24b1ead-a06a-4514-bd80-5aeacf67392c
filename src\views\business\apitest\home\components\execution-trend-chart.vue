<template>
  <a-card title="测试执行趋势" :bordered="false">
    <div ref="executionTrendChart" style="height: 300px"></div>
  </a-card>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  trendData: {
    type: Array,
    required: true
  }
});

const executionTrendChart = ref(null);
let chart = null;

const initChart = () => {
  if (!executionTrendChart.value) return;

  chart = echarts.init(executionTrendChart.value);
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['总执行数', '成功数', '失败数']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: props.trendData.map(item => item.date)
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '总执行数',
        type: 'line',
        data: props.trendData.map(item => item.total)
      },
      {
        name: '成功数',
        type: 'line',
        data: props.trendData.map(item => item.passed)
      },
      {
        name: '失败数',
        type: 'line',
        data: props.trendData.map(item => item.failed)
      }
    ]
  };

  chart.setOption(option);
};

watch(() => props.trendData, () => {
  if (chart) {
    initChart();
  }
}, { deep: true });

onMounted(() => {
  initChart();
  window.addEventListener('resize', () => {
    chart?.resize();
  });
});
</script>