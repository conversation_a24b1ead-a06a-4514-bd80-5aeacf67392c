<template>
  <div class="case-detail-container">
    <!-- 页面头部 -->
    <a-page-header
      :title="isEdit ? '编辑用例' : '新建用例'"
      @back="goBack"
    >
      <template #extra>
        <a-space>
          <a-button type="primary" @click="saveCase" :loading="saving">保存</a-button>
          <a-button @click="goBack">取消</a-button>
        </a-space>
      </template>
    </a-page-header>

    <!-- 基本信息卡片 -->
    <a-card class="section-card" title="基本信息" :bordered="false">
      <a-form
        ref="basicForm"
        :model="caseData"
        layout="vertical"
      >
        <!-- 表单内容保持不变 -->
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="用例名称" name="caseName">
              <a-input v-model:value="caseData.caseName" placeholder="请输入用例名称" />
            </a-form-item>
          </a-col>
          <a-col :span="5">
            <a-form-item label="所属模块" name="moduleId">
              <group-tree-cascader v-model:group-id="caseData.groupId" />
            </a-form-item>
          </a-col>
          <a-col :span="5">
            <a-form-item label="优先级" name="priority">
              <a-select v-model:value="caseData.priority">
                <a-select-option
                  v-for="option in priorityOptions"
                  :key="option.value"
                  :value="option.value"
                >
                  <span v-if="option.icon">
                    <component :is="option.icon" style="margin-right: 8px;color:red"/>
                    {{ option.label }}
                  </span>
                  <span v-else>
                    {{ option.label }}
                  </span>
                  <span style="color: #888; margin-left: 8px; font-size: 0.85em">
                    {{ option.title }}
                  </span>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="标签" name="tags">
              <a-select
                v-model:value="tags"
                mode="tags"
                :token-separators="[',']"
                placeholder="请输入标签"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="2">
            <a-form-item label="状态" name="status">
              <a-switch
                v-model:checked="caseData.status"
                :checked-value="true"
                :unchecked-value="false"
                checked-children="启用"
                un-checked-children="禁用"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item label="描述" name="desc">
          <a-textarea
            v-model:value="caseData.desc"
            placeholder="请输入用例描述"
            :rows="2"
          />
        </a-form-item>
      </a-form>
    </a-card>

    <!-- API步骤卡片 - 使用 a-table + Sortable.js -->
    <a-card class="section-card" title="API步骤" :bordered="false">
      <div class="api-steps-header">
        <a-button type="primary" @click="handleAddStep">
          <template #icon><plus-outlined /></template>
          添加API步骤
        </a-button>
      </div>

      <!-- 空状态 -->
      <div v-if="!caseData.apiSteps || caseData.apiSteps.length === 0" class="api-steps-empty">
        <a-empty description="暂无API步骤">
          <template #description>
            <span>
              您还没有添加任何API步骤
              <a @click="handleAddStep">现在添加</a>
            </span>
          </template>
        </a-empty>
      </div>

      <!-- API步骤表格 -->
      <div v-else class="table-container">
        <a-table
          ref="apiStepsTable"
          :dataSource="caseData.apiSteps"
          :columns="columns"
          :pagination="false"
          rowKey="apiCaseId"
        >
          <!-- 拖拽列 -->
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'drag'">
              <menu-outlined class="step-drag-handle" />
            </template>
            <template v-else-if="column.dataIndex === 'method'">
              <span class="step-method" :class="record.apiInfo?.method">
                {{ record.apiInfo?.method?.toUpperCase() || '-' }}
              </span>
            </template>
            <template v-else-if="column.dataIndex === 'name'">
              {{ record.apiInfo?.name || '未选择API' }}
            </template>
            <template v-else-if="column.dataIndex === 'url'">
              <span class="step-url">{{ record.apiInfo?.url || '' }}</span>
            </template>
            <template v-else-if="column.dataIndex === 'action'">
              <a-space>
                <a-button type="text" @click="editStep(record, index)">
                  <template #icon><edit-outlined /></template>
                </a-button>
                <a-button type="text" danger @click="confirmRemoveStep(record, index)">
                  <template #icon><delete-outlined /></template>
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </a-card>

    <!-- 前置后置操作卡片 -->
    <a-card title="前置操作" :bordered="false" class="section-card">
      <operations-modal operation-title="前置操作" :operation-options="prefixOperations" v-model:operations="caseData.prefixOperations"/>
    </a-card>
    <a-card title="后置操作" :bordered="false" class="section-card">
      <operations-modal operation-title="后置操作" :operation-options="suffixOperations" v-model:operations="caseData.suffixOperations"/>
    </a-card>

    <!-- API选择模态框 -->
    <api-tree-modal ref="apiTreeModalRef" />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick, watch, h } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import {
  PlusOutlined,
  MenuOutlined,
  EditOutlined,
  DeleteOutlined,
  UpOutlined,
  DownOutlined,
  FireOutlined,
  AlertOutlined,
  CheckCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons-vue';
import { v4 as uuidv4 } from 'uuid';
import Sortable from 'sortablejs';
import { CaseApi } from '/src/api/business/apitest/case-api.js';
import { ModuleApi } from '/src/api/business/apitest/module-api.js';
import { smartSentry } from '/src/lib/smart-sentry.js';
import GroupTreeCascader from "/src/views/business/apitest/group/group-tree-cascader.vue";
import ApiTreeModal from "/src/views/business/apitest/api/components/api-tree-modal.vue";
import OperationsModal from "/src/views/business/apitest/case/components/operations-modal.vue";
import {prefixOperations, suffixOperations} from "/src/views/business/apitest/case/components/operationColumns.js";

// 路由相关
const route = useRoute();
const router = useRouter();
const caseId = computed(() => route.query.caseId);
const isEdit = computed(() => !!caseId.value);

// 基础表单
const basicForm = ref(null);
const saving = ref(false);

// API树选择模态框
const apiTreeModalRef = ref(null);

// 步骤表格引用
const apiStepsTable = ref(null);
let sortableInstance = null;

// 步骤状态管理
const expandedStepKeys = ref([]); // 展开的步骤
const editingStepId = ref(null);  // 当前编辑的步骤

const tags = computed({
  get: () => Array.isArray(caseData.tags) ? caseData.tags : [],
  set: (value) => {
    caseData.tags = value;
  }
});


// 用例数据
const caseData = reactive({
  caseId: null,
  groupId: null,
  caseName: '',
  priority: 3,
  tags: [],
  desc: '',
  status: true,
  prefixOperations: [],
  suffixOperations: [],
  apiSteps: []
});

// 表格列定义
const columns = [
  {
    title: '拖拽',
    dataIndex: 'drag',
    width: 60,
    align: 'center',
    customRender: () => h(MenuOutlined, { class: 'step-drag-handle' })
  },
  {
    title: '步骤',
    dataIndex: 'stepOrder',
    width: 70,
    align: 'center'
  },
  {
    title: '方法',
    dataIndex: 'method',
    width: 90,
    align: 'center'
  },
  {
    title: 'API名称',
    dataIndex: 'name',
    ellipsis: true
  },
  {
    title: '请求URL',
    dataIndex: 'url',
    ellipsis: true
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 120,
    fixed: 'right',
    align: 'center'
  }
];

// 优先级选项
const priorityOptions = ref([
  {
    value: 1,
    label: 'P0',
    icon: FireOutlined,
    title: '核心'
  },
  {
    value: 2,
    label: 'P1',
    icon: AlertOutlined,
    title:'高'
  },
  {
    value: 3,
    label: 'P2',
    icon: CheckCircleOutlined,
    title: '中'
  },
  {
    value: 4,
    label: 'P3',
    icon: InfoCircleOutlined,
    title: '低'
  }
]);

// 获取API方法颜色
const getMethodColor = (method) => {
  const methodColors = {
    'get': 'green',
    'post': 'blue',
    'put': 'orange',
    'delete': 'red',
    'patch': 'purple'
  };
  return methodColors[method?.toLowerCase()] || 'default';
};

// 初始化Sortable
const initSortable = () => {
  nextTick(() => {
    // 获取表格的tbody
    const tbody = document.querySelector('.table-container table tbody');
    if (!tbody) return;

    // 销毁之前的实例
    if (sortableInstance) {
      sortableInstance.destroy();
    }

    // 创建新实例
    sortableInstance = Sortable.create(tbody, {
      animation: 150,
      handle: '.step-drag-handle',
      ghostClass: 'step-ghost',
      chosenClass: 'step-chosen',
      onEnd: (evt) => {
        const { oldIndex, newIndex } = evt;
        if (oldIndex !== newIndex) {
          // 更新数据顺序
          const itemToMove = caseData.apiSteps.splice(oldIndex, 1)[0];
          caseData.apiSteps.splice(newIndex, 0, itemToMove);

          // 更新步骤顺序
          updateStepOrders();
        }
      }
    });
  });
};

// 添加API步骤
const handleAddStep = () => {
  apiTreeModalRef.value.show((api) => {
    console.log('选择的API:', api);

    // 创建新步骤
    const newStep = {
      apiId: api.apiId,
      caseId: caseData.caseId,
      stepOrder: caseData.apiSteps.length + 1,
      apiInfo: api
    };

    // 添加到步骤列表
    caseData.apiSteps.push(newStep);

    // 自动展开新添加的步骤
    expandedStepKeys.value.push(newStep.apiCaseId);

    message.success(`已添加API步骤: ${api.name}`);

    // 重新初始化Sortable
    nextTick(() => {
      initSortable();
    });
  });
};

// 编辑步骤
const editStep = (step, index) => {
  editingStepId.value = step.apiCaseId;

  // 确保步骤被展开
  if (!expandedStepKeys.value.includes(step.apiCaseId)) {
    expandedStepKeys.value.push(step.apiCaseId);
  }
};

// 保存步骤编辑
const saveStepEdit = (step) => {
  // 可以在这里添加步骤数据验证逻辑
  editingStepId.value = null;
  message.success('步骤已保存');
};

// 取消步骤编辑
const cancelStepEdit = () => {
  editingStepId.value = null;
};

// 检查步骤是否正在编辑
const isStepEditing = (stepId) => {
  return editingStepId.value === stepId;
};


// 确认删除步骤
const confirmRemoveStep = (step, index) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除步骤 ${index + 1}: ${step.apiInfo?.name || '未命名步骤'} 吗？`,
    okText: '确认',
    okType: 'danger',
    cancelText: '取消',
    onOk: () => removeStep(index)
  });
};

// 删除步骤
const removeStep = (index) => {
  const step = caseData.apiSteps[index];

  // 从展开列表中移除
  const expandIndex = expandedStepKeys.value.indexOf(step.apiCaseId);
  if (expandIndex !== -1) {
    expandedStepKeys.value.splice(expandIndex, 1);
  }

  // 如果正在编辑，取消编辑
  if (editingStepId.value === step.apiCaseId) {
    editingStepId.value = null;
  }

  // 从步骤列表删除
  caseData.apiSteps.splice(index, 1);

  // 更新步骤顺序
  updateStepOrders();

  message.success('步骤已删除');

  // 重新初始化Sortable
  nextTick(() => {
    initSortable();
  });
};

// 更新步骤顺序
const updateStepOrders = () => {
  caseData.apiSteps.forEach((step, idx) => {
    step.stepOrder = idx + 1;
  });
};

// 保存用例
const saveCase = async () => {
  try {
    saving.value = true;
    await CaseApi.update(caseData)

  } catch (error) {
    console.error('保存失败:', error);
    message.error('保存失败，请检查表单数据');
    smartSentry.captureError(error);
  } finally {
    saving.value = false;
  }
};

// 返回上一页
const goBack = () => {
  router.back();
};

// 加载模块数据
const loadModuleTree = async () => {
  try {
    const response = await ModuleApi.getModuleTree();
    if (response.code === 1 && response.data) {
      // 处理模块树数据
    }
  } catch (error) {
    console.error('加载模块数据失败:', error);
    message.error('加载模块数据失败');
    smartSentry.captureError(error);
  }
};

// 加载用例数据
const loadCaseData = async () => {
  if (!isEdit.value) {
    // 即使是新建，也初始化空数组
    caseData.apiSteps = [];
    return;
  }

  try {
    const response = await CaseApi.get({caseIds:[caseId.value]});
    // 确保返回数据中有apiSteps字段
    if (!response.data.apiSteps) {
      response.data.apiSteps = [];
    }

    // 合并数据
    Object.assign(caseData, response.data[0]);
    console.log('加载的API步骤数据:', caseData.apiSteps);

  } catch (error) {
    console.error('加载用例数据失败:', error);
    message.error('加载用例数据失败');
    smartSentry.captureError(error);
  }
};

// 监听apiSteps变化
watch(() => caseData.apiSteps.length, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    nextTick(() => {
      initSortable();
    });
  }
});

// 初始化
onMounted(async () => {
  // 确保apiSteps是数组
  if (!caseData.apiSteps) {
    caseData.apiSteps = [];
  }

  await loadModuleTree();
  await loadCaseData();

  // 等待表格渲染完成后初始化Sortable
  await nextTick(() => {
    initSortable();
  });
});
</script>

<style scoped>
.case-detail-container {
  padding: 24px;
  background-color: #f0f2f5;
}

.section-card {
  margin-bottom: 24px;
}

.api-steps-header {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-start;
}

.api-steps-empty {
  padding: 32px 0;
  text-align: center;
  background-color: #fafafa;
  border: 1px dashed #e8e8e8;
  border-radius: 4px;
}

.table-container {
  position: relative;
  border-radius: 2px;
}

.step-drag-handle {
  cursor: move;
  color: #999;
  padding: 4px;
  border-radius: 4px;
}

.step-drag-handle:hover {
  color: #1890ff;
  background-color: #e6f7ff;
}

.step-method {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  color: white;
  text-transform: uppercase;
  background-color: #ccc;
  min-width: 50px;
  text-align: center;
  display: inline-block;
}

.step-method.get {
  background-color: #52c41a;
}

.step-method.post {
  background-color: #1890ff;
}

.step-method.put {
  background-color: #faad14;
}

.step-method.delete {
  background-color: #ff4d4f;
}

.step-method.patch {
  background-color: #722ed1;
}

.step-url {
  color: #666;
  font-size: 12px;
}
.case-detail-container {
  padding: 24px;
  background-color: #f0f2f5;
}

.section-card {
  margin-bottom: 24px;
}

.api-steps-header {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-start;
}

.api-steps-empty {
  padding: 32px 0;
  text-align: center;
  background-color: #fafafa;
  border: 1px dashed #e8e8e8;
  border-radius: 4px;
}

.table-container {
  position: relative;
  border-radius: 2px;
}

.step-drag-handle {
  cursor: move;
  color: #999;
  padding: 4px;
  border-radius: 4px;
}

.step-drag-handle:hover {
  color: #1890ff;
  background-color: #e6f7ff;
}

.step-method {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  color: white;
  text-transform: uppercase;
  background-color: #ccc;
  min-width: 50px;
  text-align: center;
  display: inline-block;
}

.step-method.get {
  background-color: #52c41a;
}

.step-method.post {
  background-color: #1890ff;
}

.step-method.put {
  background-color: #faad14;
}

.step-method.delete {
  background-color: #ff4d4f;
}

.step-method.patch {
  background-color: #722ed1;
}

.step-url {
  color: #666;
  font-size: 12px;
}

.api-step-content {
  padding: 16px;
  background-color: #fafafa;
  border-radius: 4px;
}

.api-step-info {
  margin-bottom: 16px;
}

.param-section-title {
  font-weight: 500;
  margin: 16px 0 8px;
  color: #333;
}

.api-step-params,
.api-step-extract,
.api-step-validate {
  margin-top: 16px;
}

.empty-rules {
  color: #999;
  font-style: italic;
  padding: 8px 0;
}

.rules-list {
  margin-top: 8px;
}

.rule-item {
  padding: 8px;
  background: #f9f9f9;
  border-radius: 4px;
  margin-bottom: 8px;
  border-left: 3px solid #1890ff;
}

.api-step-actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px dashed #f0f0f0;
  text-align: right;
}

/* Sortable.js 相关样式 */
.step-ghost {
  opacity: 0.5;
  background: #e6f7ff;
}

.step-chosen {
  background: #f0f7ff;
}

:deep(.ant-table-expanded-row) td {
  background: #fff;
}

/* 表格行拖拽样式 */
:deep(.ant-table-tbody > tr.step-ghost > td) {
  background-color: #e6f7ff;
  border: 1px dashed #1890ff;
}

:deep(.ant-table-tbody > tr.step-chosen > td) {
  background-color: #f0f7ff;
}

:deep(.ant-table-row:hover > td) {
  background-color: #f5f5f5;
}

:deep(.ant-table-tbody > tr > td) {
  transition: background 0.3s;
}

:deep(.sortable-ghost) {
  opacity: 0.7;
  background: #e6f7ff !important;
}

:deep(.sortable-drag) {
  opacity: 0.9;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  background: #fff !important;
}

/* 确保表格内容对齐正确 */
:deep(.ant-table-cell) {
  vertical-align: middle;
}

/* 美化展开内容样式 */
:deep(.ant-table-expanded-row > td) {
  padding: 0 !important;
}

:deep(.ant-table-expanded-row .api-step-content) {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fcfcfc;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .step-url {
    display: none;
  }

  :deep(.ant-table-cell) {
    padding: 8px 4px !important;
  }
}
</style>