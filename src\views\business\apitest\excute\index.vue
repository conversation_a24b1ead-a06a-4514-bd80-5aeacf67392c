<template>
  <div class="test-execution-container">
    <!-- 页面标题和概述 -->
    <div class="page-header">
      <a-page-header
        title="接口自动化测试执行"
        sub-title="执行自动化测试并生成报告"
      >
        <template #extra>
          <a-space>
            <a-button type="primary" @click="runTests" :loading="isRunning">
              <template #icon><PlayCircleOutlined /></template>
              立即执行
            </a-button>
            <a-button @click="showScheduleModal">
              <template #icon><ClockCircleOutlined /></template>
              定时执行
            </a-button>
            <a-button danger @click="stopExecution" :disabled="!isRunning">
              <template #icon><StopOutlined /></template>
              中断执行
            </a-button>
          </a-space>
        </template>
      </a-page-header>
    </div>

    <!-- 执行状态卡片 -->
    <a-card v-if="isRunning" class="status-card" :bordered="false">
      <div class="status-header">
        <div class="status-title">
          <loading-outlined spin />
          <span>正在执行测试...</span>
        </div>
        <div class="status-info">
          <a-tag color="success">通过: {{ passedCount }}</a-tag>
          <a-tag color="error">失败: {{ failedCount }}</a-tag>
          <a-tag color="processing">用时: {{ elapsedTime }}秒</a-tag>
        </div>
      </div>
      <div class="current-case">
        当前执行: {{ currentCase }}
      </div>
      <a-progress
        :percent="executionProgress"
        :status="executionStatus"
        stroke-color="#1890ff"
        stroke-width="6"
      />
    </a-card>

    <a-row :gutter="16" class="main-content">
      <!-- 左侧选择区 -->
      <a-col :xs="24" :md="12" :lg="12">
        <a-card :bordered="false" class="selection-card">
          <a-tabs default-active-key="case" type="card">
            <a-tab-pane key="case" tab="按用例选择">
              <div class="tab-content">
                <!-- 产品线/模块树 -->
                <div class="section-container">
                  <div class="section-header">
                    <div class="section-title">
                      <folder-outlined />
                      <span>产品线/模块</span>
                    </div>
                    <a-button type="link" size="small" @click="expandAllNodes">
                      {{ expandAll ? '收起' : '展开' }}全部
                    </a-button>
                  </div>
                  <div class="tree-container">
                    <a-tree
                      v-model:expandedKeys="expandedKeys"
                      v-model:selectedKeys="selectedKeys"
                      :tree-data="treeData"
                      :fieldNames="{ title: 'name' ,key: 'groupId', children: 'children'}"
                      @select="onTreeSelect"
                      show-line
                    >
                      <template #title="{name}">
                        <span>{{ name }}</span>
                      </template>
                    </a-tree>
                  </div>
                </div>

                <!-- 筛选条件 -->
                <div class="section-container">
                  <div class="section-header">
                    <div class="section-title">
                      <filter-outlined />
                      <span>筛选条件</span>
                    </div>
                    <a-button type="link" size="small" @click="clearFilters">
                      清空筛选
                    </a-button>
                  </div>
                  <a-form layout="vertical" class="filter-form">
                    <!-- 标签筛选 -->
                    <a-form-item label="标签">
                      <a-select
                        v-model:value="selectedTags"
                        mode="multiple"
                        placeholder="选择标签"
                        style="width: 100%"
                        allowClear
                      >
                        <a-select-option v-for="tag in tagOptions" :key="tag.value" :value="tag.value">
                          {{ tag.label }}
                        </a-select-option>
                      </a-select>
                    </a-form-item>

                    <!-- 优先级筛选 -->
                    <a-form-item label="优先级">
                      <a-checkbox-group v-model:value="selectedPriorities">
                        <a-checkbox :value="1"><a-tag color="red">高</a-tag></a-checkbox>
                        <a-checkbox :value="2"><a-tag color="orange">中</a-tag></a-checkbox>
                        <a-checkbox :value="3"><a-tag color="blue">低</a-tag></a-checkbox>
                      </a-checkbox-group>
                    </a-form-item>

                    <!-- 搜索框 -->
                    <a-form-item>
                      <a-input-search
                        v-model:value="searchKeyword"
                        placeholder="搜索用例名称"
                        enter-button
                        @search="onSearch"
                        allowClear
                      />
                    </a-form-item>
                  </a-form>
                </div>

                <!-- 用例列表 -->
                <div class="section-container">
                  <div class="section-header">
                    <div class="section-title">
                      <unordered-list-outlined />
                      <span>测试用例列表</span>
                    </div>
                    <a-space>
                      <a-button type="primary" size="small" @click="refreshCaseList">
                        <template #icon><reload-outlined /></template>
                        刷新
                      </a-button>
                      <a-button type="primary" size="small" @click="addToSuite">
                        <template #icon><plus-outlined /></template>
                        添加到集合
                      </a-button>
                    </a-space>
                  </div>

                  <a-table
                    :dataSource="filteredCaseList"
                    :columns="caseColumns"
                    :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
                    :rowKey="record => record.caseId"
                    size="middle"
                    :pagination="{ pageSize: 8, showSizeChanger: true, pageSizeOptions: ['8', '16', '32'] }"
                    class="custom-table"
                    :scroll="{ y: 350 }"
                  >
                    <template #bodyCell="{ column, record }">
                      <template v-if="column.dataIndex === 'priority'">
                        <a-tag :color="getPriorityColor(record.priority)">
                          {{ getPriorityLabel(record.priority) }}
                        </a-tag>
                      </template>
                      <template v-if="column.dataIndex === 'tags'">
                        <a-tag v-for="tag in record.tags" :key="tag" color="blue">{{ tag }}</a-tag>
                      </template>
                    </template>
                    <template #emptyText>
                      <div class="empty-container">
                        <inbox-outlined />
                        <p>暂无测试用例</p>
                        <p class="sub-text">请选择产品线/模块或调整筛选条件</p>
                      </div>
                    </template>
                  </a-table>
                </div>
              </div>
            </a-tab-pane>

            <a-tab-pane key="suite" tab="按集合选择">
              <div class="tab-content">
                <!-- 用例集合列表 -->
                <div class="section-container">
                  <div class="section-header">
                    <div class="section-title">
                      <folder-open-outlined />
                      <span>用例集合</span>
                    </div>
                    <a-space>
                      <a-button type="primary" size="small" @click="refreshSuiteList">
                        <template #icon><reload-outlined /></template>
                        刷新
                      </a-button>
                      <a-button type="primary" size="small" @click="showCreateSuiteModal">
                        <template #icon><plus-outlined /></template>
                        创建集合
                      </a-button>
                    </a-space>
                  </div>

                  <a-table
                    :dataSource="suiteList"
                    :columns="suiteColumns"
                    :rowSelection="{ selectedRowKeys: selectedSuiteKeys, onChange: onSuiteSelectChange }"
                    :rowKey="record => record.suiteId"
                    size="middle"
                    :pagination="{ pageSize: 8, showSizeChanger: true, pageSizeOptions: ['8', '16', '32'] }"
                    class="custom-table"
                    :scroll="{ y: 200 }"
                  >
                    <template #bodyCell="{ column, record }">
                      <template v-if="column.dataIndex === 'status'">
                        <a-tag :color="record.status === 1 ? 'green' : 'red'">
                          {{ record.status === 1 ? '启用' : '禁用' }}
                        </a-tag>
                      </template>
                      <template v-if="column.dataIndex === 'action'">
                        <a-space>
                          <a-button type="link" @click.stop="editSuite(record)">
                            <template #icon><edit-outlined /></template>
                            编辑
                          </a-button>
                          <a-button type="link" danger @click.stop="deleteSuite(record)">
                            <template #icon><delete-outlined /></template>
                            删除
                          </a-button>
                          <a-button type="link" @click.stop="viewSuiteCases(record)">
                            <template #icon><eye-outlined /></template>
                            查看用例
                          </a-button>
                        </a-space>
                      </template>
                    </template>
                    <template #emptyText>
                      <div class="empty-container">
                        <inbox-outlined />
                        <p>暂无测试集合</p>
                        <p class="sub-text">点击"创建集合"添加一个新的测试集合</p>
                      </div>
                    </template>
                  </a-table>
                </div>

                <!-- 集合中的用例列表 -->
                <div class="section-container" v-if="selectedSuiteId">
                  <div class="section-header">
                    <div class="section-title">
                      <apartment-outlined />
                      <span>集合中的用例</span>
                    </div>
                    <a-space>
                      <a-button type="primary" size="small" @click="refreshSuiteCaseList">
                        <template #icon><reload-outlined /></template>
                        刷新
                      </a-button>
                      <a-button type="danger" size="small" @click="removeFromSuite">
                        <template #icon><minus-outlined /></template>
                        移除用例
                      </a-button>
                    </a-space>
                  </div>

                  <a-table
                    :dataSource="suiteCaseList"
                    :columns="caseColumns"
                    :rowSelection="{ selectedRowKeys: selectedSuiteCaseKeys, onChange: onSuiteCaseSelectChange }"
                    :rowKey="record => record.caseId"
                    size="middle"
                    :pagination="{ pageSize: 8, showSizeChanger: true, pageSizeOptions: ['8', '16', '32'] }"
                    class="custom-table"
                    :scroll="{ y: 200 }"
                  >
                    <template #bodyCell="{ column, record }">
                      <template v-if="column.dataIndex === 'priority'">
                        <a-tag :color="getPriorityColor(record.priority)">
                          {{ getPriorityLabel(record.priority) }}
                        </a-tag>
                      </template>
                      <template v-if="column.dataIndex === 'tags'">
                        <a-tag v-for="tag in record.tags" :key="tag" color="blue">{{ tag }}</a-tag>
                      </template>
                    </template>
                    <template #emptyText>
                      <div class="empty-container">
                        <inbox-outlined />
                        <p>集合中暂无测试用例</p>
                        <p class="sub-text">请从用例列表中添加用例到该集合</p>
                      </div>
                    </template>
                  </a-table>
                </div>
              </div>
            </a-tab-pane>
          </a-tabs>
        </a-card>
      </a-col>

      <!-- 右侧配置与报告区 -->
      <a-col :xs="24" :md="12" :lg="12">
        <!-- 执行配置 -->
        <a-card
          :bordered="false"
          class="config-card"
          title="执行配置"
        >
          <template #title>
            <div class='card-title'>
              <setting-outlined />
              <span>执行配置</span>
            </div>
          </template>
          <a-form layout="vertical">
            <!-- 执行类型选择 -->
            <a-form-item label="执行类型">
              <a-radio-group v-model:value="executeType" button-style="solid">
                <a-radio-button value="case">
                  <unordered-list-outlined /> 按用例执行
                </a-radio-button>
                <a-radio-button value="suite">
                  <folder-open-outlined /> 按集合执行
                </a-radio-button>
              </a-radio-group>
            </a-form-item>

            <!-- 环境选择 -->
            <a-form-item label="测试环境">
              <a-select
                v-model:value="selectedEnv"
                placeholder="选择环境"
                style="width: 100%"
              >
                <a-select-option v-for="env in environments" :key="env.envId" :value="env.envId">
                  <span class="env-option">
                    <component :is="getEnvIcon(env.name)" />
                    <span>{{ env.name }}</span>
                  </span>
                </a-select-option>
              </a-select>
            </a-form-item>

            <!-- 报告名称 -->
            <a-form-item label="报告名称">
              <a-input
                v-model:value="reportName"
                placeholder="自定义报告名称（可选）"
                allowClear
              >
                <template #prefix>
                  <file-text-outlined />
                </template>
              </a-input>
            </a-form-item>

            <!-- 执行选项 -->
            <a-form-item label="执行选项">
              <a-checkbox v-model:checked="retryOnFailure">失败用例自动重试</a-checkbox>
              <a-checkbox v-model:checked="stopOnFailure">失败时中断执行</a-checkbox>
            </a-form-item>
          </a-form>
        </a-card>

        <!-- 历史报告 -->
        <a-card
          :bordered="false"
          class="report-card"
          title="历史报告"
        >
          <template #title>
            <div class='card-title'>
              <file-text-outlined />
              <span>历史报告</span>
            </div>
          </template>

          <template #extra>
            <a-button type="primary" size="small" @click="refreshReports">
              <template #icon><reload-outlined /></template>
              刷新
            </a-button>
          </template>

          <a-table
            :dataSource="reportList"
            :columns="reportColumns"
            size="middle"
            :pagination="{ pageSize: 8, showSizeChanger: true, pageSizeOptions: ['8', '16', '32'] }"
            class="custom-table"
            :scroll="{ y: 400 }"
            :rowKey="record => record.name"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'action'">
                <a-space>
                  <a-button type="primary" size="small" @click="viewReport(record)">
                    <template #icon><eye-outlined /></template>
                    查看
                  </a-button>
                  <a-button danger size="small" @click="confirmDeleteReport(record)">
                    <template #icon><delete-outlined /></template>
                    删除
                  </a-button>
                </a-space>
              </template>
              <template v-if="column.dataIndex === 'time'">
                {{ formatDate(record.time) }}
              </template>
            </template>
            <template #emptyText>
              <div class="empty-container">
                <file-search-outlined />
                <p>暂无测试报告</p>
                <p class="sub-text">执行测试后将在此处显示生成的报告</p>
              </div>
            </template>
          </a-table>
        </a-card>
      </a-col>
    </a-row>

    <!-- 创建/编辑集合对话框 -->
    <a-modal
      :title="suiteModalTitle"
      :visible="suiteModalVisible"
      @ok="handleSuiteModalOk"
      @cancel="suiteModalVisible = false"
      :okButtonProps="{ props: { disabled: !suiteForm.suiteName } }"
    >
      <a-form :model="suiteForm" layout="vertical">
        <a-form-item
          label="集合名称"
          name="suiteName"
          :rules="[{ required: true, message: '请输入集合名称' }]"
        >
          <a-input
            v-model:value="suiteForm.suiteName"
            placeholder="请输入集合名称"
          >
            <template #prefix>
              <folder-outlined />
            </template>
          </a-input>
        </a-form-item>
        <a-form-item label="描述" name="desc">
          <a-textarea
            v-model:value="suiteForm.desc"
            placeholder="请输入集合描述"
            :rows="4"
          />
        </a-form-item>
        <a-form-item label="状态" name="status">
          <a-radio-group v-model:value="suiteForm.status" button-style="solid">
            <a-radio-button :value="1">
              <check-circle-outlined /> 启用
            </a-radio-button>
            <a-radio-button :value="0">
              <stop-outlined /> 禁用
            </a-radio-button>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 添加到集合对话框 -->
    <a-modal
      title="添加到集合"
      :visible="addToSuiteModalVisible"
      @ok="handleAddToSuiteOk"
      @cancel="addToSuiteModalVisible = false"
      :okButtonProps="{ props: { disabled: !selectedSuiteForAdd } }"
    >
      <a-form layout="vertical">
        <a-form-item
          label="选择集合"
          :rules="[{ required: true, message: '请选择要添加到的集合' }]"
        >
          <a-select
            v-model:value="selectedSuiteForAdd"
            placeholder="选择要添加到的集合"
            style="width: 100%"
            show-search
            :filter-option="filterOption"
          >
            <a-select-option v-for="suite in suiteList" :key="suite.suiteId" :value="suite.suiteId">
              {{ suite.suiteName }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-button type="link" @click="showCreateSuiteModal">
            <template #icon><plus-outlined /></template>
            创建新集合
          </a-button>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 定时执行对话框 -->
    <a-modal
      title="定时执行"
      :visible="scheduleModalVisible"
      @ok="confirmSchedule"
      @cancel="scheduleModalVisible = false"
      :okButtonProps="{ props: { disabled: !scheduleTime } }"
    >
      <a-form layout="vertical">
        <a-form-item
          label="执行时间"
          :rules="[{ required: true, message: '请选择执行时间' }]"
        >
          <a-date-picker
            v-model:value="scheduleTime"
            :show-time="{ format: 'HH:mm' }"
            format="YYYY-MM-DD HH:mm"
            placeholder="选择执行时间"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item label="执行说明">
          <a-input
            v-model:value="scheduleDescription"
            placeholder="请输入定时任务说明（可选）"
            allowClear
          />
        </a-form-item>

        <a-form-item label="通知设置">
          <a-checkbox v-model:checked="notifyOnComplete">执行完成后通知</a-checkbox>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import {
  PlayCircleOutlined, StopOutlined, ClockCircleOutlined, ReloadOutlined,
  PlusOutlined, MinusOutlined, EditOutlined, DeleteOutlined, EyeOutlined,
  FolderOutlined, FolderOpenOutlined, UnorderedListOutlined, FilterOutlined,
  SettingOutlined, FileTextOutlined, CheckCircleOutlined, InboxOutlined,
  ApartmentOutlined, LoadingOutlined, FileSearchOutlined
} from '@ant-design/icons-vue';
import {GroupApi} from "/@/api/business/apitest/group-api.js";
import {message} from "ant-design-vue";
import {SuiteApi} from "/@/api/business/apitest/suite-api.js";
import {CaseApi} from "/@/api/business/apitest/case-api.js";

export default {
  components: {
    PlayCircleOutlined, StopOutlined, ClockCircleOutlined, ReloadOutlined,
    PlusOutlined, MinusOutlined, EditOutlined, DeleteOutlined, EyeOutlined,
    FolderOutlined, FolderOpenOutlined, UnorderedListOutlined, FilterOutlined,
    SettingOutlined, FileTextOutlined, CheckCircleOutlined, InboxOutlined,
    ApartmentOutlined, LoadingOutlined, FileSearchOutlined
  },

  data() {
    return {
      // 执行类型选择
      executeType: 'case',

      // 树形结构数据
      expandedKeys: [],
      selectedKeys: [],
      treeData: [],
      expandAll: false,

      // 筛选条件
      selectedTags: [],
      tagOptions: [],
      selectedPriorities: [],
      searchKeyword: '',

      // 用例数据
      caseList: [],
      selectedRowKeys: [],
      selectedCases: [],

      // 集合数据
      suiteList: [],
      selectedSuiteKeys: [],
      selectedSuites: [],
      selectedSuiteId: null,
      suiteCaseList: [],
      selectedSuiteCaseKeys: [],
      selectedSuiteCases: [],

      // 添加到集合
      addToSuiteModalVisible: false,
      selectedSuiteForAdd: null,

      // 创建/编辑集合
      suiteModalVisible: false,
      suiteModalTitle: '创建集合',
      suiteForm: {
        suiteId: null,
        suiteName: '',
        desc: '',
        status: 1
      },

      // 表格列定义
      caseColumns: [
        { title: '用例名称', dataIndex: 'caseName', key: 'caseName', ellipsis: true },
        { title: '优先级', dataIndex: 'priority', key: 'priority', width: 80, align: 'center' },
        { title: '标签', dataIndex: 'tags', key: 'tags', width: 150 },
      ],
      suiteColumns: [
        { title: '集合名称', dataIndex: 'suiteName', key: 'suiteName', ellipsis: true },
        { title: '描述', dataIndex: 'desc', key: 'desc', ellipsis: true },
        { title: '状态', dataIndex: 'status', key: 'status', width: 80, align: 'center' },
        { title: '操作', dataIndex: 'action', key: 'action', width: 200, fixed: 'right' },
      ],
      reportColumns: [
        { title: '报告名称', dataIndex: 'name', key: 'name', ellipsis: true },
        { title: '生成时间', dataIndex: 'time', key: 'time', width: 180 },
        { title: '操作', dataIndex: 'action', key: 'action', width: 150, fixed: 'right' },
      ],

      // 环境配置
      environments: [
        { envId: 1, name: '开发环境' },
        { envId: 2, name: '测试环境' },
        { envId: 3, name: '生产环境' },
      ],
      selectedEnv: 1,
      reportName: '',

      // 执行选项
      retryOnFailure: false,
      stopOnFailure: false,

      // 执行状态
      isRunning: false,
      executionProgress: 0,
      executionStatus: 'active',
      currentCase: '',
      passedCount: 0,
      failedCount: 0,
      elapsedTime: 0,
      executionTimer: null,

      // 进度更新定时器
      progressUpdateTimer: null,

      // 报告数据
      reportList: [],

      // 定时执行
      scheduleModalVisible: false,
      scheduleTime: null,
      scheduleDescription: '',
      notifyOnComplete: true,
    };
  },

  computed: {
    filteredCaseList() {
      let result = this.caseList;

      // 根据标签筛选
      if (this.selectedTags.length > 0) {
        result = result.filter(item => {
          return item.tags && item.tags.some(tag => this.selectedTags.includes(tag));
        });
      }

      // 根据优先级筛选
      if (this.selectedPriorities.length > 0) {
        result = result.filter(item => {
          return this.selectedPriorities.includes(item.priority);
        });
      }

      // 根据关键词搜索
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase();
        result = result.filter(item => {
          return item.caseName.toLowerCase().includes(keyword);
        });
      }

      return result;
    }
  },

  created() {
    this.initData();
  },

  methods: {
    // 初始化数据
    initData() {
      this.loadProductLineTree();
      this.loadReports();
      this.loadSuiteList();
    },

    // 展开/收起所有节点
    expandAllNodes() {
      this.expandAll = !this.expandAll;
      if (this.expandAll) {
        // 递归获取所有节点的 key
        const getAllKeys = (nodes) => {
          let keys = [];
          if (!nodes) return keys;

          nodes.forEach(node => {
            keys.push(node.groupId);
            if (node.children) {
              keys = keys.concat(getAllKeys(node.children));
            }
          });
          return keys;
        };

        this.expandedKeys = getAllKeys(this.treeData);
      } else {
        this.expandedKeys = [];
      }
    },

    // 清空筛选条件
    clearFilters() {
      this.selectedTags = [];
      this.selectedPriorities = [];
      this.searchKeyword = '';
    },

    // 过滤选项
    filterOption(input, option) {
      return option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    },

    // 获取环境图标
    getEnvIcon(envName) {
      if (envName.includes('开发')) return 'CodeOutlined';
      if (envName.includes('测试')) return 'BugOutlined';
      if (envName.includes('生产')) return 'CloudOutlined';
      return 'EnvironmentOutlined';
    },

    // 加载产品线树
    async loadProductLineTree() {
      // 这里应该调用API获取产品线和模块数据
      // 模拟数据
      try{
        const response = await GroupApi.getGroupTree()
        this.treeData = response.data
      }catch (e) {
        message.error("获取接口树失败")
      }

      // 展开第一级
      if (this.treeData && this.treeData.length>0){
        this.expandedKeys = [this.treeData[0].groupId];
      }

    },

    // 加载集合列表
    async loadSuiteList() {
      // 这里应该调用API获取集合列表
      // 调用 /api_test/suite/page 接口
      // 模拟数据
      try{
        const response = await SuiteApi.get({})
        this.suiteList = response.data
      }catch (e){
        message.error("获取集合列表失败")
      }
    },

    // 加载测试用例
    async loadCases(nodeKey) {
      // 这里应该调用API获取测试用例
      // 模拟数据
      try{
        const response = await CaseApi.getList({ groupIds: [nodeKey]})
        this.caseList = response.data
      }catch (e) {
        message.error("获取用例列表失败")
      }
    },

    // 加载集合中的用例
    loadSuiteCases(suiteId) {
      // 这里应该调用API获取集合中的用例
      // 模拟数据
      if (suiteId === 1) {
        this.suiteCaseList = [
          {
            caseId: 1,
            caseName: '正常登录测试',
            priority: 1,
            tags: ['login', 'core'],
            status: 1,
          },
          {
            caseId: 2,
            caseName: '错误密码测试',
            priority: 2,
            tags: ['login'],
            status: 1,
          },
        ];
      } else if (suiteId === 2) {
        this.suiteCaseList = [
          {
            caseId: 8,
            caseName: '支付宝支付测试',
            priority: 1,
            tags: ['payment', 'core'],
            status: 1,
          },
          {
            caseId: 9,
            caseName: '微信支付测试',
            priority: 1,
            tags: ['payment', 'core'],
            status: 1,
          },
        ];
      } else {
        this.suiteCaseList = [];
      }
    },

    // 加载报告列表
    loadReports() {
      // 这里应该调用API获取报告列表
      // 调用 /api_test/reports 接口
      // 模拟数据
      this.reportList = [
        {
          name: 'login_test_20250220',
          path: '/reports/login_test_20250220',
          time: new Date().getTime() / 1000 - 86400,
        },
        {
          name: 'full_regression_20250219',
          path: '/reports/full_regression_20250219',
          time: new Date().getTime() / 1000 - 86400 * 2,
        },
      ];
    },

    // 树节点选择事件
    onTreeSelect(selectedKeys, info) {
      if (selectedKeys.length > 0) {
        this.loadCases(selectedKeys[0]);
      } else {
        this.caseList = [];
      }
    },

    // 表格选择变化事件
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      this.selectedCases = selectedRows;
    },

    // 集合中用例选择变化事件
    onSuiteCaseSelectChange(selectedRowKeys, selectedRows) {
      this.selectedSuiteCaseKeys = selectedRowKeys;
      this.selectedSuiteCases = selectedRows;
    },

    // 集合选择变化事件
    onSuiteSelectChange(selectedRowKeys, selectedRows) {
      this.selectedSuiteKeys = selectedRowKeys;
      this.selectedSuites = selectedRows;

      // 如果只选中了一个集合，则自动加载其用例
      if (selectedRowKeys.length === 1) {
        this.selectedSuiteId = selectedRowKeys[0];
        this.loadSuiteCases(this.selectedSuiteId);
      } else if (selectedRowKeys.length === 0) {
        this.selectedSuiteId = null;
        this.suiteCaseList = [];
      }
    },

    // 查看集合用例
    viewSuiteCases(suite) {
      this.selectedSuiteId = suite.suiteId;
      this.loadSuiteCases(suite.suiteId);

      // 同时选中该集合
      if (!this.selectedSuiteKeys.includes(suite.suiteId)) {
        this.selectedSuiteKeys = [suite.suiteId];
        this.selectedSuites = [suite];
      }
    },

    // 搜索事件
    onSearch(value) {
      this.searchKeyword = value;
    },

    // 刷新用例列表
    refreshCaseList() {
      if (this.selectedKeys.length > 0) {
        this.loadCases(this.selectedKeys[0]);
      }
    },

    // 刷新集合列表
    refreshSuiteList() {
      this.loadSuiteList();
    },

    // 刷新集合用例列表
    refreshSuiteCaseList() {
      if (this.selectedSuiteId) {
        this.loadSuiteCases(this.selectedSuiteId);
      }
    },

    // 刷新报告列表
    refreshReports() {
      this.loadReports();
    },

    // 显示创建集合对话框
    showCreateSuiteModal() {
      this.suiteForm = {
        suiteId: null,
        suiteName: '',
        desc: '',
        status: 1
      };
      this.suiteModalTitle = '创建集合';
      this.suiteModalVisible = true;

      // 如果是从添加到集合对话框打开的，则先关闭添加对话框
      if (this.addToSuiteModalVisible) {
        this.addToSuiteModalVisible = false;
      }
    },

    // 编辑集合
    editSuite(suite) {
      this.suiteForm = {
        suiteId: suite.suiteId,
        suiteName: suite.suiteName,
        desc: suite.desc,
        status: suite.status
      };
      this.suiteModalTitle = '编辑集合';
      this.suiteModalVisible = true;
    },

    // 处理集合对话框确认
    handleSuiteModalOk() {
      // 验证表单
      if (!this.suiteForm.suiteName) {
        this.$message.error('请输入集合名称');
        return;
      }

      // 处理创建或更新集合
      if (this.suiteForm.suiteId) {
        // 更新集合
        // 调用 /api_test/suite/update 接口
        // 模拟更新
        const index = this.suiteList.findIndex(item => item.suiteId === this.suiteForm.suiteId);
        if (index !== -1) {
          this.suiteList[index] = {
            ...this.suiteList[index],
            ...this.suiteForm,
            updateTime: new Date().getTime() / 1000
          };
        }
        this.$message.success('集合已更新');
      } else {
        // 创建集合
        // 调用 /api_test/suite/add 接口
        // 模拟创建
        const newSuite = {
          ...this.suiteForm,
          suiteId: Date.now(), // 模拟生成ID
          createTime: new Date().getTime() / 1000,
          updateTime: new Date().getTime() / 1000
        };
        this.suiteList.push(newSuite);
        this.$message.success('集合已创建');

        // 如果是从添加到集合对话框打开的，则重新打开添加对话框并选中新创建的集合
        if (this.selectedRowKeys.length > 0) {
          this.selectedSuiteForAdd = newSuite.suiteId;
          this.addToSuiteModalVisible = true;
        }
      }

      this.suiteModalVisible = false;
    },

    // 删除集合
    deleteSuite(suite) {
      this.$confirm({
        title: '确认删除',
        content: `确定要删除集合 "${suite.suiteName}"？`,
        okText: '确定',
        okType: 'danger',
        cancelText: '取消',
        onOk: () => {
          // 调用 /api_test/suite/delete 接口
          // 模拟删除
          this.suiteList = this.suiteList.filter(item => item.suiteId !== suite.suiteId);

          // 如果当前选中的就是被删除的集合，则清空选择
          if (this.selectedSuiteId === suite.suiteId) {
            this.selectedSuiteId = null;
            this.suiteCaseList = [];
          }

          // 更新选中的集合列表
          this.selectedSuiteKeys = this.selectedSuiteKeys.filter(id => id !== suite.suiteId);
          this.selectedSuites = this.selectedSuites.filter(item => item.suiteId !== suite.suiteId);

          this.$message.success('集合已删除');
        },
      });
    },

    // 显示添加到集合对话框
    addToSuite() {
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning('请至少选择一个测试用例');
        return;
      }

      this.addToSuiteModalVisible = true;
    },

    // 处理添加到集合确认
    handleAddToSuiteOk() {
      if (!this.selectedSuiteForAdd) {
        this.$message.warning('请选择要添加到的集合');
        return;
      }

      // 调用 /api_test/case_suite/add 接口
      // 模拟添加
      this.$message.success('已添加到集合');
      this.addToSuiteModalVisible = false;

      // 如果当前选中的就是添加的目标集合，则刷新列表
      if (this.selectedSuiteId === this.selectedSuiteForAdd) {
        this.refreshSuiteCaseList();
      }
    },

    // 从集合中移除用例
    removeFromSuite() {
      if (this.selectedSuiteCaseKeys.length === 0) {
        this.$message.warning('请至少选择一个要移除的测试用例');
        return;
      }

      this.$confirm({
        title: '确认移除',
        content: '确定要从集合中移除选中的用例？',
        okText: '确定',
        okType: 'danger',
        cancelText: '取消',
        onOk: () => {
          // 调用移除接口
          // 模拟移除
          this.suiteCaseList = this.suiteCaseList.filter(
            item => !this.selectedSuiteCaseKeys.includes(item.caseId)
          );
          this.selectedSuiteCaseKeys = [];
          this.$message.success('已从集合中移除');
        },
      });
    },

    // 运行测试
    runTests() {
      let requestData = {
        envId: this.selectedEnv,
        reportName: this.reportName || undefined,
        retryOnFailure: this.retryOnFailure,
        stopOnFailure: this.stopOnFailure
      };

      // 根据执行类型设置参数
      if (this.executeType === 'case') {
        if (this.selectedRowKeys.length === 0) {
          this.$message.warning('请至少选择一个测试用例');
          return;
        }
        requestData.caseIds = this.selectedRowKeys;
      } else {
        if (this.selectedSuiteKeys.length === 0) {
          this.$message.warning('请选择至少一个测试集合');
          return;
        }
        requestData.suiteIds = this.selectedSuiteKeys;
      }

      // 启动执行
      this.isRunning = true;
      this.executionProgress = 0;
      this.passedCount = 0;
      this.failedCount = 0;
      this.elapsedTime = 0;
      this.currentCase = '准备中...';
      this.executionStatus = 'active';

      // 启动计时器
      this.executionTimer = setInterval(() => {
        this.elapsedTime++;
      }, 1000);

      // 发送API请求执行测试
      // 调用 /api_test/run_with_report 接口
      // 这里模拟API调用
      this.simulateProgressUpdate();
    },

    // 模拟进度更新
    simulateProgressUpdate() {
      const cases = this.executeType === 'case' ? this.selectedCases : this.suiteCaseList;
      const totalCases = cases.length;
      let completedCases = 0;

      // 确保清除之前的更新定时器
      if (this.progressUpdateTimer) {
        clearInterval(this.progressUpdateTimer);
      }

      this.progressUpdateTimer = setInterval(() => {
        if (!this.isRunning) {
          clearInterval(this.progressUpdateTimer);
          return;
        }

        if (completedCases < totalCases) {
          completedCases++;
          this.currentCase = cases[completedCases - 1].caseName;

          // 随机设置通过/失败
          if (Math.random() > 0.2) {
            this.passedCount++;
          } else {
            this.failedCount++;

            // 如果设置了失败时停止
            if (this.stopOnFailure) {
              clearInterval(this.progressUpdateTimer);
              this.isRunning = false;
              clearInterval(this.executionTimer);
              this.executionStatus = 'exception';
              this.$message.error('测试执行失败，已停止');
              this.loadReports();
              return;
            }
          }

          // 更新进度
          this.executionProgress = Math.floor((completedCases / totalCases) * 100);
        } else {
          clearInterval(this.progressUpdateTimer);

          // 只有在执行状态下才显示完成消息，防止中断后仍显示完成
          if (this.isRunning) {
            // 执行完成
            this.isRunning = false;
            clearInterval(this.executionTimer);
            this.executionStatus = this.failedCount > 0 ? 'exception' : 'success';
            this.$message.success('测试执行完成');
            this.loadReports(); // 刷新报告列表
          }
        }
      }, 1000); // 每1秒更新一次
    },

    // 停止执行
    stopExecution() {
      this.$confirm({
        title: '确认中断',
        content: '确定要中断当前执行？',
        okText: '确定',
        okType: 'danger',
        cancelText: '取消',
        onOk: () => {
          // 发送停止请求
          // ...

          // 清除进度更新定时器
          if (this.progressUpdateTimer) {
            clearInterval(this.progressUpdateTimer);
          }

          // 清除计时器
          if (this.executionTimer) {
            clearInterval(this.executionTimer);
          }

          this.isRunning = false;
          this.executionStatus = 'exception';
          this.$message.warning('测试执行已中断');
        },
      });
    },

    // 显示定时执行对话框
    showScheduleModal() {
      this.scheduleModalVisible = true;

      // 检查执行条件
      if (this.executeType === 'case' && this.selectedRowKeys.length === 0) {
        this.$message.warning('请至少选择一个测试用例');
        this.scheduleModalVisible = false;
        return;
      }

      if (this.executeType === 'suite' && this.selectedSuiteKeys.length === 0) {
        this.$message.warning('请选择至少一个测试集合');
        this.scheduleModalVisible = false;
        return;
      }
    },

    // 确认定时任务
    confirmSchedule() {
      if (!this.scheduleTime) {
        this.$message.warning('请选择执行时间');
        return;
      }

      let requestData = {
        envId: this.selectedEnv,
        reportName: this.reportName || undefined,
        scheduleTime: this.scheduleTime.valueOf(),
        description: this.scheduleDescription,
        notifyOnComplete: this.notifyOnComplete,
        retryOnFailure: this.retryOnFailure,
        stopOnFailure: this.stopOnFailure
      };

      // 根据执行类型设置参数
      if (this.executeType === 'case') {
        requestData.caseIds = this.selectedRowKeys;
      } else {
        requestData.suiteIds = this.selectedSuiteKeys;
      }

      // 发送定时执行请求
      // ...

      this.scheduleModalVisible = false;
      this.$message.success('定时任务已创建');
    },

    // 查看报告
    viewReport(report) {
      window.open(report.path, '_blank');
    },

    // 确认删除报告
    confirmDeleteReport(report) {
      this.$confirm({
        title: '确认删除',
        content: `确定要删除报告 "${report.name}"？`,
        okText: '确定',
        okType: 'danger',
        cancelText: '取消',
        onOk: () => {
          // 发送删除请求
          // ...
          this.$message.success('报告已删除');
          this.loadReports();
        },
      });
    },

    // 格式化日期
    formatDate(timestamp) {
      return new Date(timestamp * 1000).toLocaleString();
    },

    // 获取优先级颜色
    getPriorityColor(priority) {
      const colors = {
        1: 'red',
        2: 'orange',
        3: 'blue',
      };
      return colors[priority] || 'blue';
    },

    // 获取优先级文本
    getPriorityLabel(priority) {
      const labels = {
        1: '高',
        2: '中',
        3: '低',
      };
      return labels[priority] || '未知';
    },
  },

  // 组件销毁时清理定时器
  beforeUnmount() {
    if (this.executionTimer) {
      clearInterval(this.executionTimer);
    }

    if (this.progressUpdateTimer) {
      clearInterval(this.progressUpdateTimer);
    }
  }
};
</script>

<style scoped>
.test-execution-container {
  padding: 20px;
  background: #f0f2f5;
  min-height: calc(100vh - 64px);
}

.page-header {
  background: white;
  padding: 16px;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  margin-bottom: 16px;
}

.main-content {
  margin-top: 16px;
}

.selection-card, .config-card, .report-card, .status-card {
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  margin-bottom: 16px;
}

.status-card {
  background: white;
  padding: 16px;
  margin-bottom: 16px;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.status-title {
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-info {
  display: flex;
  gap: 8px;
}

.current-case {
  margin-bottom: 10px;
  color: #666;
}

.tab-content {
  padding: 0 8px;
}

.section-container {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tree-container {
  background: #fafafa;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
  max-height: 300px;
  overflow: auto;
}

.filter-form {
  background: #fafafa;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
}

.custom-table {
  margin-top: 8px;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.empty-container {
  padding: 32px;
  text-align: center;
  color: #999;
}

.empty-container p {
  margin-top: 8px;
  margin-bottom: 4px;
}

.sub-text {
  font-size: 12px;
  color: #aaa;
}

.env-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 响应式调整 */
@media (max-width: 992px) {
  .main-content {
    margin-top: 8px;
  }

  .section-container {
    margin-bottom: 16px;
  }
}
</style>