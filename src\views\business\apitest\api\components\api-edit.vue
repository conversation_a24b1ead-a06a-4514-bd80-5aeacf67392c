<template>
  <div class="api-editor">
    <!-- API 基本信息 -->
    <div class="api-header">
      <div class="api-toolbar-left">
        <a-dropdown>
          <a-button class="method-button" :style="{ backgroundColor: getMethodColor(apiData.method, true), color: '#fff' }">
            {{ apiData.method.toUpperCase() }}
            <down-outlined />
          </a-button>
          <template #overlay>
            <a-menu @click="handleMethodChange">
              <a-menu-item v-for="method in methodOptions" :key="method.value">
                <span :style="{ color: method.color }">{{ method.label }}</span>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>

        <a-input
          v-model:value="apiData.url"
          placeholder="输入请求URL (例如: https://api.example.com/v1/users)"
          class="url-input"
          @pressEnter="sendRequest"
          allow-clear
        >
          <template #prefix>
            <global-outlined class="url-prefix-icon" />
          </template>
        </a-input>

        <a-button
          type="primary"
          @click="sendRequest"
          :loading="sending"
          class="send-button"
        >
          <thunderbolt-outlined />
          <span class="button-text">发送</span>
        </a-button>

        <a-button
          @click="saveApi"
          :loading="saving"
          class="save-button"
        >
          <save-outlined />
          <span class="button-text">保存</span>
        </a-button>
      </div>

      <div class="api-toolbar-right">
        <a-space class="toolbar-actions">
          <a-tooltip title="历史记录">
            <a-button class="action-btn" @click="showHistory">
              <history-outlined />
            </a-button>
          </a-tooltip>
          <a-tooltip title="环境设置">
            <a-button class="action-btn" @click="showEnvironments">
              <environment-outlined />
            </a-button>
          </a-tooltip>
          <a-dropdown>
            <a-button class="action-btn">
              <more-outlined />
            </a-button>
            <template #overlay>
              <a-menu class="action-menu">
                <a-menu-item key="copy">
                  <copy-outlined /> 复制为cURL
                </a-menu-item>
                <a-menu-item key="code">
                  <code-outlined /> 生成代码
                </a-menu-item>
                <a-menu-item key="export">
                  <export-outlined /> 导出
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </a-space>
      </div>
    </div>

    <!-- 请求与响应区域 -->
    <div class="api-content">
      <!-- 请求配置区域 -->
      <div class="request-section">
        <!-- 请求信息标签页 -->
        <a-tabs v-model:activeKey="activeRequestTab" class="request-tabs" :forceRender="true">
          <a-tab-pane key="params">
            <template #tab>
              <span class="tab-title">
                <search-outlined />
                查询参数
              </span>
            </template>
            <request-params-editor v-model:params="apiData.paramList" />
          </a-tab-pane>

          <a-tab-pane key="headers">
            <template #tab>
              <span class="tab-title">
                <file-text-outlined />
                请求头
              </span>
            </template>
            <request-headers-editor
              v-model:headers="apiData.headerList"
            />
          </a-tab-pane>

          <a-tab-pane key="body">
            <template #tab>
              <span class="tab-title">
                <code-outlined />
                请求体
              </span>
            </template>
            <request-body-editor-two
              ref="requestBodyEditorRef"
              :headerMap="apiData.headers"
              v-model:json="apiData.json"
              v-model:data="apiData.dataList"
              v-model:content="apiData.content"
              v-model:files="apiData.fileList"
              @update:content-type="updateContentType"
            />
          </a-tab-pane>

          <a-tab-pane key="prefix">
            <template #tab>
              <span class="tab-title">
                <play-circle-outlined />
                前置操作
              </span>
            </template>
            <operations-modal operation-title="前置操作" :operation-options="prefixOperations" v-model:operations="apiData.prefixOperations"/>
          </a-tab-pane>

          <a-tab-pane key="suffix">
            <template #tab>
              <span class="tab-title">
                <check-circle-outlined />
                后置操作
              </span>
            </template>
            <operations-modal operation-title="后置操作" :operation-options="suffixOperations" v-model:operations="apiData.suffixOperations"/>
          </a-tab-pane>

          <a-tab-pane key="auth" tab="认证">
            <auth-editor v-model:auth-type="apiData.authType" v-model:auth-config="apiData.authConfig" />
          </a-tab-pane>

          <a-tab-pane key="settings" tab="设置">
            <request-settings-editor v-model:settings="apiData.settings" />
          </a-tab-pane>
        </a-tabs>
      </div>

      <!-- 分割线 -->
      <a-divider class="resize-divider" />

      <!-- 响应区域 -->
      <div class="response-section">
        <div class="response-header">
          <div class="response-status" v-if="response">
            <div class="status-info">
              <a-tag :color="getStatusColor(response.status)" class="status-tag">
                {{ response.status }}
              </a-tag>
              <span class="status-text">{{ response.statusText }}</span>
            </div>
            <div class="response-meta">
              <span class="response-time">
                <clock-circle-outlined />
                {{ response.time }}ms
              </span>
              <span class="response-size">
                <file-outlined />
                {{ getResponseSize() }}
              </span>
            </div>
          </div>
          <div class="response-actions">
            <a-space>
              <a-tooltip title="以新窗口打开">
                <a-button size="small" type="text" class="response-action-btn" @click="openInNewWindow">
                  <export-outlined />
                </a-button>
              </a-tooltip>
              <a-tooltip title="下载响应">
                <a-button size="small" type="text" class="response-action-btn" @click="downloadResponse">
                  <download-outlined />
                </a-button>
              </a-tooltip>
              <a-tooltip title="清除响应">
                <a-button size="small" type="text" class="response-action-btn" @click="clearResponse">
                  <clear-outlined />
                </a-button>
              </a-tooltip>
            </a-space>
          </div>
        </div>

        <a-tabs v-model:activeKey="activeResponseTab">
          <a-tab-pane key="body" tab="响应体">
            <response-body-viewer
              :response="response"
              :active-view="responseViewMode"
              @change-view="responseViewMode = $event"
            />
          </a-tab-pane>

          <a-tab-pane key="headers" tab="响应头">
            <response-headers-viewer :headers="response?.headerList" />
          </a-tab-pane>

          <a-tab-pane key="cookies" tab="Cookies">
            <response-cookies-viewer :cookies="responseCookies" />
          </a-tab-pane>

          <a-tab-pane key="timeline" tab="时间轴">
            <response-timeline-viewer :response="response" />
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>

    <!-- 侧边抽屉 -->
    <a-drawer
      title="API详细信息"
      v-model:open="drawerVisible"
      width="500"
      :footer-style="{ textAlign: 'right' }"
    >
      <a-form
        ref="basicForm"
        :model="apiData"
        layout="vertical"
      >
        <a-form-item label="接口名称" name="name" :rules="[{ required: true }]">
          <a-input v-model:value="apiData.name" placeholder="请输入接口名称" />
        </a-form-item>

        <a-form-item label="接口描述" name="desc">
          <a-textarea
            v-model:value="apiData.desc"
            placeholder="请输入接口描述"
            :rows="4"
          />
        </a-form-item>

        <a-form-item label="标签" name="tags">
          <a-select
            :value="apiData.tags || []"
            @update:value="value => apiData.tags = value || []"
            mode="tags"
            :token-separators="[',']"
            placeholder="添加标签"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item label="状态" name="status">
          <a-radio-group v-model:value="apiData.status">
            <a-radio-button :value="1">已发布</a-radio-button>
            <a-radio-button :value="2">已废弃</a-radio-button>
            <a-radio-button :value="3">草稿</a-radio-button>
          </a-radio-group>
        </a-form-item>
      </a-form>

      <template #footer>
        <a-space>
          <a-button @click="drawerVisible = false">取消</a-button>
          <a-button type="primary" @click="saveApiDetails">保存</a-button>
        </a-space>
      </template>
    </a-drawer>

    <!-- 历史记录抽屉 -->
    <a-drawer
      title="请求历史记录"
      v-model:open="historyDrawerVisible"
      width="600"
    >
      <a-empty v-if="requestHistory.length === 0" />
      <a-list
        v-else
        :dataSource="requestHistory"
        :pagination="{ pageSize: 10 }"
        size="small"
      >
        <template #renderItem="{ item }">
          <a-list-item>
            <a-list-item-meta
              :title="item.url"
              :description="`${new Date(item.timestamp).toLocaleString()} · ${item.method.toUpperCase()} · ${item.status || 'N/A'}`"
            >
              <template #avatar>
                <a-tag :color="getMethodColor(item.method)">
                  {{ item.method.toUpperCase() }}
                </a-tag>
              </template>
            </a-list-item-meta>
            <template #actions>
              <a @click="loadHistoryRequest(item)">加载</a>
            </template>
          </a-list-item>
        </template>
      </a-list>
    </a-drawer>

    <!-- 环境设置抽屉 -->
    <a-drawer
      title="环境变量"
      v-model:open="envDrawerVisible"
      width="600"
    >
      <a-tabs>
        <a-tab-pane key="current" tab="当前环境">
          <environment-editor
            :env-data="environments"
            :active-env-key="activeEnvKey"
            @update:env="updateEnvironment"
            @update:active-env-key="(key) => activeEnvKey = key"
          />
        </a-tab-pane>
        <a-tab-pane key="globals" tab="全局变量">
          <globals-editor :global-data="globalVariables" @update:globals="updateGlobals"/>
        </a-tab-pane>
      </a-tabs>
    </a-drawer>
  </div>
</template>

<script setup>
import {ref, reactive, computed, onMounted, defineAsyncComponent, watch} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import axios from 'axios';
import {
  DownOutlined,
  ThunderboltOutlined,
  SaveOutlined,
  HistoryOutlined,
  EnvironmentOutlined,
  MoreOutlined,
  CopyOutlined,
  CodeOutlined,
  ExportOutlined,
  DownloadOutlined,
  ClearOutlined,
  GlobalOutlined,
  SearchOutlined,
  FileTextOutlined,
  PlayCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  FileOutlined
} from '@ant-design/icons-vue';
import {Api} from "/@/api/business/apitest/api.js";
import OperationsModal from "/@/views/business/apitest/case/components/operations-modal.vue";
import {prefixOperations, suffixOperations} from "/@/views/business/apitest/case/components/operationColumns.js";
import EnvironmentEditor from "/@/views/business/apitest/api/components/EnvironmentEditor.vue";
import GlobalsEditor from "/@/views/business/apitest/api/components/GlobalsEditor.vue";
import {EnvironmentApi} from "/@/api/business/apitest/environment-api.js";
import {globalVariableApi} from "/@/api/business/apitest/global-variable-api.js";
import RequestBodyEditor from "/@/views/business/apitest/api/components/RequestBodyEditor.vue";
import ResponseTimelineViewer from "/@/views/business/apitest/api/components/ResponseTimelineViewer.vue";
import ResponseCookiesViewer from "/@/views/business/apitest/api/components/ResponseCookiesViewer.vue";
import ResponseHeadersViewer from "/@/views/business/apitest/api/components/ResponseHeadersViewer.vue";
import ResponseBodyViewer from "/@/views/business/apitest/api/components/ResponseBodyViewer.vue";
import RequestSettingsEditor from "/@/views/business/apitest/api/components/RequestSettingsEditor.vue";
import AuthEditor from "/@/views/business/apitest/api/components/AuthEditor.vue";
import RequestHeadersEditor from "/@/views/business/apitest/api/components/RequestHeadersEditor.vue";
import RequestParamsEditor from "/@/views/business/apitest/api/components/RequestParamsEditor.vue";
import RequestBodyEditorTwo from "/@/views/business/apitest/api/components/RequestBodyEditorTwo.vue";

const props = defineProps({
  data: {
    type: Object,
    required: true
  }
})
const apiData = reactive(JSON.parse(JSON.stringify(props.data || {})));
const isEdit = computed(() => !!props.data.apiId);

// 本地状态管理（不使用全局 composables，避免状态冲突）
const environments = ref([]);
const currentEnvironment = ref({});
const globalVariables = ref([]);

const activeEnvKey = ref(null);

// 表单引用
const basicForm = ref(null);
const requestBodyEditorRef = ref(null);
const saving = ref(false);
const sending = ref(false);

// 抽屉可见性
const drawerVisible = ref(false);
const historyDrawerVisible = ref(false);
const envDrawerVisible = ref(false);

// 活动标签页
const activeRequestTab = ref('params');
const activeResponseTab = ref('body');
const responseViewMode = ref('pretty');

// 请求历史记录
const requestHistory = ref([]);

// HTTP方法选项
const methodOptions = [
  { label: 'GET', value: 'get', color: '#108ee9' },
  { label: 'POST', value: 'post', color: '#87d068' },
  { label: 'PUT', value: 'put', color: '#2db7f5' },
  { label: 'DELETE', value: 'delete', color: '#f50' },
  { label: 'PATCH', value: 'patch', color: '#722ed1' },
  { label: 'OPTIONS', value: 'options', color: '#faad14' },
  { label: 'HEAD', value: 'head', color: '#fadb14' }
];

watch(()=>props.data,(newData)=>{
  Object.assign(apiData,newData)
},{ deep: true })

function updateContentType(value){
  console.log('value',value)
  if (!value){
    let index = apiData.headerList?.findIndex(item => item.name === 'content-type')
    console.log('index',index)
    if (index >= 0){
      apiData.headerList.splice(index, 1);
      return
    }
  }
  if (!apiData.headerList){
    apiData.headerList = [];
    apiData.headerList.push({
      name: 'content-type',
      value: value,
      enabled: true
    });
  }else{
    let contentType = apiData.headerList.find(item => item.name === 'content-type')
    if (contentType){
      contentType.value = value;
      contentType.enabled = true;
    }else{
      apiData.headerList.push({
        name: 'content-type',
        value: value,
        enabled: true
      });
    }
  }
}

// 响应数据
const response = ref(null);

// 响应cookies转换为表格数据
const responseCookies = computed(() => {
  if (!response.value || !response.value.cookies) return [];
  return Object.entries(response.value.cookies).map(([name, details]) => ({
    name,
    ...details
  }));
});

// 获取状态码颜色
const getStatusColor = (status) => {
  if (!status) return 'default';
  if (status >= 200 && status < 300) return 'success';
  if (status >= 300 && status < 400) return 'processing';
  if (status >= 400 && status < 500) return 'warning';
  if (status >= 500) return 'error';
  return 'default';
};

// 获取方法颜色
const getMethodColor = (method, isBg = false) => {
  const colors = {
    get: isBg ? '#108ee9' : '#108ee9',
    post: isBg ? '#87d068' : '#87d068',
    put: isBg ? '#2db7f5' : '#2db7f5',
    delete: isBg ? '#f50' : '#f50',
    patch: isBg ? '#722ed1' : '#722ed1',
    options: isBg ? '#faad14' : '#faad14',
    head: isBg ? '#fadb14' : '#fadb14'
  };
  return colors[method?.toLowerCase()] || (isBg ? '#888' : '#888');
};

// 计算响应大小
const getResponseSize = () => {
  if (!response.value || !response.value.body) return '0 B';

  const bytes = new TextEncoder().encode(response.value.body).length;

  if (bytes < 1024) return `${bytes} B`;
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(2)} KB`;
  return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
};

// 处理方法变更
const handleMethodChange = (e) => {
  apiData.method = e.key;
};

// 保存API详情
const saveApiDetails = async () => {
  try {
    await basicForm.value.validate();
    if(isEdit.value){
      await Api.update({apiId: props.apiId, ...apiData});
    }else{
      await Api.add(apiData);
    }
    drawerVisible.value = false;
    message.success('API详情已更新');
  } catch (error) {
    console.error('保存API详情失败:', error);
  }
};

// 显示历史记录
const showHistory = () => {
  console.log('显示历史记录抽屉被调用');
  historyDrawerVisible.value = true;
};

// 加载历史请求
const loadHistoryRequest = (item) => {
  // 加载历史请求数据到当前表单
  apiData.method = item.method;
  apiData.url = item.url;
  // 其他字段...

  historyDrawerVisible.value = false;
  message.success('已加载历史请求');
};

// 显示环境设置
const showEnvironments = () => {
  envDrawerVisible.value = true;
};

// 更新环境变量
const updateEnvironment = (env) => {
  console.log('Environment updated:', env);
  if (env) {
    currentEnvironment.value = env;
    if (env.envId) {
      activeEnvKey.value = env.envId;
    }
  }
};

// 更新全局变量
const updateGlobals = (globals) => {
  console.log('Globals updated:', globals);
  if (globals) {
    globalVariables.value = globals;
  }
};

// 在新窗口打开响应
const openInNewWindow = () => {
  if (!response.value || !response.value.body) return;

  const newWindow = window.open();
  newWindow.document.write(response.value.body);
  newWindow.document.close();
};

// 下载响应
const downloadResponse = () => {
  if (!response.value || !response.value.body) return;

  const blob = new Blob([response.value.body], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `response-${new Date().getTime()}.txt`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

// 发送请求
const sendRequest = async () => {
  try {
    sending.value = true;

    // 获取当前激活的请求体数据
    // const currentRequestBody = requestBodyEditorRef.value?.getCurrentRequestBody();
    // console.log('Current Request Body:', requestBodyEditorRef.value);
    // 构建发送的 API 数据，只包含当前激活的请求体
    const sendApiData = {
      ...apiData,
      // requestBody: currentRequestBody ? { [getRequestBodyKey(apiData.contentType)]: currentRequestBody } : {},
    };

    const proxyData = {
      apiData: sendApiData,
      environmentData: currentEnvironment.value,
      globalVariables: {variableList:globalVariables.value}
    }
    // 发送API请求
    const apiResponse = await Api.apiProxy(proxyData)
      // 处理响应
      response.value = {
        status: apiResponse.data.status,
        statusText: apiResponse.data.status === 200 ? 'OK' : 'Error',
        headers: apiResponse.data.headers,
        body: apiResponse.data.text,
        time: apiResponse.data.duration,
        cookies: apiResponse.data.cookies || {},
        timeline: [
          { phase: 'Socket Creation', duration: 5 },
          { phase: 'DNS Lookup', duration: 15 },
          { phase: 'Initial Connection', duration: 30 },
          { phase: 'SSL Handshake', duration: 50 },
          { phase: 'Request Sent', duration: 10 },
          { phase: 'Waiting (TTFB)', duration: apiResponse.data.time - 110 },
          { phase: 'Content Download', duration: 5 }
        ]
      };
      // 添加到历史记录
      requestHistory.value.unshift({
        method: apiData.method,
        url: apiData.url,
        timestamp: Date.now(),
        status: apiResponse.data.status
      });
  } catch (error) {
    console.error('请求失败:', error);
    message.error('请求失败: ' + (error.message || '未知错误'));

  } finally {
    sending.value = false;
  }
};

// 清除响应数据
const clearResponse = () => {
  response.value = null;
};

// 根据内容类型获取请求体的键名
const getRequestBodyKey = (contentType) => {
  if (!contentType) return null;

  if (contentType.includes('json')) return 'json';
  if (contentType.includes('form-data')) return 'data';
  if (contentType.includes('form-urlencoded')) return 'data';
  if (contentType.includes('xml')) return 'content';
  if (contentType.includes('multipart')) return 'files';

  return 'content';
};

// 保存API
const saveApi = async () => {
  try {
    drawerVisible.value = true; // 打开详情抽屉让用户填写详细信息
  } catch (error) {
    console.error('保存失败:', error);
    message.error('保存失败: ' + (error.message || '未知错误'));
  }
};

// 加载请求历史
const loadRequestHistory = async () => {
  try {
    // 这里可以从本地存储或服务器加载
  } catch (error) {
    console.error('加载请求历史失败:', error);
    // 使用模拟数据
    requestHistory.value = [
      {
        method: 'get',
        url: 'https://api.example.com/users',
        timestamp: Date.now() - 3600000,
        status: 200
      },
      {
        method: 'post',
        url: 'https://api.example.com/auth/login',
        timestamp: Date.now() - 7200000,
        status: 200
      },
      {
        method: 'put',
        url: 'https://api.example.com/users/123',
        timestamp: Date.now() - 86400000,
        status: 204
      }
    ];
  }
};

// 复制为cURL
const copyAsCurl = () => {
  try {
    const url = `${apiData.protocol}://${apiData.url}`;
    let curlCommand = `curl -X ${apiData.method.toUpperCase()} "${url}"`;

    // 添加请求头
    apiData.headerList.forEach(header => {
      if (header.name && header.enabled !== false) {
        curlCommand += ` -H "${header.name}: ${header.value}"`;
      }
    });

    // 添加请求体
    if (apiData.method !== 'get' && apiData.method !== 'head') {
      if (apiData.contentType === 'application/json') {
        curlCommand += ` -d '${apiData.requestBody}'`;
      } else if (apiData.contentType === 'application/x-www-form-urlencoded') {
        const formData = apiData.formData
          .filter(item => item.name && item.enabled !== false)
          .map(item => `${item.name}=${encodeURIComponent(item.value || '')}`)
          .join('&');
        curlCommand += ` -d '${formData}'`;
      }
    }

    // 复制到剪贴板
    navigator.clipboard.writeText(curlCommand);
    message.success('已复制cURL命令到剪贴板');
  } catch (error) {
    console.error('复制cURL失败:', error);
    message.error('复制失败: ' + (error.message || '未知错误'));
  }
};

// 获取环境列表
const fetchEnvironments = async () => {
  try {
    const response = await EnvironmentApi.get();
    environments.value = response.data || [];

    // 设置默认环境
    if (environments.value.length > 0 && !activeEnvKey.value) {
      const firstEnv = environments.value[0];
      currentEnvironment.value = firstEnv;
      activeEnvKey.value = firstEnv.envId;
    }
  } catch (error) {
    console.error('获取环境列表失败:', error);
    message.error('获取环境列表失败');
  }
};

// 加载全局变量
const loadGlobals = async () => {
  try {
    const response = await globalVariableApi.get();
    globalVariables.value = response.data || [];
  } catch (error) {
    console.error('加载全局变量失败:', error);
    message.error('加载全局变量失败');
  }
};

// 初始化环境和全局变量
const initializeEnvironmentAndGlobals = async () => {
  await Promise.all([
    fetchEnvironments(),
    loadGlobals()
  ]);
};

// 初始化
onMounted(async () => {
  await Promise.all([
    initializeEnvironmentAndGlobals(),
    loadRequestHistory(),
  ]);
});
</script>

<style scoped>
.api-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  overflow: hidden;
}

.api-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.api-toolbar-left {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.method-button {
  min-width: 100px;
  height: 36px;
  border-radius: 6px;
  background: #fff;
  border: 1px solid #d9d9d9;
  color: #333;
  font-weight: 500;
  transition: all 0.3s ease;
}

.method-button:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.url-input {
  flex: 1;
  margin: 0;
}

:deep(.url-input .ant-input) {
  height: 36px;
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  background: #fff;
  color: #333;
  font-size: 14px;
}

:deep(.url-input .ant-input::placeholder) {
  color: #999;
}

:deep(.url-input .ant-input-prefix) {
  color: #666;
  margin-right: 8px;
}

.send-button {
  flex-shrink: 0;
  height: 36px;
  padding: 0 16px;
  border-radius: 6px;
  background: #1890ff;
  border: none;
  color: #fff;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
}

.send-button:hover {
  background: #40a9ff;
}

.save-button {
  height: 36px;
  padding: 0 16px;
  border-radius: 6px;
  background: #fff;
  border: 1px solid #d9d9d9;
  color: #333;
  font-weight: 500;
  transition: all 0.3s ease;
}

.save-button:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.button-text {
  margin-left: 8px;
}

.api-toolbar-right {
  display: flex;
  align-items: center;
}

.toolbar-actions {
  gap: 8px;
}

.action-btn {
  width: 36px;
  height: 36px;
  border-radius: 6px;
  background: #fff;
  border: 1px solid #d9d9d9;
  color: #666;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.api-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.request-section {
  flex: 1;
  padding: 16px;
  background-color: #fff;
  min-height: 250px;
  overflow: auto;
}

.resize-divider {
  cursor: row-resize;
  margin: 0;
  user-select: none;
  position: relative;
}

.resize-divider::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 30px;
  height: 5px;
  background-color: #e8e8e8;
  border-radius: 3px;
}

.response-section {
  flex: 1;
  padding: 16px;
  background-color: #fff;
  overflow: auto;
  display: flex;
  flex-direction: column;
}

.response-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.response-status {
  display: flex;
  align-items: center;
}

.status-tag {
  font-weight: bold;
  font-size: 14px;
}

.status-text {
  margin-left: 8px;
  font-weight: 500;
}

.response-time {
  margin-left: 16px;
  color: #666;
  font-size: 13px;
}

.response-size {
  margin-left: 16px;
  color: #666;
  font-size: 13px;
}

.response-actions {
  display: flex;
  align-items: center;
}

/* 增强标签页样式 */
:deep(.ant-tabs-tab) {
  padding: 12px 16px;
  font-size: 14px;
}

:deep(.ant-tabs-tab-active) {
  font-weight: 500;
}

:deep(.ant-tabs-content) {
  height: 100%;
  overflow: auto;
}

/* 增强响应体样式 */
:deep(.monaco-editor) {
  border: 1px solid #e8e8e8;
  border-radius: 2px;
}

:deep(.ant-drawer-body) {
  padding: 24px;
}

/* 响应查看器样式 */
:deep(.response-viewer) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
  font-size: 13px;
  line-height: 1.5;
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 2px;
  background-color: #fafafa;
  overflow: auto;
  height: 400px;
}

/* 响应切换视图按钮组 */
:deep(.response-view-switcher) {
  margin-bottom: 12px;
}

/* 历史记录样式 */
:deep(.ant-list-item-meta-title) {
  margin-bottom: 4px;
  font-size: 14px;
  font-weight: 500;
}

:deep(.ant-list-item-meta-description) {
  font-size: 12px;
}

/* 环境变量编辑器样式 */
:deep(.env-editor-table) {
  margin-bottom: 16px;
}

/* 美化表单 */
:deep(.ant-form-item-label > label) {
  font-weight: 500;
}

/* 响应Timeline */
:deep(.timeline-bar) {
  height: 24px;
  margin: 8px 0;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

:deep(.timeline-segment) {
  height: 100%;
  position: absolute;
  top: 0;
}

:deep(.timeline-dns) {
  background-color: #faad14;
}

:deep(.timeline-connect) {
  background-color: #1890ff;
}

:deep(.timeline-ssl) {
  background-color: #722ed1;
}

:deep(.timeline-wait) {
  background-color: #52c41a;
}

:deep(.timeline-download) {
  background-color: #13c2c2;
}

/* 响应为空时的样式 */
:deep(.response-empty) {
  padding: 48px 0;
  text-align: center;
  color: #999;
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
}
</style>