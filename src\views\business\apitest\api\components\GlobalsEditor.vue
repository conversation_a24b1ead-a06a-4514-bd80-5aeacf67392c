<template>
  <div class="globals-editor">
    <div class="editor-toolbar">
      <a-space>
        <a-button type="primary" size="small" @click="handleAddVariable">
          <plus-outlined /> 添加变量
        </a-button>
        <a-button size="small" @click="showImportModal">
          <import-outlined /> 导入变量
        </a-button>
        <a-button size="small" @click="handleExportVariables">
          <export-outlined /> 导出变量
        </a-button>
      </a-space>

      <a-input
        v-model:value="searchText"
        placeholder="搜索变量"
        class="search-input"
        size="small"
        allow-clear
        @input="handleSearch"
      >
        <template #prefix>
          <search-outlined />
        </template>
      </a-input>
    </div>

    <a-table
      :dataSource="displayedVariables"
      :columns="columns"
      :pagination="false"
      size="small"
      bordered
      :loading="loading"
      rowKey="variableId"
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.dataIndex === 'variableName'">
          <a-input
            v-if="editingIndex === index"
            v-model:value="editingVariable.variableName"
            placeholder="变量名"
            size="small"
            @pressEnter="handleSaveVariable"
          />
          <span v-else>{{ record.variableName }}</span>
        </template>

        <template v-else-if="column.dataIndex === 'variableValue'">
          <a-input
            v-if="editingIndex === index"
            v-model:value="editingVariable.variableValue"
            placeholder="变量值"
            size="small"
            @pressEnter="handleSaveVariable"
          />
          <div v-else>
            <a-typography-paragraph
              :copyable="{ text: record.variableValue }"
              :ellipsis="{ rows: 1, expandable: true, symbol: '展开' }"
              style="margin-bottom: 0"
            >
              {{ record.variableValue }}
            </a-typography-paragraph>
          </div>
        </template>

        <template v-else-if="column.dataIndex === 'category'">
          <a-select
            v-if="editingIndex === index"
            v-model:value="editingVariable.category"
            placeholder="选择分类"
            size="small"
            style="width: 100%"
          >
            <a-select-option value="认证">认证</a-select-option>
            <a-select-option value="配置">配置</a-select-option>
            <a-select-option value="业务参数">业务参数</a-select-option>
            <a-select-option value="系统参数">系统参数</a-select-option>
          </a-select>
          <a-tag v-else :color="getCategoryColor(record.category)">
            {{ record.category || '未分类' }}
          </a-tag>
        </template>

        <template v-else-if="column.dataIndex === 'description'">
          <a-input
            v-if="editingIndex === index"
            v-model:value="editingVariable.description"
            placeholder="描述"
            size="small"
            @pressEnter="handleSaveVariable"
          />
          <span v-else>{{ record.description }}</span>
        </template>

        <template v-else-if="column.dataIndex === 'status'">
          <a-switch
            v-if="editingIndex === index"
            v-model:checked="editingVariable.status"
            size="small"
          />
          <a-tag v-else :color="record.status ? 'success' : 'default'">
            {{ record.status ? '启用' : '禁用' }}
          </a-tag>
        </template>

        <template v-else-if="column.dataIndex === 'action'">
          <a-space>
            <template v-if="editingIndex === index">
              <a-tooltip title="保存">
                <a-button type="primary" size="small" @click="handleSaveVariable">
                  <check-outlined />
                </a-button>
              </a-tooltip>
              <a-tooltip title="取消">
                <a-button size="small" @click="handleCancelEdit">
                  <close-outlined />
                </a-button>
              </a-tooltip>
            </template>
            <template v-else>
              <a-tooltip title="编辑">
                <a-button type="text" size="small" @click="handleEditVariable(record, index)">
                  <edit-outlined />
                </a-button>
              </a-tooltip>
              <a-tooltip title="删除">
                <a-popconfirm
                  title="确定要删除此变量吗?"
                  @confirm="handleRemoveVariable(record.variableId)"
                >
                  <a-button type="text" danger size="small">
                    <delete-outlined />
                  </a-button>
                </a-popconfirm>
              </a-tooltip>
            </template>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 导入模态框 -->
    <a-modal
      v-model:visible="importModalVisible"
      title="导入全局变量"
      @ok="handleImportOk"
      width="600px"
    >
      <a-alert
        message="支持两种格式：1. 每行一个变量(变量名=变量值) 2. JSON格式"
        type="info"
        show-icon
        style="margin-bottom: 16px"
      />
      <a-textarea
        v-model:value="importText"
        :rows="12"
        placeholder="格式1:
baseUrl=https://api.example.com
apiVersion=v1

格式2:
[
  {&quot;variableName&quot;: &quot;baseUrl&quot;, &quot;variableValue&quot;: &quot;https://api.example.com&quot;},
  {&quot;variableName&quot;: &quot;apiVersion&quot;, &quot;variableValue&quot;: &quot;v1&quot;}
]"
      />
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import {
  PlusOutlined,
  SearchOutlined,
  ImportOutlined,
  ExportOutlined,
  EditOutlined,
  DeleteOutlined,
  CheckOutlined,
  CloseOutlined
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
const props = defineProps({
  globalData: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:globals']);

// 本地状态管理
const globalVariables = ref([]);
const loading = ref(false);

// 本地状态
const searchText = ref('');
const editingIndex = ref(-1);
const editingVariable = ref({
  variableId: null,
  variableName: '',
  variableValue: '',
  category: '',
  description: '',
  status: true
});

// 模态框状态
const importModalVisible = ref(false);
const importText = ref('');

// 表格列定义
const columns = [
  {
    title: '变量名',
    dataIndex: 'variableName',
    width: '20%',
    ellipsis: true
  },
  {
    title: '变量值',
    dataIndex: 'variableValue',
    width: '25%',
    ellipsis: true
  },
  {
    title: '分类',
    dataIndex: 'category',
    width: '10%',
    align: 'center'
  },
  {
    title: '描述',
    dataIndex: 'description',
    width: '25%',
    ellipsis: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: '10%',
    align: 'center'
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '10%',
    align: 'center'
  }
];

// 显示的变量列表（搜索过滤后）
const displayedVariables = computed(() => {
  if (!searchText.value) {
    return globalVariables.value;
  }

  const text = searchText.value.toLowerCase();
  return globalVariables.value.filter(variable =>
    variable.variableName.toLowerCase().includes(text) ||
    variable.variableValue.toLowerCase().includes(text) ||
    (variable.category && variable.category.toLowerCase().includes(text)) ||
    (variable.description && variable.description.toLowerCase().includes(text))
  );
});

// 获取分类颜色
const getCategoryColor = (category) => {
  const categoryMap = {
    '认证': 'green',
    '配置': 'blue',
    '业务参数': 'purple',
    '系统参数': 'orange'
  };
  return categoryMap[category] || 'default';
};

// 处理搜索
const handleSearch = () => {
  // 搜索逻辑已在 displayedVariables 计算属性中处理
};

// 添加变量
const handleAddVariable = () => {
  // 在表格顶部插入新行进行编辑
  editingIndex.value = 0;
  editingVariable.value = {
    variableId: null,
    variableName: '',
    variableValue: '',
    category: '',
    description: '',
    status: true
  };

  // 在数组开头插入临时变量
  globalVariables.value.unshift({ ...editingVariable.value });
};

// 编辑变量
const handleEditVariable = (record, index) => {
  editingIndex.value = index;
  editingVariable.value = { ...record };
};

// 保存变量
const handleSaveVariable = () => {
  // 验证必填字段
  if (!editingVariable.value.variableName || !editingVariable.value.variableName.trim()) {
    message.error('变量名不能为空');
    return;
  }

  if (!editingVariable.value.variableValue || !editingVariable.value.variableValue.trim()) {
    message.error('变量值不能为空');
    return;
  }

  if (editingVariable.value.variableId) {
    // 更新现有变量
    const index = globalVariables.value.findIndex(v => v.variableId === editingVariable.value.variableId);
    if (index >= 0) {
      globalVariables.value[index] = { ...editingVariable.value };
    }
  } else {
    // 添加新变量 - 替换临时变量
    globalVariables.value[0] = {
      ...editingVariable.value,
      variableId: Date.now() // 临时ID
    };
  }

  editingIndex.value = -1;
  message.success('变量已保存');
  emit('update:globals', globalVariables.value);
};

// 取消编辑
const handleCancelEdit = () => {
  if (!editingVariable.value.variableId) {
    // 如果是新添加的变量，移除临时变量
    globalVariables.value.shift();
  }
  editingIndex.value = -1;
  editingVariable.value = {
    variableId: null,
    variableName: '',
    variableValue: '',
    category: '',
    description: '',
    status: true
  };
};

// 删除变量
const handleRemoveVariable = (variableId) => {
  const index = globalVariables.value.findIndex(v => v.variableId === variableId);
  if (index >= 0) {
    globalVariables.value.splice(index, 1);
    message.success('变量已删除');
    emit('update:globals', globalVariables.value);
  }
};

// 显示导入模态框
const showImportModal = () => {
  importText.value = '';
  importModalVisible.value = true;
};

// 处理导入确认
const handleImportOk = () => {
  try {
    // 尝试JSON格式
    const imported = JSON.parse(importText.value);
    if (Array.isArray(imported)) {
      const newVariables = imported.map((variable, index) => ({
        ...variable,
        variableId: Date.now() + index // 临时ID
      }));
      globalVariables.value.push(...newVariables);
      importModalVisible.value = false;
      message.success(`成功导入 ${newVariables.length} 个变量`);
      emit('update:globals', globalVariables.value);
      return;
    }
  } catch (e) {
    // JSON解析失败，尝试简单格式
    try {
      const variables = importText.value
        .split('\n')
        .filter(line => line.trim() && line.includes('='))
        .map((line, index) => {
          const [name, ...valueParts] = line.split('=');
          return {
            variableId: Date.now() + index,
            variableName: name.trim(),
            variableValue: valueParts.join('=').trim(),
            category: '',
            description: '',
            status: true
          };
        });

      if (variables.length === 0) {
        message.error('没有找到有效的变量');
        return;
      }

      globalVariables.value.push(...variables);
      importModalVisible.value = false;
      message.success(`成功导入 ${variables.length} 个变量`);
      emit('update:globals', globalVariables.value);
    } catch (error) {
      message.error('导入失败，请检查格式');
    }
  }
};

// 导出变量
const handleExportVariables = () => {
  if (globalVariables.value.length === 0) {
    message.error('没有可导出的变量');
    return;
  }

  const exportData = globalVariables.value.map(v => ({
    variableName: v.variableName,
    variableValue: v.variableValue,
    category: v.category,
    description: v.description
  }));

  const data = JSON.stringify(exportData, null, 2);
  const blob = new Blob([data], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = 'global-variables.json';
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
  message.success('全局变量已导出');
};

// 初始化
onMounted(() => {
  // 使用传入的全局变量数据
  if (props.globalData && props.globalData.length > 0) {
    globalVariables.value = props.globalData;
  }
});

// 监听props变化
watch(() => props.globalData, (newData) => {
  if (newData && newData.length > 0) {
    globalVariables.value = newData;
  }
}, { deep: true });

// 监听全局变量变化，向父组件发送更新
watch(globalVariables, (newVariables) => {
  emit('update:globals', newVariables);
}, { deep: true });
</script>

<style scoped>
.globals-editor {
  display: flex;
  flex-direction: column;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.search-input {
  width: 220px;
}

:deep(.ant-table-cell) {
  vertical-align: middle !important;
}
</style>
