<template>
  <div ref="container" class="json-editor-container" :style="{ height: height + 'px' }"></div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch} from 'vue';
import JSONEditor from 'jsoneditor';
import 'jsoneditor/dist/jsoneditor.min.css';

const props = defineProps({
  value: {
    type: [String, Object],
    default: '{}'
  },
  mode: {
    type: String,
    default: 'tree', // 支持: 'tree' | 'text' | 'view' | 'preview'
    validator: v => ['tree', 'text', 'view', 'preview'].includes(v)
  },
  height: {
    type: Number,
    default: 300
  }
});

const emit = defineEmits(['update:value']);
const container = ref(null);
let editor = null;

onMounted(() => {
  if (container.value) {
    const options = {
      mode: props.mode,
      onChange: () => {
        try {
          const json = editor.get();
          emit('update:value', json);
        } catch (e) {
          console.error('JSON解析失败:', e);
        }
      }
    };

    editor = new JSONEditor(container.value, options);

    try {
      const parsedValue = typeof props.value === 'string' ? JSON.parse(props.value) : props.value;
      editor.set(parsedValue);
    } catch (e) {
      editor.set({error: 'Invalid JSON input'});
    }
  }
});

watch(() => props.value, (newValue) => {
  if (!editor) return;

  try {
    const current = editor.get();
    const parsedNewValue = typeof newValue === 'string' ? JSON.parse(newValue) : newValue;

    if (JSON.stringify(current) !== JSON.stringify(parsedNewValue)) {
      editor.set(parsedNewValue);
    }
  } catch (e) {
    console.error('更新 JSONEditor 失败:', e);
  }
});

onBeforeUnmount(() => {
  if (editor) {
    editor.destroy();
    editor = null;
  }
});
</script>

<style scoped>
.json-editor-container {
  width: 100%;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
}
</style>
