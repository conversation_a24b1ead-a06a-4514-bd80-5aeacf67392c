<!-- components/AuthEditor.vue -->
<template>
  <div class="auth-editor">
    <a-form layout="vertical">
      <a-form-item label="认证类型">
        <a-select v-model:value="localAuthType" @change="handleAuthTypeChange">
          <a-select-option :value="null">无认证</a-select-option>
          <a-select-option value="basic">Basic Auth</a-select-option>
          <a-select-option value="bearer">Bear<PERSON></a-select-option>
          <a-select-option value="apikey">API Key</a-select-option>
          <a-select-option value="oauth2">OAuth 2.0</a-select-option>
        </a-select>
      </a-form-item>

      <!-- Basic Auth -->
      <template v-if="localAuthType === 'basic'">
        <a-form-item label="用户名">
          <a-input
            v-model:value="localAuthConfig.username"
            placeholder="请输入用户名"
            @change="emitAuthConfigUpdate"
          />
        </a-form-item>
        <a-form-item label="密码">
          <a-input-password
            v-model:value="localAuthConfig.password"
            placeholder="请输入密码"
            @change="emitAuthConfigUpdate"
          />
        </a-form-item>

        <a-alert
          message="Basic Auth认证会自动生成Authorization请求头"
          description="格式：Authorization: Basic base64(username:password)"
          type="info"
          show-icon
        />
      </template>

      <!-- Bearer Token -->
      <template v-else-if="localAuthType === 'bearer'">
        <a-form-item label="Token">
          <a-input
            v-model:value="localAuthConfig.token"
            placeholder="请输入访问令牌"
            @change="emitAuthConfigUpdate"
          >
            <template #prefix>Bearer</template>
          </a-input>
        </a-form-item>

        <a-alert
          message="Bearer Token认证会自动生成Authorization请求头"
          description="格式：Authorization: Bearer your_token"
          type="info"
          show-icon
        />

        <a-form-item label="从环境变量中获取" style="margin-top: 16px">
          <a-input-group compact>
            <a-input
              style="width: 70%"
              addon-before="{{"
              addon-after="}}"
              placeholder="token"
              v-model:value="localAuthConfig.tokenVariable"
              @change="updateTokenFromVariable"
            />
            <a-button style="width: 30%" @click="updateTokenFromVariable">
              应用
            </a-button>
          </a-input-group>
        </a-form-item>
      </template>

      <!-- API Key -->
      <template v-else-if="localAuthType === 'apikey'">
        <a-form-item label="Key名称">
          <a-input
            v-model:value="localAuthConfig.keyName"
            placeholder="请输入Key名称"
            @change="emitAuthConfigUpdate"
          />
        </a-form-item>
        <a-form-item label="Key值">
          <a-input
            v-model:value="localAuthConfig.keyValue"
            placeholder="请输入Key值"
            @change="emitAuthConfigUpdate"
          />
        </a-form-item>
        <a-form-item label="添加位置">
          <a-radio-group v-model:value="localAuthConfig.keyIn" @change="emitAuthConfigUpdate">
            <a-radio value="header">请求头</a-radio>
            <a-radio value="query">查询参数</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-alert
          v-if="localAuthConfig.keyIn === 'header'"
          message="API Key将作为请求头添加"
          :description="`格式：${localAuthConfig.keyName || 'X-API-Key'}: ${localAuthConfig.keyValue || 'your_api_key'}`"
          type="info"
          show-icon
        />

        <a-alert
          v-else-if="localAuthConfig.keyIn === 'query'"
          message="API Key将作为查询参数添加"
          :description="`格式：?${localAuthConfig.keyName || 'api_key'}=${localAuthConfig.keyValue || 'your_api_key'}`"
          type="info"
          show-icon
        />
      </template>

      <!-- OAuth 2.0 -->
      <template v-else-if="localAuthType === 'oauth2'">
        <a-form-item label="Grant类型">
          <a-select v-model:value="localAuthConfig.grantType" @change="emitAuthConfigUpdate">
            <a-select-option value="client_credentials">Client Credentials</a-select-option>
            <a-select-option value="authorization_code">Authorization Code</a-select-option>
            <a-select-option value="password">Password</a-select-option>
            <a-select-option value="implicit">Implicit</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="访问令牌URL">
          <a-input
            v-model:value="localAuthConfig.accessTokenUrl"
            placeholder="https://auth.example.com/token"
            @change="emitAuthConfigUpdate"
          />
        </a-form-item>

        <a-form-item label="Client ID">
          <a-input
            v-model:value="localAuthConfig.clientId"
            placeholder="请输入Client ID"
            @change="emitAuthConfigUpdate"
          />
        </a-form-item>

        <a-form-item label="Client Secret">
          <a-input-password
            v-model:value="localAuthConfig.clientSecret"
            placeholder="请输入Client Secret"
            @change="emitAuthConfigUpdate"
          />
        </a-form-item>

        <template v-if="localAuthConfig.grantType === 'authorization_code'">
          <a-form-item label="授权URL">
            <a-input
              v-model:value="localAuthConfig.authorizationUrl"
              placeholder="https://auth.example.com/authorize"
              @change="emitAuthConfigUpdate"
            />
          </a-form-item>

          <a-form-item label="回调URL">
            <a-input
              v-model:value="localAuthConfig.redirectUri"
              placeholder="http://localhost/callback"
              @change="emitAuthConfigUpdate"
            />
          </a-form-item>
        </template>

        <template v-if="localAuthConfig.grantType === 'password'">
          <a-form-item label="用户名">
            <a-input
              v-model:value="localAuthConfig.username"
              placeholder="请输入用户名"
              @change="emitAuthConfigUpdate"
            />
          </a-form-item>

          <a-form-item label="密码">
            <a-input-password
              v-model:value="localAuthConfig.password"
              placeholder="请输入密码"
              @change="emitAuthConfigUpdate"
            />
          </a-form-item>
        </template>

        <a-form-item label="Scope (多个用空格分隔)">
          <a-input
            v-model:value="localAuthConfig.scope"
            placeholder="read write"
            @change="emitAuthConfigUpdate"
          />
        </a-form-item>

        <a-button
          type="primary"
          style="margin-top: 16px"
          @click="requestToken"
          :loading="requestingToken"
        >
          获取新令牌
        </a-button>

        <a-divider>当前令牌</a-divider>

        <div v-if="localAuthConfig.accessToken" class="token-info">
          <div class="token-value">
            <a-typography-paragraph copyable>
              {{ localAuthConfig.accessToken }}
            </a-typography-paragraph>
          </div>
          <div class="token-metadata">
            <div v-if="localAuthConfig.tokenType">
              <strong>Token类型:</strong> {{ localAuthConfig.tokenType }}
            </div>
            <div v-if="localAuthConfig.expiresIn">
              <strong>过期时间:</strong> {{ formatExpiresIn(localAuthConfig.expiresIn) }}
            </div>
          </div>
        </div>
        <a-empty v-else description="暂无有效令牌" />
      </template>

      <!-- 无认证 -->
      <template v-else-if="localAuthType === 'none'">
        <a-empty description="无需认证" />
      </template>
    </a-form>
  </div>
</template>

<script setup>
import { ref, reactive, defineProps, defineEmits, watch } from 'vue';
import { message } from 'ant-design-vue';
import axios from 'axios';

const props = defineProps({
  authType: {
    type: String,
    default: 'none'
  },
  authConfig: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:auth-type', 'update:auth-config']);

// 本地状态
const localAuthType = ref(props.authType);
const localAuthConfig = reactive({
  // Basic Auth
  username: '',
  password: '',

  // Bearer Token
  token: '',
  tokenVariable: '',

  // API Key
  keyName: '',
  keyValue: '',
  keyIn: 'header',

  // OAuth 2.0
  grantType: 'client_credentials',
  accessTokenUrl: '',
  authorizationUrl: '',
  clientId: '',
  clientSecret: '',
  redirectUri: '',
  scope: '',
  accessToken: '',
  tokenType: '',
  expiresIn: null,

  // 填充所有配置项
  ...props.authConfig
});

const requestingToken = ref(false);

// 监听props变化
watch(() => props.authType, (newAuthType) => {
  localAuthType.value = newAuthType;
});

watch(() => props.authConfig, (newAuthConfig) => {
  Object.assign(localAuthConfig, newAuthConfig);
}, { deep: true });

// 处理认证类型变更
const handleAuthTypeChange = (value) => {
  localAuthType.value = value;
  emit('update:auth-type', value);

  // 初始化配置
  if (value === 'apikey' && !localAuthConfig.keyIn) {
    localAuthConfig.keyIn = 'header';
  } else if (value === 'oauth2' && !localAuthConfig.grantType) {
    localAuthConfig.grantType = 'client_credentials';
  }

  emitAuthConfigUpdate();
};

// 更新授权配置
const emitAuthConfigUpdate = () => {
  emit('update:auth-config', { ...localAuthConfig });
};

// 从环境变量更新令牌
const updateTokenFromVariable = () => {
  // 实际应用中，这里会从环境变量服务获取变量值
  // 这里简化实现
  const variableName = localAuthConfig.tokenVariable;
  if (!variableName) return;

  localAuthConfig.token = `{{${variableName}}}`;
  emitAuthConfigUpdate();
  message.success(`令牌已设置为环境变量 ${variableName}`);
};

// 格式化过期时间
const formatExpiresIn = (expiresIn) => {
  if (!expiresIn) return '未知';

  // 如果是数字，假设是秒数
  if (typeof expiresIn === 'number') {
    // 计算过期时间
    const expiresAt = new Date(Date.now() + expiresIn * 1000);
    return expiresAt.toLocaleString();
  }

  return expiresIn;
};

// 请求OAuth令牌
const requestToken = async () => {
  if (!localAuthConfig.accessTokenUrl) {
    message.error('请输入访问令牌URL');
    return;
  }

  if (!localAuthConfig.clientId) {
    message.error('请输入Client ID');
    return;
  }

  try {
    requestingToken.value = true;

    let data = null;
    let headers = {
      'Content-Type': 'application/x-www-form-urlencoded'
    };

    // 根据不同的授权类型构建请求数据
    if (localAuthConfig.grantType === 'client_credentials') {
      data = new URLSearchParams({
        grant_type: 'client_credentials',
        client_id: localAuthConfig.clientId,
        client_secret: localAuthConfig.clientSecret || '',
        scope: localAuthConfig.scope || ''
      });
    } else if (localAuthConfig.grantType === 'password') {
      data = new URLSearchParams({
        grant_type: 'password',
        client_id: localAuthConfig.clientId,
        client_secret: localAuthConfig.clientSecret || '',
        username: localAuthConfig.username,
        password: localAuthConfig.password,
        scope: localAuthConfig.scope || ''
      });
    } else {
      message.error('暂不支持该授权类型的自动获取令牌');
      requestingToken.value = false;
      return;
    }

    // 发送请求
    const response = await axios.post(localAuthConfig.accessTokenUrl, data, { headers });

    // 处理响应
    if (response.data && response.data.access_token) {
      localAuthConfig.accessToken = response.data.access_token;
      localAuthConfig.tokenType = response.data.token_type || 'Bearer';
      localAuthConfig.expiresIn = response.data.expires_in;

      emitAuthConfigUpdate();
      message.success('成功获取新令牌');
    } else {
      throw new Error('响应中未包含访问令牌');
    }
  } catch (error) {
    console.error('获取令牌失败:', error);
    message.error('获取令牌失败: ' + (error.response?.data?.error_description || error.message || '未知错误'));
  } finally {
    requestingToken.value = false;
  }
};
</script>

<style scoped>
.auth-editor {
  padding: 16px 0;
}

.token-info {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 16px;
}

.token-value {
  margin-bottom: 8px;
  word-break: break-all;
}

.token-metadata {
  font-size: 12px;
  color: #666;
}
</style>