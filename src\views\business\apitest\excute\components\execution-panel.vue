<template>
  <!-- 悬浮的执行配置面板 -->
  <div
    class="execution-panel-container"
    ref="dragPanel"
    :class="{ 'collapsed': isCollapsed }"
    :style="{ top: panelPosition.y + 'px', left: panelPosition.x + 'px' }"
  >
    <div
      class="execution-panel-header"
      @mousedown="startDrag"
    >
      <div class="panel-title">
        <setting-outlined />
        <span v-if="!isCollapsed">执行配置</span>
      </div>
      <div class="panel-controls" v-if="!isCollapsed">
        <a-button type="text" @click="toggleCollapse">
          <template #icon>
            <menu-fold-outlined />
          </template>
        </a-button>
      </div>
    </div>
    <div class="execution-config-panel" v-if="!isCollapsed">
      <!-- 标签页切换 -->
      <div class="tab-container">
        <a-tabs v-model:activeKey="activeTab">
          <a-tab-pane key="functional" tab="功能测试" />
          <a-tab-pane key="performance" tab="性能测试" />
          <a-tab-pane key="beta" tab="Beta" />
        </a-tabs>
      </div>

      <!-- 运行环境配置区域 -->
      <div class="section-title">运行环境</div>
      <div class="environment-selector">
        <a-select
          v-model:value="selectedEnv"
          placeholder="开发环境"
          style="width: 100%"
          :dropdownMatchSelectWidth="false"
        >
          <a-select-option v-for="env in environments" :key="env.envId" :value="env.envId">
            <span class="env-option">
              <component :is="getEnvIcon(env.name)" />
              <span>{{ env.name }}</span>
            </span>
          </a-select-option>
        </a-select>
      </div>

      <!-- 测试数据配置区域 -->
<!--      <div class="section-title">测试数据 <a-tooltip title="配置测试数据"><info-circle-outlined /></a-tooltip></div>-->
<!--      <div class="test-data-selector">-->
<!--        <a-select-->
<!--          v-model:value="useTestData"-->
<!--          placeholder="不使用测试数据"-->
<!--          style="width: 100%"-->
<!--        >-->
<!--          <a-select-option value="none">不使用测试数据</a-select-option>-->
<!--          <a-select-option value="default">默认测试数据</a-select-option>-->
<!--          <a-select-option value="custom">自定义测试数据</a-select-option>-->
<!--        </a-select>-->
<!--      </div>-->

      <!-- 循环次数配置 -->
      <div class="config-row">
        <div class="config-col">
          <div class="section-title">循环次数 <a-tooltip title="设置测试循环执行次数"><info-circle-outlined /></a-tooltip></div>
          <a-input-number v-model:value="loopCount" :min="1" :max="100" style="width: 100%" />
        </div>
        <div class="config-col">
          <div class="section-title">线程数 <a-tooltip title="并发执行的线程数"><info-circle-outlined /></a-tooltip></div>
          <a-input-number v-model:value="threadCount" :min="1" :max="10" style="width: 100%" />
        </div>
      </div>

      <!-- 运行方式配置 -->
      <div class="section-title">运行于 <a-tooltip title="选择测试运行的位置"><info-circle-outlined /></a-tooltip></div>
      <div class="run-location-selector">
        <a-select
          v-model:value="runLocation"
          placeholder="本机"
          style="width: 100%"
        >
          <a-select-option value="local">本机</a-select-option>
          <a-select-option value="remote">远程服务器</a-select-option>
          <a-select-option value="docker">Docker容器</a-select-option>
        </a-select>
      </div>

      <!-- 通知配置 -->
      <div class="notification-config">
        <a-switch v-model:checked="enableNotification" />
        <span class="notification-text">通知</span>
      </div>

      <!-- 高级设置 -->
      <div class="advanced-settings">
        <a-collapse>
          <a-collapse-panel key="1" header="高级设置">
            <div class="advanced-option">
              <div class="option-label">失败重试</div>
              <a-switch v-model:checked="retryOnFailure" />
            </div>
            <!-- 新增的条件输入框 -->
            <div class="advanced-option" v-if="retryOnFailure">
              <div class="option-label">重试次数</div>
              <a-input-number v-model:value="reruns" :min="1" :max="10" style="width: 100%" />
            </div>

            <div class="advanced-option" v-if="retryOnFailure">
              <div class="option-label">重试间隔(秒)</div>
              <a-input-number v-model:value="rerunsDelay" :min="1" :max="60" style="width: 100%" />
            </div>
            <div class="advanced-option">
              <div class="option-label">失败停止</div>
              <a-switch v-model:checked="stopOnFailure" />
            </div>
<!--            <div class="advanced-option">-->
<!--              <div class="option-label">超时时间(秒)</div>-->
<!--              <a-input-number v-model:value="timeout" :min="1" :max="300" />-->
<!--            </div>-->
<!--            <div class="advanced-option">-->
<!--              <div class="option-label">保存测试结果</div>-->
<!--              <a-switch v-model:checked="saveResults" />-->
<!--            </div>-->
            <div class="advanced-option">
              <div class="option-label">生成HTML报告</div>
              <a-switch v-model:checked="generateHtmlReport" />
            </div>
            <div class="advanced-option">
              <div class="option-label">Pytest参数</div>
              <a-input v-model:value="pytestArgs" placeholder="-v --html=report.html" />
            </div>
          </a-collapse-panel>
        </a-collapse>
      </div>
      <!-- 操作按钮 -->
      <div class="action-buttons">
        <a-button type="primary" @click="runTest" :loading="isRunning" block>立即运行</a-button>
        <a-button type="primary" @click="addPeriodicTask" :loading="isRunning" block :icon="h(PoweroffOutlined)">定时运行</a-button>
        <a-button @click="saveConfig" :disabled="isRunning" block>保存</a-button>
      </div>
    </div>
    <!-- 折叠状态下的展开按钮 -->
    <div
      v-if="isCollapsed"
      class="collapsed-button"
      @click.stop="toggleCollapse"
      @mousedown.stop="startDrag"
    >
      <setting-outlined />
    </div>
    <JobFormModal ref="jobFormModal" />
  </div>
</template>

<script setup>
import {reactive, ref, onMounted, computed, h, watch, onUnmounted, nextTick} from 'vue';
import { message, Modal, Tag } from 'ant-design-vue';
import {EnvironmentApi, getEnvIcon} from '/@/api/business/apitest/environment-api.js';
import {
  SettingOutlined,
  CloseOutlined,
  InfoCircleOutlined,
  CodeOutlined,
  BugOutlined,
  CloudOutlined,
  EnvironmentOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined, PoweroffOutlined
} from "@ant-design/icons-vue";
import {executeApi} from "/@/api/business/apitest/execute-api.js";
import JobFormModal from '/@/views/support/periodic/components/job-form-modal.vue'


const props = defineProps({
  selectRowKeys: {
    type: Array,
    required: true
  }
});

// 添加折叠状态
const isCollapsed = ref(false);
const dragPanel = ref(null);
// 初始位置设置在右上角
const panelPosition = ref({ x: window.innerWidth - 68, y: 20 });
const isDragging = ref(false);
const dragOffset = ref({ x: 0, y: 0 });

// --------------------------- 执行面板状态 ---------------------------
const jobFormModal = ref(null)
const showExecutionPanel = ref(true);
const activeTab = ref('functional');
const selectedEnv = ref(1);
const environments = ref([]);
const useTestData = ref('none');
const loopCount = ref(1);
const threadCount = ref(1);
const runLocation = ref('local');
const reruns = ref(1);
const rerunsDelay = ref(1);
const enableNotification = ref(false);
const retryOnFailure = ref(false);
const stopOnFailure = ref(false);
const timeout = ref(30);
const saveResults = ref(true);
const generateHtmlReport = ref(true);
const isRunning = ref(false);
const pytestArgs = computed(()=>{
  const args = [
      '--count',JSON.stringify(loopCount.value),
  ]
  if (threadCount.value && threadCount.value>1){
    args.push('-n',JSON.stringify(threadCount.value))
  }
  if (retryOnFailure.value){
    args.push('--reruns',JSON.stringify(reruns.value || 0))
    args.push('--reruns-delay',JSON.stringify(rerunsDelay.value || 3))
  }
  if (stopOnFailure.value){
    args.push('-x')
  }
  return args
})
console.log(pytestArgs.value)
// --------------------------- 方法 ---------------------------

// 获取环境列表
const fetchEnvironments = async () => {
  try {
    const response = await EnvironmentApi.get();
    environments.value = response.data

    // 默认选择第一个环境
    if (environments.value.length > 0) {
      selectedEnv.value = environments.value[0].envId;
    }
  } catch (error) {
    console.error('获取环境列表失败:', error);
    message.error('获取环境列表失败');
  }
};

function toggleCollapse() {
  isCollapsed.value = !isCollapsed.value;

  // 展开/折叠时调整位置，确保不超出屏幕
  nextTick(() => {
    if (dragPanel.value) {
      const rect = dragPanel.value.getBoundingClientRect();
      const maxX = window.innerWidth - rect.width;
      const maxY = window.innerHeight - rect.height;

      if (panelPosition.value.x > maxX) {
        panelPosition.value.x = maxX;
      }

      if (panelPosition.value.y > maxY) {
        panelPosition.value.y = maxY;
      }
    }
  });

  // 保存折叠状态到本地存储
  localStorage.setItem('executionPanelCollapsed', isCollapsed.value);
}

// 保存配置
function saveConfig() {
  const config = {
    envId: selectedEnv.value,
    useTestData: useTestData.value,
    loopCount: loopCount.value,
    threadCount: threadCount.value,
    runLocation: runLocation.value,
    enableNotification: enableNotification.value,
    retryOnFailure: retryOnFailure.value,
    stopOnFailure: stopOnFailure.value,
    timeout: timeout.value,
    saveResults: saveResults.value,
    generateHtmlReport: generateHtmlReport.value,
    pytestArgs: pytestArgs.value
  };

  // 保存配置到本地存储
  localStorage.setItem('testExecutionConfig', JSON.stringify(config));
  message.success('配置已保存');
}

// 加载保存的配置
function loadSavedConfig() {
  const savedConfig = localStorage.getItem('testExecutionConfig');
  if (savedConfig) {
    try {
      const config = JSON.parse(savedConfig);
      selectedEnv.value = config.envId || 'dev';
      useTestData.value = config.useTestData || 'none';
      loopCount.value = config.loopCount || 1;
      threadCount.value = config.threadCount || 1;
      runLocation.value = config.runLocation || 'local';
      enableNotification.value = config.enableNotification || false;
      retryOnFailure.value = config.retryOnFailure || false;
      stopOnFailure.value = config.stopOnFailure || false;
      timeout.value = config.timeout || 30;
      saveResults.value = config.saveResults !== undefined ? config.saveResults : true;
      generateHtmlReport.value = config.generateHtmlReport !== undefined ? config.generateHtmlReport : true;
      pytestArgs.value = config.pytestArgs || '-v';
    } catch (error) {
      console.error('加载保存的配置失败:', error);
    }
  }
}

// 运行测试
const runTest = async () => {
  if (props.selectRowKeys.length ===0){
    message.warning('请选择要执行的用例');
    return
  }
  try {
    // 构建请求数据
    const requestData = {
      envId: selectedEnv.value,
      caseQuery:{
        caseIds: props.selectRowKeys
      },
      pytestArgs: pytestArgs.value,
      isGenerateReport: generateHtmlReport.value,
    };
    await executeApi.execute(requestData)
  } catch (error) {
    console.error('执行测试失败:', error);
    message.error('执行测试失败');
    isRunning.value = false;
  }
};

function addPeriodicTask(){
  if (props.selectRowKeys.length ===0){
    message.warning('请选择要定时执行的用例');
    return
  }
  const periodicTask = {
    taskName: "",
    task: "run_test_cases",
    args: null,
    kwargs: {
        current_user_id: 1,
        run_test_params: {
          envId: selectedEnv.value,
          caseQuery:{
            caseIds: props.selectRowKeys
          },
          pytestArgs: pytestArgs.value,
          isGenerateReport: generateHtmlReport.value,
        }
    },
    triggerType: "cron",
    triggerValue: null,
    deletedFlag: false,
    enabled: true,
    sort: null
  }
  jobFormModal.value.openUpdateModal(periodicTask)
}

// 开始拖拽 - 改进版本，支持折叠状态下的拖拽
const startDrag = (event) => {
  // 如果点击的是普通按钮，不进行拖拽
  if (event.target.closest('button') && !event.target.closest('.collapsed-button')) {
    return;
  }

  isDragging.value = true;

  // 计算鼠标在面板内的相对位置
  const rect = dragPanel.value.getBoundingClientRect();
  dragOffset.value = {
    x: event.clientX - rect.left,
    y: event.clientY - rect.top
  };

  // 添加全局事件监听
  document.addEventListener('mousemove', onDrag);
  document.addEventListener('mouseup', stopDrag);

  // 防止文本选择
  event.preventDefault();
};

// 拖拽中
const onDrag = (event) => {
  if (!isDragging.value) return;

  // 计算新位置
  panelPosition.value = {
    x: event.clientX - dragOffset.value.x,
    y: event.clientY - dragOffset.value.y
  };

  // 限制不超出屏幕边界
  const rect = dragPanel.value.getBoundingClientRect();
  const maxX = window.innerWidth - rect.width;
  const maxY = window.innerHeight - rect.height;

  if (panelPosition.value.x < 0) panelPosition.value.x = 0;
  if (panelPosition.value.y < 0) panelPosition.value.y = 0;
  if (panelPosition.value.x > maxX) panelPosition.value.x = maxX;
  if (panelPosition.value.y > maxY) panelPosition.value.y = maxY;
};

// 停止拖拽
const stopDrag = () => {
  if (!isDragging.value) return;

  isDragging.value = false;
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mouseup', stopDrag);

  // 保存位置到本地存储
  localStorage.setItem('executionPanelPosition', JSON.stringify(panelPosition.value));
};

// 窗口大小改变时调整位置
const handleResize = () => {
  if (dragPanel.value) {
    const rect = dragPanel.value.getBoundingClientRect();
    const maxX = window.innerWidth - rect.width;
    const maxY = window.innerHeight - rect.height;

    if (panelPosition.value.x > maxX) {
      panelPosition.value.x = maxX;
    }

    if (panelPosition.value.y > maxY) {
      panelPosition.value.y = maxY;
    }
  }
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mouseup', stopDrag);
  window.removeEventListener('resize', handleResize);
});

// 组件挂载时执行
onMounted(() => {
  fetchEnvironments();
  loadSavedConfig();

  // 加载折叠状态
  const savedCollapsedState = localStorage.getItem('executionPanelCollapsed');
  if (savedCollapsedState !== null) {
    isCollapsed.value = savedCollapsedState === 'true';
  }

  // 加载保存的位置
  const savedPosition = localStorage.getItem('executionPanelPosition');
  if (savedPosition) {
    try {
      panelPosition.value = JSON.parse(savedPosition);
    } catch (e) {
      console.error('加载面板位置失败', e);
    }
  }

  // 添加窗口大小改变事件监听
  window.addEventListener('resize', handleResize);

  // 确保初始位置在屏幕内
  nextTick(() => {
    handleResize();
  });
});
</script>

<style scoped>
.execution-panel-container {
  position: fixed;
  width: 380px;
  max-height: calc(100vh - 100px);
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: width 0.3s ease, height 0.3s ease, border-radius 0.3s ease;
}

/* 折叠状态样式 */
.execution-panel-container.collapsed {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  background-color: #1890ff;
}

.execution-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f0f2f5;
  border-bottom: 1px solid #e8e8e8;
  cursor: move;
  user-select: none;
}

.collapsed .execution-panel-header {
  display: none;
}

.panel-title {
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 16px;
}

.panel-title .anticon {
  margin-right: 8px;
}

.panel-controls {
  display: flex;
  align-items: center;
}

/* 折叠后的按钮样式 */
.collapsed-button {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: move; /* 指示可拖动 */
}

.collapsed-button .anticon {
  font-size: 24px;
  pointer-events: none; /* 确保图标不会干扰拖拽事件 */
}

.execution-config-panel {
  padding: 16px;
  overflow-y: auto;
  max-height: calc(100vh - 160px);
}

/* 标签页样式 */
.tab-container {
  margin-bottom: 16px;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  margin: 12px 0 8px 0;
  color: #333;
  display: flex;
  align-items: center;
}

.section-title :deep(.anticon) {
  margin-left: 4px;
  color: #999;
  font-size: 12px;
}

.environment-selector,
.test-data-selector,
.run-location-selector {
  margin-bottom: 16px;
}

.config-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.config-col {
  flex: 1;
}

.notification-config {
  display: flex;
  align-items: center;
  margin: 16px 0;
}

.notification-text {
  margin-left: 8px;
}

.advanced-settings {
  margin-top: auto;
  margin-bottom: 16px;
}

.advanced-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.option-label {
  font-size: 14px;
}

.action-buttons {
  display: flex;
  gap: 8px; /* 按钮间距 */
  margin-top: 16px;
}

/* 环境选项样式 */
.env-option {
  display: flex;
  align-items: center;
}

.env-option .anticon {
  margin-right: 8px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .execution-panel-container {
    width: 100%;
    top: 0;
    right: 0;
    height: 100vh;
    border-radius: 0;
  }

  .execution-panel-container.collapsed {
    width: 48px;
    height: 48px;
    top: 10px;
    right: 10px;
    border-radius: 50%;
  }

  .config-row {
    flex-direction: column;
    gap: 8px;
  }
}
</style>