<template>
  <div class="extract-form-container">
    <!-- 添加按钮区域 -->
    <div class="button-area">
      <a-button type="primary" @click="addExtractRule">
        <plus-outlined />
        添加提取规则
      </a-button>
    </div>

    <!-- 规则列表区域 - 使用原生 div 结构完全控制布局 -->
    <div class="rules-container" v-if="props.extractRules && props.extractRules.length > 0">
      <!-- 单个规则项 -->
      <div v-for="(rule, index) in props.extractRules" :key="index" class="rule-item">
        <div class="rule-header">
          <div class="rule-title">{{ rule.name || '未命名规则' }}</div>
          <div class="rule-actions">
            <a-button type="primary" size="small" @click="editRule(rule, index)">
              <edit-outlined />
              编辑
            </a-button>
            <a-popconfirm
              title="确定要删除这条规则吗?"
              @confirm="deleteRule(index)"
              ok-text="确定"
              cancel-text="取消"
            >
              <a-button type="danger" size="small">
                <delete-outlined />
                删除
              </a-button>
            </a-popconfirm>
          </div>
        </div>
        <div class="rule-content">
          <div class="rule-row">
            <div class="rule-label">变量名称:</div>
            <div class="rule-value">{{ rule.name }}</div>
          </div>
          <div class="rule-row">
            <div class="rule-label">源:</div>
            <div class="rule-value">{{ getSourceLabel(rule.source) }}</div>
          </div>
          <div class="rule-row">
            <div class="rule-label">表达式:</div>
            <div class="rule-value">{{ rule.expression }}</div>
          </div>
          <div class="rule-row">
            <div class="rule-label">作用域:</div>
            <div class="rule-value">
              <a-tag :color="getScopeColor(rule.scope)">{{ getScopeLabel(rule.scope) }}</a-tag>
            </div>
          </div>
          <div class="rule-row">
            <div class="rule-label">默认值:</div>
            <div class="rule-value">{{ rule.default }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <a-empty description="暂无提取规则" />
    </div>

    <!-- 编辑模态框 -->
    <case-extract-modal ref="extractModalRef" @add="handleAddRule" @edit="handleEditRule"/>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import { defineProps, defineEmits } from 'vue';
import CaseExtractModal from "/@/views/business/apitest/case/components/case-extract-modal.vue";

const props = defineProps({
  extractRules: {
    type: Array,
    default: () => [],
  }
});

const emits = defineEmits(['change', 'update:extractRules']);
const extractModalRef = ref(null);

// 源选项
const sourceOptions = [
  { label: '响应结果', value: 'response' },
  { label: '响应头', value: 'headers' },
  { label: '响应cookie', value: 'cookies' }
];

// 作用域映射
const scopeMap = {
  global: { label: '全局', color: '#52c41a' },
  case: { label: '用例', color: '#f5222d' },
  step: { label: '步骤', color: '#108ee9' }
};

// 获取源标签
const getSourceLabel = (sourceValue) => {
  const option = sourceOptions.find(opt => opt.value === sourceValue);
  return option ? option.label : sourceValue;
};

// 获取作用域标签
const getScopeLabel = (scopeValue) => {
  return scopeMap[scopeValue]?.label || scopeValue;
};

// 获取作用域颜色
const getScopeColor = (scopeValue) => {
  return scopeMap[scopeValue]?.color || '';
};

// 添加提取规则
const addExtractRule = () => {
  extractModalRef.value.showModal({
    name: '',
    scope: 'step',
    source: 'response',
    expression: '',
    default: ''
  });
};

// 编辑规则
const editRule = (rule, index) => {
  extractModalRef.value.showModal(rule, index);
};

// 删除规则
const deleteRule = (index) => {
  const updatedRules = [...props.extractRules];
  updatedRules.splice(index, 1);
  emits('update:extractRules', updatedRules);
  emits('change', updatedRules);
};

// 处理添加规则
const handleAddRule = (newRule) => {
  const updatedRules = [...props.extractRules, newRule];
  emits('update:extractRules', updatedRules);
  emits('change', updatedRules);
};

// 处理编辑规则
const handleEditRule = (updatedRule, index) => {
  const updatedRules = [...props.extractRules];
  updatedRules.splice(index, 1, updatedRule);
  emits('update:extractRules', updatedRules);
  emits('change', updatedRules);
};
</script>

<style scoped>
/* 容器样式 */
.extract-form-container {
  width: 100%;
  margin-bottom: 20px;
}

/* 按钮区域 */
.button-area {
  margin-bottom: 16px;
}

/* 规则容器 */
.rules-container {
  border: 1px solid #e8e8e8;
  border-radius: 2px;
  background-color: #fff;
  padding: 0;
  width: 100%;
}

/* 单个规则项 */
.rule-item {
  border-bottom: 1px solid #e8e8e8;
  padding: 16px;
  width: 100%;
}

.rule-item:last-child {
  border-bottom: none;
}

/* 规则标题栏 */
.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  width: 100%;
}

.rule-title {
  font-weight: 500;
  font-size: 16px;
}

.rule-actions {
  display: flex;
  gap: 8px;
}

/* 规则内容区域 */
.rule-content {
  background-color: #fafafa;
  padding: 12px;
  border-radius: 2px;
  width: 100%;
}

/* 规则行 */
.rule-row {
  display: flex;
  margin-bottom: 8px;
  width: 100%;
  align-items: center;
}

.rule-row:last-child {
  margin-bottom: 0;
}

/* 调整标签宽度和右侧间距 */
.rule-label {
  width: 100px;  /* 增加宽度 */
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  padding-right: 24px;  /* 增加右侧间距 */
  text-align: right;    /* 右对齐 */
  position: relative;   /* 为冒号定位做准备 */
}

/* 为标签添加冒号 */
.rule-label::after {
  content: "";
  position: absolute;
  right: 12px;  /* 冒号位置 */
  top: 50%;
  transform: translateY(-50%);
}

/* 调整值的样式 */
.rule-value {
  flex: 1;
  padding-left: 600px;  /* 左侧添加一些间距 */
  border-left: 1px solid #f0f0f0;  /* 添加分隔线 */
}

/* 空状态 */
.empty-state {
  padding: 24px;
  text-align: center;
}
</style>