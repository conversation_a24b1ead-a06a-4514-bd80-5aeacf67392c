<template>
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="产品线" class="smart-query-form-item">
        <a-input style="width: 300px" v-model:value="queryForm.productLineName" placeholder="产品线" />
      </a-form-item>
      <a-form-item label="模块" class="smart-query-form-item">
        <a-input style="width: 300px" v-model:value="queryForm.moduleName" placeholder="模块" />
      </a-form-item>
      <a-form-item label="功能点" class="smart-query-form-item">
        <a-input style="width: 300px" v-model:value="queryForm.featureName" placeholder="功能点" />
      </a-form-item>
      <a-form-item label="用例名称" class="smart-query-form-item">
        <a-input style="width: 300px" v-model:value="queryForm.caseName" placeholder="用例名称" />
      </a-form-item>
      <a-form-item label="优先级" class="smart-query-form-item">
        <a-input style="width: 300px" v-model:value="queryForm.priority" placeholder="优先级" />
      </a-form-item>
      <a-form-item label="标签" class="smart-query-form-item">
        <a-input style="width: 300px" v-model:value="queryForm.tags" placeholder="标签" />
      </a-form-item>
      <a-form-item label="状态" style="width: 150px" class="smart-query-form-item">
        <a-select v-model:value="queryForm.status">
          <a-select-option :value=1>启用</a-select-option>
          <a-select-option :value=0>禁用</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item class="smart-query-form-item smart-margin-left10">
        <a-button-group>
          <a-button type="primary" @click="onSearch">
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </a-button>
          <a-button @click="resetQuery">
            <template #icon>
              <ReloadOutlined />
            </template>
            重置
          </a-button>
        </a-button-group>
      </a-form-item>
    </a-row>
  </a-form>

  <a-card size="small" :bordered="false" :hoverable="true">
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="add" type="primary">
          <template #icon>
            <PlusOutlined />
          </template>
          新建扫描数据
        </a-button>
        <a-button @click="exportExcel()" type="primary">
          <template #icon>
            <FileExcelOutlined />
          </template>
          导入
        </a-button>
        <a-button type="primary" @click="addToSuite">
          <template #icon><plus-outlined /></template>
          添加到集合
        </a-button>
      </div>
    </a-row>
    <a-modal
      title="添加到集合"
      :visible="addToSuiteModalVisible"
      @ok="handleAddToSuiteOk"
      @cancel="addToSuiteModalVisible = false"
      :okButtonProps="{ props: { disabled: !selectedSuiteForAdd } }"
    >
      <a-form layout="vertical">
        <a-form-item
          label="选择集合"
          :rules="[{ required: true, message: '请选择要添加到的集合' }]"
        >
          <a-select
            v-model:value="selectedSuiteForAdd"
            placeholder="选择要添加到的集合"
            style="width: 100%"
            show-search
            :filter-option="filterOption"
          >
            <a-select-option v-for="suite in suiteList" :key="suite.suiteId" :value="suite.suiteId">
              {{ suite.suiteName }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-button type="link" @click="showCreateSuiteModal">
            <template #icon><plus-outlined /></template>
            创建新集合
          </a-button>
        </a-form-item>
      </a-form>
    </a-modal>
    <suite-add-modal ref="suiteAddRef"/>
    <a-table
      :rowSelection="{ selectedRowKeys: selectedRowKeys.value, onChange: onSelectChange }"
      :scroll="{ x: 1300 }"
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="id"
      :pagination="false"
      :loading="tableLoading"
      bordered
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'tags'">
          <a-tag v-for="tag in record.tags" :key="tag" color="blue">{{ tag }}</a-tag>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button @click="showDrawer(record)" size="small" type="link">编辑</a-button>
            <a-button @click="confirmDelete(record.enterpriseId)" size="small" danger type="link">删除</a-button>
          </div>
        </template>
      </template>
    </a-table>

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="ajaxQuery"
        @showSizeChange="ajaxQuery"
        :show-total="(total) => `共${total}条`"
      />
    </div>
  </a-card>
</template>
<script setup>
import {reactive, ref, onMounted, computed, h} from 'vue';
import {message, Modal, Tag} from 'ant-design-vue';
import { PAGE_SIZE, PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
import { useRouter } from 'vue-router';
import { smartSentry } from '/@/lib/smart-sentry';
import {CaseApi} from "/@/api/business/apitest/case-api.js";
import {PlusOutlined} from "@ant-design/icons-vue";
import {SuiteApi} from "/@/api/business/apitest/suite-api.js";
import SuiteAddModal from "/@/views/business/apitest/suite/components/suite-add-modal.vue";

  // --------------------------- 企业表格 列 ---------------------------

  const columns = ref([
      {
      title: '产品线',
      dataIndex: 'productLineName',
      minWidth: 20,
      ellipsis: true,
    },
      {
      title: '模块',
      dataIndex: 'moduleName',
      minWidth: 20,
      ellipsis: true,
    },
      {
      title: '功能点',
      dataIndex: 'featureName',
      minWidth: 20,
      ellipsis: true,
    },
    {
      title: '用例名称',
      dataIndex: 'caseName',
      minWidth: 20,
      ellipsis: true,
    },
      {
      title: '优先级',
      minWidth: 100,
      dataIndex: 'priority',
      ellipsis: true,
      customRender: ({text, record}) => {
        const colors = {
          1: '#f5222d', // 高-红色
          2: '#faad14', // 中-黄色
          3: '#52c41a'  // 低-绿色
        };
        const priority = {
          1: '高',
          2: '中',
          3: '低'
        }
        return h(Tag, { color: colors[text] || '#d9d9d9' }, () => priority[text]);
      },
    },
    {
      title: '标签',
      minWidth: 120,
      dataIndex: 'tags',
      ellipsis: true,
      customRender: ({text, record}) => {
        return h(Tag, {}, () => text?JSON.stringify(text):[]);
      },
    },
      {
      title: '描述',
      minWidth: 100,
      dataIndex: 'desc',
      ellipsis: true,
    },
    {
      title: '状态',
      minWidth: 120,
      dataIndex: 'status',
      ellipsis: true,
      customRender: ({text, record}) => {
        return text === true ? '启用' : '禁用';
      },
    },
    {
      title: '创建用户',
      minWidth: 60,
      dataIndex: 'createUserId'
    },
    {
      title: '更新用户',
      dataIndex: 'updateUserId',
      minWidth: 20,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      minWidth: 100,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      minWidth: 100,
    },
    {
      title: '操作',
      dataIndex: 'action',
      minWidth: 100,
    },
  ]);

  // --------------------------- 查询 ---------------------------

  const queryFormState = {
    caseName: '',
    productLineName: '',
    moduleName: '',
    featureName:'',
    priority:'',
    tags:'',
    status: undefined,
    pageNum: 1,
    pageSize: PAGE_SIZE,
  };
  const queryForm = reactive({ ...queryFormState });
  const tableLoading = ref(false);
  const tableData = ref([]);
  const total = ref(0);
  const selectedRowKeys = ref([])
  const selectedCases = ref([])
  const selectedSuiteForAdd = ref(null)
  const addToSuiteModalVisible = ref(false)
  const suiteList = ref([])
  const suiteAddRef = ref(null)
  function onSearch() {
    queryForm.pageNum = 1;
    ajaxQuery();
  }

  function resetQuery() {
    Object.assign(queryForm, queryFormState);
    ajaxQuery();
  }

  async function ajaxQuery() {
    try {
      tableLoading.value = true;
      let responseModel = await CaseApi.pageQuery(queryForm);
      const list = responseModel.data.items;
      total.value = responseModel.data.total;
      tableData.value = list;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  async function getSuiteOperation() {
    try {
      let response = await SuiteApi.get();
      suiteList.value = response.data.map(item => {
        return {
          suiteId: item.suiteId,
          suiteName: item.suiteName
        }
      });
    } catch (e) {
      smartSentry.captureError(e);
    }
  }
  // --------------------------- 删除 ---------------------------

  function confirmDelete(enterpriseId) {
    Modal.confirm({
      title: '确定要删除吗？',
      content: '删除后，该信息将不可恢复',
      okText: '删除',
      okType: 'danger',
      onOk() {
        console.log('OK');
      },
      cancelText: '取消',
      onCancel() {},
    });
  }

  // --------------------------- 增加、修改、详情 ---------------------------

  let router = useRouter();
  const scanDataOperateModal = ref();
  const showDrawer = async (record) => {

    await router.push({
      path: '/case/detail',
      query: {
        caseId:record.caseId
      }
    })
  };

  function add() {
    scanDataOperateModal.value.showDrawer();
  }

  // 表格选择变化事件
  function onSelectChange(keys, rows) {
    selectedRowKeys.value = keys;
    selectedCases.value = rows;
  }

  function filterOption(input, option) {
    return option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  }
  // 处理添加到集合确认
  function handleAddToSuiteOk() {
    if (!selectedSuiteForAdd.value) {
      message.warning('请选择要添加到的集合');
      return;
    }

    // 调用 /api_test/case_suite/add 接口
    // 模拟添加
    message.success('已添加到集合');
  }

  // 显示添加到集合对话框
  function addToSuite() {
    if (selectedRowKeys.value.length === 0) {
      message.warning('请至少选择一个测试用例');
      return;
    }

    addToSuiteModalVisible.value = true;
  }
  function showCreateSuiteModal(){
    suiteAddRef.value.show()
  }
  onMounted(()=>{
    ajaxQuery()
    getSuiteOperation()
  });

</script>
