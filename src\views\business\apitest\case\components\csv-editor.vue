<template>
  <a-modal
    v-model:open="Visible"
    title="CSV编辑"
    width="1200px"
    :footer="null"
    :bodyStyle="{ maxHeight: 'calc(90vh - 200px)', overflow: 'auto' }"
    @cancel="handleModalClose"
    @ok="handleOk"
  >
    <div class="csv-data-editor">
      <!-- 工具栏 -->
      <div class="csv-toolbar">
        <a-space>
          <a-button type="primary" size="small" @click="addRow">
            <plus-outlined />
            添加行
          </a-button>
          <a-button size="small" @click="importCsv">
            <import-outlined />
            导入CSV
          </a-button>
          <a-button size="small" @click="exportCsv">
            <export-outlined />
            导出CSV
          </a-button>
          <a-button size="small" @click="clearAll">
            <clear-outlined />
            清空
          </a-button>
          <a-divider type="vertical" />
          <span class="csv-info">
            共 {{ localCsvData.length }} 行数据
          </span>
        </a-space>
      </div>

      <!-- CSV表格编辑器 -->
      <div class="csv-table-container">
        <a-table
          :dataSource="localCsvData"
          :columns="dynamicColumns"
          :pagination="false"
          size="small"
          :scroll="{ x: 'max-content', y: 300 }"
          bordered
        >
          <template #headerCell="{ column }">
            <div class="csv-header-cell">
              <template v-if="column.key !== 'action'">
                <a-input
                  v-if="editingColumnKey === column.key"
                  v-model:value="editingColumnTitle"
                  @blur="finishEditColumnTitle(column)"
                  @pressEnter="finishEditColumnTitle(column)"
                  @keyup.esc="cancelEditColumnTitle"
                  size="small"
                  placeholder="列名"
                  ref="columnTitleInput"
                />
                <span
                  v-else
                  @dblclick="startEditColumnTitle(column)"
                  style="cursor: pointer; min-height: 24px; display: inline-block; width: 100%;"
                >
                  {{ column.title }}
                </span>
              </template>
              <span v-else>操作</span>
            </div>
          </template>

          <template #bodyCell="{ column, record, index }">
            <template v-if="column.key === 'action'">
              <a-space>
                <a-tooltip title="复制行">
                  <a-button type="text" size="small" @click="copyRow(index)">
                    <copy-outlined />
                  </a-button>
                </a-tooltip>
                <a-tooltip title="删除行">
                  <a-button type="text" size="small" danger @click="deleteRow(index)">
                    <delete-outlined />
                  </a-button>
                </a-tooltip>
              </a-space>
            </template>
            <template v-else-if="column.dataIndex === 'dataName'">
              <!-- 数据集列，必填 -->
              <a-input
                v-model:value="record[column.dataIndex]"
                size="small"
                placeholder="请输入数据集名称"
                :status="!record[column.dataIndex] ? 'error' : ''"
                @blur="validateDataName(record, index)"
              />
            </template>
            <template v-else>
              <a-input
                v-model:value="record[column.dataIndex]"
                size="small"
              />
            </template>
          </template>
        </a-table>
      </div>

      <!-- 添加列按钮 -->
      <div class="csv-footer">
        <a-button type="dashed" block @click="addColumn">
          <plus-outlined />
          添加列
        </a-button>
      </div>

      <!-- 原始CSV预览 -->
      <a-collapse v-model:activeKey="previewKey" size="small" class="csv-preview">
        <a-collapse-panel key="preview" header="CSV原始数据预览">
          <a-textarea
            :value="csvString"
            :rows="6"
            readonly
            class="csv-raw-text"
          />
          <div class="csv-preview-actions">
            <a-button size="small" @click="copyCsvString">
              <copy-outlined />
              复制CSV
            </a-button>
          </div>
        </a-collapse-panel>
      </a-collapse>

      <!-- 文件导入模态框 -->
      <a-modal
        v-model:visible="importModalVisible"
        title="导入CSV文件"
        @ok="handleImportConfirm"
        @cancel="() => { importModalVisible = false; importText = ''; uploadFileList = []; }"
      >
        <div class="import-modal-content">

          <div class="upload-section">
            <a-upload
              v-model:fileList="uploadFileList"
              :beforeUpload="handleBeforeUpload"
              accept=".csv"
              :maxCount="1"
              :showUploadList="false"
            >
              <a-button size="large" type="primary">
                <upload-outlined />
                选择CSV文件
              </a-button>
            </a-upload>
            <div v-if="uploadFileList.length > 0" class="file-info">
              已选择文件: {{ uploadFileList[0].name }}
            </div>
          </div>

          <a-divider>或者</a-divider>

          <div class="textarea-section">
            <a-textarea
              v-model:value="importText"
              placeholder="直接粘贴CSV内容，第一列为数据集名称"
              :rows="6"
            />
          </div>
          <a-alert
            message="导入说明"
            description="第一行为标题行：第一列标题忽略（固定为数据集），其余列标题作为字段名；从第二行开始为数据行：第一列为数据集名称，其余列为对应字段的值"
            type="info"
            show-icon
            style="margin-bottom: 16px"
          />
        </div>
      </a-modal>
    </div>
  </a-modal>

</template>

<script setup>
import { ref, computed, watch } from 'vue';
import {message, Modal} from 'ant-design-vue';
import {
  PlusOutlined,
  ImportOutlined,
  ExportOutlined,
  ClearOutlined,
  CopyOutlined,
  DeleteOutlined,
  UploadOutlined
} from '@ant-design/icons-vue';

const props = defineProps({
  stepName: {
    type: String,
    default: ''
  }
});

const emits = defineEmits(['update:csvData', 'change']);

const Visible = ref(false)
const callback = ref(null);
// 本地数据
const localCsvData = ref([]);
const previewKey = ref([]);
const importModalVisible = ref(false);
const uploadFileList = ref([]);
const importText = ref('');

// 添加编辑状态管理
const editingColumnKey = ref(null);
const editingColumnTitle = ref('');

// 开始编辑列标题
const startEditColumnTitle = (column) => {
  editingColumnKey.value = column.key;
  editingColumnTitle.value = column.title;
};

// 完成编辑列标题
const finishEditColumnTitle = (column) => {
  if (editingColumnKey.value === column.key) {
    updateColumnTitle(column, editingColumnTitle.value);
    editingColumnKey.value = null;
    editingColumnTitle.value = '';
  }
};

// 取消编辑
const cancelEditColumnTitle = () => {
  editingColumnKey.value = null;
  editingColumnTitle.value = '';
};

// 添加原始数据备份，用于检测变化
const originalCsvData = ref([]);
const originalColumnTitles = ref({})
// 添加原始数据备份，用于检测变化
const hasDataChanged = () => {
  return JSON.stringify(localCsvData.value) !== JSON.stringify(originalCsvData.value);
};

// 处理模态框关闭事件
const handleModalClose = () => {
  if (hasDataChanged()) {
    Modal.confirm({
      title: '提示',
      content: '数据已修改，是否保存更改？',
      okText: '保存',
      cancelText: '不保存',
      onOk: () => {
        handleOk();
      },
      onCancel: () => {
        handleClose();
      }
    });
  } else {
    handleClose();
  }
};
// 添加列
const addColumn = () => {
  // 生成新的列名
  let newColIndex = 1;
  let newColKey = `col${newColIndex}`;

  // 找到下一个可用的列名
  while (columnTitles.value[newColKey]) {
    newColIndex++;
    newColKey = `col${newColIndex}`;
  }

  // 初始化新列的标题
  columnTitles.value[newColKey] = `列${newColIndex}`;

  // 为所有现有行添加新列
  localCsvData.value.forEach(row => {
    row[newColKey] = '';
  });
};

// 添加行
const addRow = () => {
  const newRow = {
    id: Date.now(),
    dataName: `数据集${localCsvData.value.length + 1}` // 提供默认名称
  };

  // 基于现有的columnTitles添加列
  const existingKeys = Object.keys(columnTitles.value);
  existingKeys.forEach(key => {
    newRow[key] = '';
  });

  // 如果没有其他列，创建第一列
  if (existingKeys.length === 0) {
    newRow['col1'] = '';
    columnTitles.value['col1'] = '列1';
  }

  localCsvData.value.push(newRow);
};

// 删除行
const deleteRow = (index) => {
  localCsvData.value.splice(index, 1);
};

// 复制行
const copyRow = (index) => {
  const rowToCopy = { ...localCsvData.value[index] };
  rowToCopy.id = Date.now();
  localCsvData.value.splice(index + 1, 0, rowToCopy);
};

// 清空所有数据
const clearAll = () => {
  localCsvData.value = [];
  message.success('已清空所有数据');
};


// 导入CSV
const importCsv = () => {
  importModalVisible.value = true;
};

// 导出CSV
const exportCsv = () => {
  if (localCsvData.value.length === 0) {
    message.warning('没有数据可导出');
    return;
  }

  const blob = new Blob([csvString.value], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = `${props.stepName || 'csv-data'}.csv`;
  link.click();

  message.success('CSV文件已下载');
};

// 复制CSV字符串
const copyCsvString = () => {
  navigator.clipboard.writeText(csvString.value);
  message.success('CSV内容已复制到剪贴板');
};

// 文件上传处理
const handleBeforeUpload = (file) => {
  const reader = new FileReader();
  reader.onload = (e) => {
    importText.value = e.target.result;
  };
  reader.readAsText(file);
  return false;
};
// 确认导入
const handleImportConfirm = () => {
  if (!importText.value.trim()) {
    message.warning('请选择文件或输入CSV内容');
    return;
  }

  try {
    const lines = importText.value.trim().split('\n');
    if (lines.length === 0) {
      message.warning('CSV内容为空');
      return;
    }

    // 解析CSV数据
    const newData = [];
    const newColumnTitles = {};

    lines.forEach((line, lineIndex) => {
      const values = line.split(',').map(val => val.replace(/"/g, '').trim());

      if (values.length === 0) return;

      if (lineIndex === 0) {
        // 第一行：处理列标题
        values.slice(1).forEach((title, colIndex) => {
          const colKey = title || `col${colIndex + 1}`;
          newColumnTitles[colKey] = title || `列${colIndex + 1}`;
        });
      } else {
        // 数据行
        const row = {
          id: Date.now() + lineIndex,
          dataName: values[0] || `数据集${lineIndex}` // 第一列作为数据集名称
        };

        // 处理其余列，使用第一行的列标题作为字段名
        const columnKeys = Object.keys(newColumnTitles);
        values.slice(1).forEach((val, colIndex) => {
          const colKey = columnKeys[colIndex];
          if (colKey) {
            row[colKey] = val;
          }
        });

        newData.push(row);
      }
    });

    // 更新数据和列标题
    localCsvData.value = newData;
    columnTitles.value = newColumnTitles;

    importModalVisible.value = false;
    importText.value = '';
    uploadFileList.value = [];

    message.success(`成功导入 ${newData.length} 行数据，使用第一行作为列标题`);
  } catch (error) {
    console.error('CSV导入错误:', error);
    message.error('CSV格式解析失败，请检查格式');
  }
};
// 验证数据集名称
const validateDataName = (record, index) => {
  if (!record.dataName || !record.dataName.trim()) {
    message.error(`第 ${index + 1} 行的数据集名称不能为空`);
  }
};

// 验证所有数据
const validateAllData = () => {
  const errors = [];

  localCsvData.value.forEach((row, index) => {
    if (!row.dataName || !row.dataName.trim()) {
      errors.push(`第 ${index + 1} 行的数据集名称不能为空`);
    }
  });
  return errors;
};

function handleOk(){
  // 验证数据
  const errors = validateAllData();
  if (errors.length > 0) {
    message.error(errors[0]); // 显示第一个错误
    return;
  }

  // 构建完整的返回数据
  const resultData = localCsvData.value.map(row => {
    const cleanRow = { ...row };
    delete cleanRow.id; // 移除内部使用的id字段
    return cleanRow;
  });

  callback.value(resultData);
  Visible.value = false;
}
function handleClose(){
  callback.value = null
}

// 将动态列配置改为响应式数据
const columnTitles = ref({});


// 初始化数据时也要初始化列标题
const initializeData = () => {
  // 初始化已有列的标题
  if (localCsvData.value.length > 0) {
    const firstRow = localCsvData.value[0];
    Object.keys(firstRow)
      .filter(key => key !== 'id' && key !== 'dataName') // 排除id和dataName
      .forEach((key, index) => {
        if (columnTitles.value[key] === undefined) {
          columnTitles.value[key] = key;
        }
      });
  }
};

// 动态列配置
const dynamicColumns = computed(() => {
  const columns = [];

  // 固定的数据集列
  columns.push({
    title: '数据集',
    dataIndex: 'dataName',
    key: 'dataName',
    width: 150
  });

  // 检查是否有有效的列标题配置
  const titleKeys = Object.keys(columnTitles.value);
  if (titleKeys.length > 0) {
    const dataColumns = titleKeys.map(key => ({
      title: columnTitles.value[key],
      dataIndex: key,
      key: key,
      width: 150
    }));
    columns.push(...dataColumns);
  }

  // 操作列
  columns.push({
    title: '操作',
    key: 'action',
    width: 100,
    fixed: 'right'
  });

  return columns;
});


// 更新列标题的方法
const updateColumnTitle = (column, newTitle) => {
  // 如果列名为空，删除该列
  if (!newTitle.trim()) {
    deleteColumn(column.key);
    return;
  }

  const oldKey = column.key;
  const newKey = newTitle.trim();

  // 如果列名没有变化，直接更新标题
  if (oldKey === newKey) {
    columnTitles.value[oldKey] = newTitle;
    return;
  }

  // 检查新列名是否已存在
  if (columnTitles.value[newKey] && newKey !== oldKey) {
    message.warning('列名已存在');
    return;
  }

  // 更新所有数据行中的字段名
  localCsvData.value.forEach(row => {
    if (row.hasOwnProperty(oldKey)) {
      row[newKey] = row[oldKey];
      delete row[oldKey];
    }
  });

  // 更新列标题配置
  delete columnTitles.value[oldKey];
  columnTitles.value[newKey] = newTitle;
};

// 删除列的方法
const deleteColumn = (columnKey) => {
  // 从所有行中删除该列
  localCsvData.value.forEach(row => {
    delete row[columnKey];
  });

  // 删除列标题
  delete columnTitles.value[columnKey];

  // 检查是否还有有效的数据列
  const remainingColumns = Object.keys(columnTitles.value);
  if (remainingColumns.length === 0) {
    // 如果没有列了，清空所有数据
    localCsvData.value = [];
  }

  message.success('列已删除');
};



// 修改showCsvModal方法
function showCsvModal(data, callbackFn) {
  const initialData = data ? data.map(item => ({
    ...item,
    id: Date.now() + Math.random(),
    dataName: item.dataName || '' // 确保有dataName字段
  })) : [];

  callback.value = callbackFn;
  localCsvData.value = initialData;
  originalCsvData.value = JSON.parse(JSON.stringify(initialData));

  // 重置列标题
  columnTitles.value = {};

  // 初始化数据和列标题
  initializeData();

  // 备份初始化后的列标题
  originalColumnTitles.value = JSON.parse(JSON.stringify(columnTitles.value));

  Visible.value = true;
}

// CSV字符串预览也要使用实际的列标题
const csvString = computed(() => {
  if (localCsvData.value.length === 0) return '';

  const headers = dynamicColumns.value
    .filter(col => col.key !== 'action')
    .map(col => col.title);

  const rows = localCsvData.value.map(row => {
    const values = [];
    dynamicColumns.value
      .filter(col => col.key !== 'action')
      .forEach(col => {
        values.push(row[col.dataIndex] || '');
      });
    return values;
  });

  return [headers, ...rows]
    .map(row => row.map(cell => `"${cell || ''}"`).join(','))
    .join('\n');
});
defineExpose({
  showCsvModal,
})

</script>

<style scoped>
.csv-data-editor {
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.csv-toolbar {
  margin-bottom: 16px;
  padding: 12px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
}

.csv-info {
  color: #666;
  font-size: 12px;
}

.csv-table-container {
  background: white;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  margin-bottom: 16px;
}

.csv-header-cell {
  padding: 0;
}

.csv-footer {
  margin-bottom: 16px;
}

.csv-preview {
  background: white;
}

.csv-raw-text {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.csv-preview-actions {
  margin-top: 8px;
  text-align: right;
}

:deep(.ant-table-cell) {
  padding: 4px 8px !important;
}

:deep(.ant-input) {
  border: none;
  box-shadow: none;
}

:deep(.ant-input:focus) {
  border: 1px solid #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
.import-modal-content {
  padding: 16px 0;
}

.upload-section {
  text-align: center;
  margin-bottom: 16px;
}

.file-info {
  margin-top: 12px;
  color: #52c41a;
  font-size: 14px;
}

.textarea-section {
  margin-top: 16px;
}

:deep(.ant-divider) {
  margin: 24px 0;
  text-align: center;
}

:deep(.ant-upload) {
  display: inline-block;
}
</style>