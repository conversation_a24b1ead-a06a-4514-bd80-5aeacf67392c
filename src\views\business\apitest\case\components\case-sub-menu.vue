<template>
  <a-sub-menu :key="'group-'+menuItem.groupId" @click="onSubMenuClick">
    <template #title>
      <folder-outlined />
      <span>{{menuItem.name}}</span>
    </template>
    <a-menu-item v-for="item in menuItem.cases" :key="'case-'+item.caseId">
      <file-outlined />
      <span>
        {{item.caseName}}
      </span>
    </a-menu-item>
    <template v-for="item in menuItem.children" :key="item.groupId">
      <template v-if="item.children.length ===0 && item.cases.length ===0">
        <a-menu-item :key="'group-'+item.groupId">
          <folder-outlined />
          <span>{{item.name}}</span>
        </a-menu-item>
      </template>
      <template v-else>
        <case-sub-menu :menu-item="item" />
      </template>
    </template>
  </a-sub-menu>
</template>

<script setup>
import {
  PlusOutlined, FolderOutlined, FileOutlined, EditOutlined,
  DeleteOutlined, MoreOutlined, SaveOutlined, PlayCircleOutlined,
  FolderAddOutlined
} from '@ant-design/icons-vue';
// 定义props
const props = defineProps({
  menuItem: {
    type: Object,
    required: true
  }
});
const onSubMenuClick = (keyPath, event) => {
  console.log('onSubMenuClick', keyPath);
};

// 定义emits
defineEmits(['select']);
</script>