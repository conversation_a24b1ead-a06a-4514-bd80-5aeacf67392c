<template>
  <div class="execution-list-container">
    <h1>接口自动化测试执行记录</h1>
    <div class="table-operations">
      <a-button type="primary" @click="fetchExecutions">刷新</a-button>
    </div>
    <a-table
      :columns="columns"
      :data-source="executions"
      :loading="loading"
      row-key="execution_id"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'progress'">
          <a-progress
            :percent="calculateProgress(record)"
            :success="{ percent: calculateSuccessPercent(record) }"
            :status="record.status === 'running' ? 'active' : 'normal'"
          />
        </template>
        <template v-else-if="column.key === 'status'">
          <a-tag v-if="record.status === 'passed'" color="success">
            <template #icon><check-circle-outlined /></template>
            通过
          </a-tag>
          <a-tag v-else-if="record.status === 'failed'" color="error">
            <template #icon><close-circle-outlined /></template>
            失败
          </a-tag>
          <a-tag v-else-if="record.status === 'running'" color="processing">
            <template #icon><loading-outlined /></template>
            运行中
          </a-tag>
          <a-tag v-else>{{ record.status }}</a-tag>
        </template>
        <template v-else-if="column.key === 'action'">
          <a-button type="primary" @click="viewDetails(record)">
            <template #icon><eye-outlined /></template>
            查看详情
          </a-button>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  LoadingOutlined,
  EyeOutlined
} from '@ant-design/icons-vue';

const router = useRouter();
const executions = ref([]);
const loading = ref(false);

const columns = [
  {
    title: '执行ID',
    dataIndex: 'execution_id',
    key: 'execution_id',
  },
  {
    title: '执行名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '环境',
    dataIndex: 'env_id',
    key: 'env_id',
  },
  {
    title: '用例数',
    dataIndex: 'total_cases',
    key: 'total_cases',
  },
  {
    title: '执行进度',
    key: 'progress',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '创建时间',
    dataIndex: 'create_time',
    key: 'create_time',
  },
  {
    title: '操作',
    key: 'action',
  },
];

const fetchExecutions = async () => {
  loading.value = true;
  try {
    const response = await fetch('/api/executions');
    executions.value = await response.json();
  } catch (error) {
    console.error('获取执行记录失败:', error);
  } finally {
    loading.value = false;
  }
};

const calculateProgress = (record) => {
  return Math.round(((record.passed_cases + record.failed_cases) / record.total_cases) * 100);
};

const calculateSuccessPercent = (record) => {
  return Math.round((record.passed_cases / record.total_cases) * 100);
};

const viewDetails = (record) => {
  router.push(`/executions/${record.execution_id}`);
};

onMounted(() => {
  fetchExecutions();
});
</script>

<style scoped>
.execution-list-container {
  padding: 24px;
}

.table-operations {
  margin-bottom: 16px;
}
</style>