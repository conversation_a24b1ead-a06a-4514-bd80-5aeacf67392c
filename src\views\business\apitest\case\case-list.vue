<template>
  <div class="case-management-container">
    <!-- 左侧分组树 -->
    <div class="group-tree-container">
      <div class="group-tree-header">
        <h3>用例分组</h3>
        <a-button type="primary" size="small" @click="showCreateGroupModal">
          <template #icon><plus-outlined /></template>
          新建分组
        </a-button>
      </div>
      <!-- 树形控件 -->
      <a-spin :spinning="treeLoading">
        <case-menu @on-click="onClick"/>
      </a-spin>
    </div>

    <!-- 右侧内容区 -->
    <div class="content-container">
      <!-- 无选中用例时显示 -->
      <div v-if="!currentKey" class="empty-state">
        <a-empty description="请从左侧选择一个用例或分组" />
      </div>
      <!-- 选中用例时显示用例详情 -->
      <div v-else-if="currentKey?.startsWith('case-')" class="case-detail">
        <case-edit :case-id="keyId"/>
      </div>
      <!-- 选中分组时显示分组下的用例列表 -->
      <div v-else-if="currentKey?.startsWith('group-')" class="group-cases">
        <case-execute :group-id="keyId"/>
      </div>

    </div>

  </div>
</template>
  
<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import {
  PlusOutlined, FolderOutlined, FileOutlined, EditOutlined,
  DeleteOutlined, MoreOutlined, SaveOutlined, PlayCircleOutlined,
  FolderAddOutlined
} from '@ant-design/icons-vue';
import CaseEdit from "/@/views/business/apitest/case/components/case-edit.vue";
import CaseMenu from "/src/views/business/apitest/case/components/case-menu.vue";
import CaseExecute from "/@/views/business/apitest/case/components/case-execute.vue";

// 树形数据相关
const treeData = ref([]);
const treeLoading = ref(false);
const currentKey = ref(null);
const keyId = computed(()=>{
  return getKeyId(currentKey.value)

})

// 分组相关
const groupModalVisible = ref(false);
const groupModalMode = ref('create'); // 'create' 或 'edit'
const groupForm = reactive({
  id: null,
  name: '',
  parentId: null
});
const groupTreeData = ref([]);


function getKeyId(key){
  if (key){
    return parseInt(key.split('-')[1]);
  }else{
    return null;
  }
}
const onClick = (key) =>{
  currentKey.value = key;
  console.log(currentKey.value)
}
</script>

<style scoped>
.case-management-container {
  display: flex;
  height: 100%;
  overflow: hidden;
}

.group-tree-container {
  width: 280px;
  border-right: 1px solid #e8e8e8;
  padding: 16px;
  overflow-y: auto;
  background-color: #f5f5f5;
}

.group-tree-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.group-tree-header h3 {
  margin: 0;
}

.content-container {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.tree-node {
  display: flex;
  align-items: center;
  width: 100%;
}

.group-icon, .case-icon {
  margin-right: 8px;
}

.node-title {
  flex: 1;
}

.tree-node-actions {
  display: none;
}

.tree-node:hover .tree-node-actions {
  display: block;
}

.action-icon {
  cursor: pointer;
  color: #999;
}

.action-icon:hover {
  color: #1890ff;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.group-header h2 {
  margin: 0;
}

.case-header {
  margin-bottom: 16px;
}

.steps-toolbar {
  margin-bottom: 16px;
}

.api-steps {
  margin-top: 16px;
}

.action-buttons {
  white-space: nowrap;
}
</style>