import {getRequest, postRequest} from "/@/lib/axios.js";

export const TaskApi = {
    pageQuery(params){
        return getRequest('/task/page',params);
    },
    add(data){
        return postRequest('/task/add',data);
    },
    update(data){
        return postRequest('/task/update',data);
    },
    delete(taskId){
        return getRequest(`/task/delete/${taskId}`);
    },
    queryJobInfo(taskId){
        return getRequest(`/task/task-info/${taskId}`);
    }
}