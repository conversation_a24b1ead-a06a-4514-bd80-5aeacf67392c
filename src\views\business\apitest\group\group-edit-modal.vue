<template>
  <a-modal
  v-model:open="groupModelVisible"
  :title="groupData.groupId ? '编辑目录' : '添加目录'"
  @ok="handleOk"
  destroyOnClose
  >
    <a-form :model=groupData ref="groupRef" :rules="groupRules">
      <a-form-item name="name">
        <a-input v-model:value="groupData.name" />
      </a-form-item>
      <a-form-item name="parentId">
        <group-tree-select :group-type="groupType" v-model:group-key="groupData.parentId"/>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>

import {reactive, ref} from "vue";
import {GroupApi} from "/@/api/business/apitest/group-api.js";
import {message} from "ant-design-vue";
import GroupTreeSelect from "/@/views/business/apitest/group/group-tree-select.vue";
const props = defineProps({
  groupType: {
    type: String,
    default: null
  }
})

const emits = defineEmits(['load-tree','update:group-key'])

const groupData = reactive({
  groupId:null,
  name: '',
  parentId: null,
  type: props.groupType
})
const groupModelVisible = ref(false)
const groupRef = ref(null)
const groupRules = ref({
  name: [
    { required: true, message: '请输入目录名称', trigger: 'blur' },
  ],
  parentId: [
    { required: false, message: '请选择父级目录', trigger: 'blur' },
  ]
})

async function handleOk(){
  await groupRef.value.validate()
  try{
    if (!groupData.groupId){
      await GroupApi.add(groupData)
    }else{
      await GroupApi.update(groupData)
    }
  }catch (e) {
    message.error(groupData.groupId? '更新目录失败' : '添加目录失败')
  }finally {
    emits('load-tree')
    groupModelVisible.value = false
  }


}

const showModel = (Data) => {
  Object.assign(groupData, Data)
  groupModelVisible.value = true
}

defineExpose({
  showModel,
})


</script>