import { ref, reactive, computed, watch, readonly } from 'vue';
import { message } from 'ant-design-vue';
import { EnvironmentApi } from '/@/api/business/apitest/environment-api.js';

// 全局环境状态
const environments = ref([]);
const currentEnvironment = ref(null);
const loading = ref(false);
const error = ref(null);

/**
 * 环境管理 Composable
 * 提供统一的环境变量管理功能
 */
export function useEnvironment() {
  // 获取环境列表
  const fetchEnvironments = async () => {
    try {
      loading.value = true;
      error.value = null;
      const response = await EnvironmentApi.get();
      environments.value = response.data || [];
      
      // 如果没有当前环境且有可用环境，设置第一个为当前环境
      if (!currentEnvironment.value && environments.value.length > 0) {
        currentEnvironment.value = environments.value[0];
      }
      
      return environments.value;
    } catch (err) {
      error.value = err.message || '获取环境列表失败';
      message.error(error.value);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  // 切换当前环境
  const switchEnvironment = (envId) => {
    const env = environments.value.find(e => e.envId === envId);
    if (env) {
      currentEnvironment.value = { ...env };
      message.success(`已切换到环境: ${env.name}`);
      return currentEnvironment.value;
    } else {
      message.error('环境不存在');
      return null;
    }
  };

  // 更新环境变量
  const updateEnvironmentVariables = (variables) => {
    if (!currentEnvironment.value) {
      message.error('请先选择环境');
      return false;
    }
    
    try {
      // 验证变量格式
      const validVariables = variables.filter(v => v.name && v.name.trim());
      currentEnvironment.value.variables = validVariables;
      message.success('环境变量已更新');
      return true;
    } catch (err) {
      message.error('更新环境变量失败');
      return false;
    }
  };

  // 添加环境变量
  const addEnvironmentVariable = (name, value) => {
    if (!currentEnvironment.value) {
      message.error('请先选择环境');
      return false;
    }
    
    if (!name || !name.trim()) {
      message.error('变量名不能为空');
      return false;
    }

    if (!currentEnvironment.value.variables) {
      currentEnvironment.value.variables = [];
    }

    // 检查是否已存在同名变量
    const existingIndex = currentEnvironment.value.variables.findIndex(v => v.name === name);
    if (existingIndex >= 0) {
      currentEnvironment.value.variables[existingIndex].value = value;
      message.success('变量已更新');
    } else {
      currentEnvironment.value.variables.push({ name, value });
      message.success('变量已添加');
    }
    
    return true;
  };

  // 删除环境变量
  const removeEnvironmentVariable = (name) => {
    if (!currentEnvironment.value || !currentEnvironment.value.variables) {
      return false;
    }
    
    const index = currentEnvironment.value.variables.findIndex(v => v.name === name);
    if (index >= 0) {
      currentEnvironment.value.variables.splice(index, 1);
      message.success('变量已删除');
      return true;
    }
    
    return false;
  };

  // 批量导入环境变量
  const importEnvironmentVariables = (text) => {
    try {
      const variables = text
        .split('\n')
        .filter(line => line.trim() && line.includes('='))
        .map(line => {
          const [name, ...valueParts] = line.split('=');
          return {
            name: name.trim(),
            value: valueParts.join('=').trim()
          };
        });

      if (variables.length === 0) {
        message.error('没有找到有效的变量');
        return false;
      }

      updateEnvironmentVariables(variables);
      return true;
    } catch (err) {
      message.error('导入失败，请检查格式');
      return false;
    }
  };

  // 导出环境变量
  const exportEnvironmentVariables = () => {
    if (!currentEnvironment.value || !currentEnvironment.value.variables) {
      message.error('没有可导出的变量');
      return null;
    }

    const exportData = {
      name: currentEnvironment.value.name,
      variables: currentEnvironment.value.variables
    };

    return JSON.stringify(exportData, null, 2);
  };

  // 获取环境变量值
  const getEnvironmentVariable = (name) => {
    if (!currentEnvironment.value || !currentEnvironment.value.variables) {
      return null;
    }
    
    const variable = currentEnvironment.value.variables.find(v => v.name === name);
    return variable ? variable.value : null;
  };

  // 计算属性
  const environmentOptions = computed(() => 
    environments.value.map(env => ({
      label: env.name,
      value: env.envId,
      env
    }))
  );

  const currentEnvironmentId = computed(() => 
    currentEnvironment.value ? currentEnvironment.value.envId : null
  );

  const currentEnvironmentVariables = computed(() => 
    currentEnvironment.value ? (currentEnvironment.value.variables || []) : []
  );

  return {
    // 状态
    environments: readonly(environments),
    currentEnvironment: readonly(currentEnvironment),
    loading: readonly(loading),
    error: readonly(error),
    
    // 计算属性
    environmentOptions,
    currentEnvironmentId,
    currentEnvironmentVariables,
    
    // 方法
    fetchEnvironments,
    switchEnvironment,
    updateEnvironmentVariables,
    addEnvironmentVariable,
    removeEnvironmentVariable,
    importEnvironmentVariables,
    exportEnvironmentVariables,
    getEnvironmentVariable
  };
}
