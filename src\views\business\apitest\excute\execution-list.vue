<template>
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="执行名称" class="smart-query-form-item">
        <a-input style="width: 300px" v-model:value="queryForm.executionName" placeholder="执行名称" />
      </a-form-item>
      <a-form-item label="执行环境" class="smart-query-form-item">
        <a-input style="width: 300px" v-model:value="queryForm.envName" placeholder="执行环境" />
      </a-form-item>
      <a-form-item label="状态" style="width: 150px" class="smart-query-form-item">
        <a-select v-model:value="queryForm.status">
          <a-select-option value="running">执行中</a-select-option>
          <a-select-option value='finished'>执行完成</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item class="smart-query-form-item smart-margin-left10">
        <a-button-group>
          <a-button type="primary" @click="onSearch">
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </a-button>
          <a-button @click="resetQuery">
            <template #icon>
              <ReloadOutlined />
            </template>
            重置
          </a-button>
        </a-button-group>
      </a-form-item>
    </a-row>
  </a-form>

  <a-card size="small" :bordered="false" :hoverable="true">
    <!-- 批量操作工具栏 -->
    <div class="table-toolbar" v-if="selectedRowKeys.length > 0">
      <a-space>
        <span>已选择 {{ selectedRowKeys.length }} 项</span>
        <a-button type="primary" danger size="small" @click="confirmBatchDelete">
          批量删除
        </a-button>
        <a-button size="small" @click="clearSelection">
          取消选择
        </a-button>
      </a-space>
    </div>
    <a-table
      :rowSelection="{ selectedRowKeys: selectedRowKeys.value, onChange: onSelectChange }"
      :scroll="{ x: 1300 }"
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="id"
      :pagination="false"
      :loading="tableLoading"
      bordered
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'status'">
          <a-tag v-if="record.status === 'finished'" color="success">
            <template #icon><check-circle-outlined /></template>
            完成
          </a-tag>
          <a-tag v-else-if="record.status === 'running'" color="processing">
            <template #icon><loading-outlined /></template>
            运行中
          </a-tag>
          <a-tag v-else>{{ record.status }}</a-tag>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button v-if="record.reportPath" @click="showReport(record)" size="small" type="link">查看报告</a-button>
            <a-button @click="viewDetails(record)" size="small" type="link">
              <template #icon><eye-outlined /></template>
              查看详情
            </a-button>
            <a-button @click="confirmDelete(record.executionId)" size="small" danger type="link">删除</a-button>
          </div>
        </template>
      </template>
    </a-table>

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="ajaxQuery"
        @showSizeChange="ajaxQuery"
        :show-total="(total) => `共${total}条`"
      />
    </div>
  </a-card>
</template>
<script setup>
import {ref, onMounted, reactive} from 'vue';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  LoadingOutlined,
  EyeOutlined,
  SearchOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue';
import {PAGE_SIZE, PAGE_SIZE_OPTIONS} from "/@/constants/common-const.js";
import {message, Modal} from "ant-design-vue";
import {smartSentry} from "/@/lib/smart-sentry.js";
import {ExecutionApi} from "/@/api/business/apitest/execution-api.js";
import { useRouter } from 'vue-router';
import {getRequest} from "/@/lib/axios.js";
  // --------------------------- 企业表格 列 ---------------------------

const columns = [
  // {
  //   title: '执行ID',
  //   dataIndex: 'executionId',
  //   key: 'executionId',
  // },
  {
    title: '执行名称',
    dataIndex: 'executionName',
    key: 'executionName',
  },
  {
    title: '环境',
    dataIndex: 'envName',
    key: 'envName',
  },
  {
    title: '用例数',
    dataIndex: 'totalCases',
    key: 'totalCases',
  },
    {
    title: '通过',
    dataIndex: 'passedCases',
    key: 'passedCases',
  },
    {
    title: '失败',
    dataIndex: 'failedCases',
    key: 'failedCases',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
  },
];

// --------------------------- 查询 ---------------------------

const queryFormState = {
  executionName:undefined,
  envName: undefined,
  status: undefined,
  pageNum: 1,
  pageSize: PAGE_SIZE,
};
const queryForm = reactive({ ...queryFormState });
const tableLoading = ref(false);
const tableData = ref([]);
const total = ref(0);
const selectedRowKeys = ref([])
const selectedCases = ref([])
const router = useRouter()

function onSearch() {
  queryForm.pageNum = 1;
  ajaxQuery();
}

function resetQuery() {
  Object.assign(queryForm, queryFormState);
  ajaxQuery();
}

async function ajaxQuery() {
  try {
    tableLoading.value = true;
    let responseModel = await ExecutionApi.pageQuery(queryForm);
    const list = responseModel.data.items;
    total.value = responseModel.data.total;
    tableData.value = list;
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    tableLoading.value = false;
  }
}
// --------------------------- 删除 ---------------------------

async function confirmDelete(executionId) {
  Modal.confirm({
    title: '确定要删除吗？',
    content: '删除后，该执行记录将不可恢复',
    okText: '删除',
    okType: 'danger',
    async onOk() {
      try {
        // 这里应该调用删除API
        await ExecutionApi.delete(executionId);
        message.success('删除成功');
        // 重新查询数据
        await ajaxQuery();
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败: ' + (error.message || '未知错误'));
      }
    },
    cancelText: '取消',
    onCancel() {
      console.log('取消删除');
    },
  });
}

// 批量删除确认
async function confirmBatchDelete() {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择要删除的记录');
    return;
  }

  Modal.confirm({
    title: `确定要删除选中的 ${selectedRowKeys.value.length} 条记录吗？`,
    content: '删除后，这些执行记录将不可恢复',
    okText: '删除',
    okType: 'danger',
    async onOk() {
      try {
        // 这里应该调用批量删除API
        await ExecutionApi.batchDelete(selectedRowKeys.value);
        message.success(`成功删除 ${selectedRowKeys.value.length} 条记录`);
        // 清除选择
        clearSelection();
        // 重新查询数据
        await ajaxQuery();
      } catch (error) {
        console.error('批量删除失败:', error);
        message.error('批量删除失败: ' + (error.message || '未知错误'));
      }
    },
    cancelText: '取消',
    onCancel() {
      console.log('取消批量删除');
    },
  });
}

// 清除选择
function clearSelection() {
  selectedRowKeys.value = [];
  selectedCases.value = [];
}

// 表格选择变化事件
function onSelectChange(keys, rows) {
  selectedRowKeys.value = keys;
  selectedCases.value = rows;
}

function viewDetails(record){
  router.push({
    path: '/execution/detail',
    query: {
      executionId:record.executionId
    }
  })
}
async function showReport(record){
  let reportUrl = `${import.meta.env.VITE_APP_API_URL}/allure-report/${record.reportPath}/index.html`
  window.open(reportUrl)
}

onMounted(()=>{
  ajaxQuery()
});

</script>

<style scoped>
.table-toolbar {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f5f5f5;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
}
</style>
