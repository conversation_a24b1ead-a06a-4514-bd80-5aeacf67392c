import {getRequest, postRequest} from "/@/lib/axios.js";
import {BugOutlined, CloudOutlined, CodeOutlined, EnvironmentOutlined} from "@ant-design/icons-vue";

export const EnvironmentApi = {
    pageQuery: (param) => {
        return getRequest('/api_test/environment/page', param);
    },
    add(data){
        return postRequest('/api_test/environment/add', data);
    },
    update(data){
        return postRequest('/api_test/environment/update', data);
    },
    delete(envId){
        return getRequest('/api_test/environment/delete', {envId: envId});
    },
    get(params){
        return getRequest('/api_test/environment/get', params);
    }
}

export function getEnvIcon(envName) {
  if (envName.includes('开发')) return CodeOutlined;
  if (envName.includes('测试')) return BugOutlined;
  if (envName.includes('生产')) return CloudOutlined;
  return EnvironmentOutlined;
}


