<template>
  <div class="execution-detail-container">
    <a-page-header
      :title="`执行详情 #${executionId}`"
      @back="goBack"
    />

    <a-descriptions title="执行信息" bordered>
      <a-descriptions-item label="执行ID">{{ execution.executionId }}</a-descriptions-item>
      <a-descriptions-item label="执行名称">{{ execution.executionName }}</a-descriptions-item>
      <a-descriptions-item label="环境">{{ execution.envName }}</a-descriptions-item>
      <a-descriptions-item label="状态">
        <a-tag :color="getStatusColor(execution.status)">{{ execution.status }}</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="用例统计">
        总数: {{ execution.totalCases }} |
        通过: {{ execution.passedCases }} |
        失败: {{ execution.failedCases }}
      </a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ execution.createTime }}</a-descriptions-item>
    </a-descriptions>

    <div class="case-list-header">
      <h2>测试用例列表</h2>
    </div>

    <a-collapse v-model:activeKey="activeKeys">
      <a-collapse-panel
        v-for="caseExec in caseExecutions"
        :key="caseExec.caseExecId"
        :header="getCaseHeader(caseExec)"
      >
        <a-descriptions bordered size="small">
          <a-descriptions-item label="用例ID">{{ caseExec.caseId }}</a-descriptions-item>
          <a-descriptions-item label="用例名称">{{ caseExec.caseName }}</a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(caseExec.status)">{{ caseExec.status }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="步骤统计">
            总数: {{ caseExec.totalSteps }} |
            通过: {{ caseExec.passedSteps }} |
            失败: {{ caseExec.failedSteps }}
          </a-descriptions-item>
          <a-descriptions-item label="开始时间">{{ caseExec.startTime }}</a-descriptions-item>
          <a-descriptions-item label="结束时间">{{ caseExec.endTime }}</a-descriptions-item>
        </a-descriptions>
        <a-table
          :columns="stepColumns"
          :data-source="caseExec.stepExecutions"
          size="small"
          row-key="stepExecId"
          :pagination="false"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">{{ record.status }}</a-tag>
            </template>
            <template v-else-if="column.key === 'action'">
              <div class="action-button">
                <a-button
                  type="link"
                  @click="viewOperationResults(record)"
                  :disabled="!hasOperationResults(record)"
                >
                  Operations
                  <span v-if="record.results" style="font-size: 12px; color: #999;">
                    ({{ getResultsCount(record) }})
                  </span>
                </a-button>
                <a-button type="link" @click="viewStepDetail(record)">接口详情</a-button>
              </div>
            </template>
          </template>
        </a-table>
      </a-collapse-panel>
    </a-collapse>
    <api-detail-modal ref="detailRef" :env-data="execution.environmentData" :global-data="execution.globalVariable"/>
    <operation-results-modal ref="operationResultsRef" />
  </div>
</template>

<script setup>
import {ref, onMounted, reactive} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {ExecutionApi} from "/@/api/business/apitest/execution-api.js";
import ApiDetailModal from "/@/views/business/apitest/api/components/api-detail-modal.vue";
import OperationResultsModal from "./operation-results-modal.vue";

const route = useRoute();
const router = useRouter();
const executionId = ref(route.query.executionId);
const execution = reactive({});
const caseExecutions = ref([]);
const activeKeys = ref([]);
const detailRef = ref(null);
const operationResultsRef = ref(null);

const stepColumns = [
  {
    title: 'API 名称',
    dataIndex: 'apiName',
    key: 'apiName',
    width: 200,
    ellipsis: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '是否跳过',
    dataIndex: 'skip',
    key: 'skip',
    width: 100,
    customRender: ({text}) => {
      return text ? "是": "否"
    }
  },
  {
    title: '前后置操作',
    key: 'operationResults',
    width: 120,
    customRender: ({record}) => {
      let results = record.results;

      // 处理字符串格式的 results
      if (typeof results === 'string') {
        try {
          results = JSON.parse(results);
        } catch (e) {
          return '-';
        }
      }

      if (!results) return '-';

      // 如果不是数组，包装成数组
      if (!Array.isArray(results)) {
        if (typeof results === 'object') {
          results = [results];
        } else {
          return '-';
        }
      }

      if (results.length === 0) return '-';

      const successCount = results.filter(r => r.success === true).length;
      const failCount = results.length - successCount;

      return `${successCount}成功/${failCount}失败`;
    }
  },
  {
    title: '错误信息',
    dataIndex: 'errorMessage',
    key: 'errorMessage',
    ellipsis: true
  },
  {
    title: '执行时长(秒)',
    dataIndex: 'duration',
    key: 'duration',
    width: 120
  },
  {
    title: '操作',
    key: 'action',
    width: 200
  },
];

const fetchExecutionDetails = async () => {
  try {
    // 获取执行记录详情
    const execResponse = await ExecutionApi.getById(executionId.value);
    Object.assign(execution, execResponse.data)

    // 获取用例执行记录
    caseExecutions.value = execution.caseExecutions

  } catch (error) {
    console.error('获取执行详情失败:', error);
  }
};

const getStatusColor = (status) => {
  switch (status) {
    case 'finished': return 'success';
    case 'failed': return 'error';
    case 'skipped': return 'warning';
    case 'passed': return 'success';
    case 'running': return 'processing';
    default: return 'default';
  }
};

const getCaseHeader = (caseExec) => {
  return `${caseExec.caseName} - ${caseExec.status}`;
};

const viewStepDetail = (step) => {
  detailRef.value.showDetailModal(step.request);
};

// 检查是否有操作结果
const hasOperationResults = (record) => {
  // 检查 results 字段
  if (record.results) {
    // 如果是字符串，尝试解析
    if (typeof record.results === 'string') {
      try {
        const parsed = JSON.parse(record.results);
        return Array.isArray(parsed) && parsed.length > 0;
      } catch (e) {
        return false;
      }
    }
    // 如果是数组
    if (Array.isArray(record.results)) {
      return record.results.length > 0;
    }
    // 如果是对象
    if (typeof record.results === 'object') {
      return Object.keys(record.results).length > 0;
    }
  }
  return false;
};

// 获取结果数量
const getResultsCount = (record) => {
  if (!record.results) return 0;

  if (typeof record.results === 'string') {
    try {
      const parsed = JSON.parse(record.results);
      return Array.isArray(parsed) ? parsed.length : 1;
    } catch (e) {
      return 0;
    }
  }

  if (Array.isArray(record.results)) {
    return record.results.length;
  }

  return 1;
};

const viewOperationResults = (step) => {
  operationResultsRef.value.showModal(step.results);
};



const goBack = () => {
  router.push('/api/record');
};

onMounted(() => {
  fetchExecutionDetails();
});
</script>

<style scoped>
.execution-detail-container {
  padding: 24px;
}

.case-list-header {
  margin: 24px 0 16px;
}

pre {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  overflow: auto;
  max-height: 400px;
}
.action-button {
  display: flex;
  gap: 8px;
}
</style>