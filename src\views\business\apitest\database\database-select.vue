<template>
  <div class="database-select-container">
    <a-form layout="vertical">
      <a-form-item label="数据库连接">
        <a-select
          v-model:value="selectedDatabaseId"
          placeholder="请选择数据库连接"
          @change="onDatabaseChange"
          :loading="loading"
        >
          <a-select-option v-for="item in databases" :key="item.databaseId" :value="item.databaseId">
            {{ item.databaseName || item.databaseType }} ({{ item.databaseHost }}:{{ item.databasePort }})
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="数据库名称">
        <a-input
          v-model:value="localDatabaseConfig.defaultDb"
          placeholder="请输入数据库名称"
          @change="onConfigChange"
        />
      </a-form-item>

      <a-form-item label="连接信息" v-if="showDetails">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="主机地址">
              <a-input
                v-model:value="localDatabaseConfig.databaseHost"
                placeholder="数据库主机"
                @change="onConfigChange"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="端口">
              <a-input
                v-model:value="localDatabaseConfig.databasePort"
                placeholder="端口号"
                @change="onConfigChange"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="用户名">
              <a-input
                v-model:value="localDatabaseConfig.databaseUsername"
                placeholder="数据库用户名"
                @change="onConfigChange"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="密码">
              <a-input-password
                v-model:value="localDatabaseConfig.databasePassword"
                placeholder="数据库密码"
                @change="onConfigChange"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form-item>

      <a-form-item>
        <a-space>
          <a-button type="link" @click="showDetails = !showDetails">
            {{ showDetails ? '隐藏' : '显示' }}连接详情
            <down-outlined v-if="!showDetails" />
            <up-outlined v-if="showDetails" />
          </a-button>
          <a-button type="link" @click="testConnection" :loading="testing">
            <template #icon><api-outlined /></template>
            测试连接
          </a-button>
        </a-space>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import {
  DownOutlined,
  UpOutlined,
  ApiOutlined
} from '@ant-design/icons-vue';
import { databaseApi } from '/src/api/business/apitest/database-api.js';

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      databaseId: null,
      databaseType: '',
      databaseHost: '',
      databasePort: '',
      databaseUsername: '',
      databasePassword: '',
      defaultDb: ''
    })
  },
  showDetails: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'change']);

// 状态管理
const loading = ref(false);
const testing = ref(false);
const showDetails = ref(props.showDetails);
const selectedDatabaseId = ref(null);
const databases = ref([]);

// 本地数据库配置
const localDatabaseConfig = reactive({
  databaseType: '',
  databaseName: '',
  databaseHost: '',
  databasePort: '',
  databaseUsername: '',
  databasePassword: '',
  defaultDb: ''
});

// 初始化本地配置
const initLocalConfig = () => {
  Object.assign(localDatabaseConfig, props.modelValue);

  // 如果有配置，尝试找到对应的数据库ID
  if (localDatabaseConfig.databaseType) {
    const matchedDb = databases.value.find(db =>
      db.databaseType === localDatabaseConfig.databaseType &&
      db.databaseHost === localDatabaseConfig.databaseHost &&
      db.databasePort === localDatabaseConfig.databasePort
    );
    if (matchedDb) {
      selectedDatabaseId.value = matchedDb.databaseId;
    }
  }
};

// 监听props变化
watch(() => props.modelValue, (newVal) => {
  Object.assign(localDatabaseConfig, newVal);
  initLocalConfig();
}, { deep: true });

// 数据库选择变化
const onDatabaseChange = (databaseId) => {
  const selectedDb = databases.value.find(db => db.databaseId === databaseId);

  if (selectedDb) {
    // 自动填充选中数据库的信息
    Object.assign(localDatabaseConfig, selectedDb);

    onConfigChange();
    message.success(`已选择数据库: ${selectedDb.databaseName || selectedDb.databaseType}`);
  }
};

// 配置变化
const onConfigChange = () => {
  emit('update:modelValue', { ...localDatabaseConfig });
  emit('change', { ...localDatabaseConfig });
};

// 测试连接
const testConnection = async () => {
  if (!localDatabaseConfig.databaseHost || !localDatabaseConfig.databasePort) {
    message.warning('请先填写数据库连接信息');
    return;
  }

  try {
    testing.value = true;

    // 调用测试连接API
    const response = await databaseApi.testConnection({
      databaseType: localDatabaseConfig.databaseType,
      databaseHost: localDatabaseConfig.databaseHost,
      databasePort: localDatabaseConfig.databasePort,
      databaseUsername: localDatabaseConfig.databaseUsername,
      databasePassword: localDatabaseConfig.databasePassword,
      defaultDb: localDatabaseConfig.defaultDb
    });

    if (response.code === 1) {
      message.success('数据库连接测试成功');
    } else {
      message.error(`连接测试失败: ${response.msg}`);
    }
  } catch (error) {
    console.error('数据库连接测试失败:', error);
    message.error('连接测试失败，请检查连接信息');
  } finally {
    testing.value = false;
  }
};

// 加载数据库列表
const loadDatabases = async () => {
  try {
    loading.value = true;
    const response = await databaseApi.getList();
    if (response.code === 1) {
      databases.value = response.data || [];
      initLocalConfig();
    }
  } catch (error) {
    console.error('加载数据库列表失败:', error);
    message.error('加载数据库列表失败');
  } finally {
    loading.value = false;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadDatabases();
});

// 暴露方法给父组件
defineExpose({
  testConnection,
  loadDatabases
});
</script>

<style scoped>
.database-select-container {
  width: 100%;
}

.database-select-container :deep(.ant-form-item) {
  margin-bottom: 16px;
}

.database-select-container :deep(.ant-form-item:last-child) {
  margin-bottom: 0;
}
</style>