<template>
  <a-row :gutter="16">
    <a-col :span="12">
      <a-card title="测试通过率" :bordered="false">
        <div ref="passRateChart" style="height: 300px"></div>
      </a-card>
    </a-col>
    <a-col :span="12">
      <a-card title="接口覆盖率" :bordered="false">
        <div class="coverage-stats">
          <div class="coverage-item">
            <div class="label">总接口数</div>
            <div class="value">{{ coverageData.totalApis }}</div>
          </div>
          <div class="coverage-item">
            <div class="label">已覆盖接口</div>
            <div class="value">{{ coverageData.coveredApis }}</div>
          </div>
          <div class="coverage-item">
            <div class="label">覆盖率</div>
            <div class="value">{{ (coverageData.coverageRate * 100).toFixed(2) }}%</div>
          </div>
        </div>
        <a-progress
          :percent="coverageData.coverageRate * 100"
          :stroke-color="getCoverageColor(coverageData.coverageRate)"
          :format="percent => `${percent.toFixed(2)}%`"
        />
        <div class="coverage-by-method">
          <h4>按请求方法覆盖率</h4>
          <div v-for="method in coverageData.methodCoverage" :key="method.name">
            <span>{{ method.name }}</span>
            <a-progress
              :percent="method.rate * 100"
              :stroke-color="getMethodColor(method.name)"
              :format="percent => `${percent.toFixed(2)}%`"
              size="small"
            />
          </div>
        </div>
      </a-card>
    </a-col>
  </a-row>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  passRateData: {
    type: Object,
    required: true
  },
  coverageData: {
    type: Object,
    required: true
  }
});

const passRateChart = ref(null);
let chart = null;

const initChart = () => {
  if (!passRateChart.value) return;

  chart = echarts.init(passRateChart.value);
  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
    },
    series: [
      {
        name: '测试结果',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: props.passRateData.passed, name: '通过', itemStyle: { color: '#52c41a' } },
          { value: props.passRateData.failed, name: '失败', itemStyle: { color: '#f5222d' } },
          { value: props.passRateData.skipped, name: '跳过', itemStyle: { color: '#faad14' } }
        ]
      }
    ]
  };

  chart.setOption(option);
};

const getMethodColor = (method) => {
  const colors = {
    GET: '#1890ff',
    POST: '#52c41a',
    PUT: '#faad14',
    DELETE: '#f5222d',
    PATCH: '#722ed1'
  };
  return colors[method] || '#1890ff';
};

const getCoverageColor = (rate) => {
  if (rate >= 0.8) return '#52c41a';
  if (rate >= 0.6) return '#faad14';
  return '#f5222d';
};

watch(() => props.passRateData, () => {
  if (chart) {
    initChart();
  }
}, { deep: true });

onMounted(() => {
  initChart();
  window.addEventListener('resize', () => {
    chart?.resize();
  });
});
</script>

<style scoped>
.coverage-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 16px;
}
.coverage-item {
  text-align: center;
}
.coverage-item .label {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
}
.coverage-item .value {
  font-size: 24px;
  font-weight: bold;
}
.coverage-by-method {
  margin-top: 16px;
}
</style>