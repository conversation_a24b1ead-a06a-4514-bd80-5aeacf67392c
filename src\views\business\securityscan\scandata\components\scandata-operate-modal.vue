
<template>
  <a-drawer
    :body-style="{ paddingBottom: '80px' }"
    :maskClosable="true"
    :title="form.id ? '编辑' : '添加'"
    :open="visible"
    :width="600"
    @close="onClose"
    destroyOnClose
  >
    <a-form ref="formRef" :labelCol="{ span: 5 }" :labelWrap="true" :model="form" :rules="rules">
      <a-form-item label="产品名称" name="productName">
        <a-input v-model:value="form.productName" placeholder="请输入产品名称" />
      </a-form-item>
      <a-form-item label="产品线" name="productLines">
        <a-input v-model:value="form.productLines" placeholder="请输入产品线" />
      </a-form-item>
      <a-form-item label="服务名称" name="serviceName">
        <a-input v-model:value="form.serviceName" placeholder="请输入服务名称" />
      </a-form-item>
      <a-form-item label="Git仓库地址" name="gitPath">

        <a-input v-model:value="form.gitPath" placeholder="请输入Git仓库地址" />
      </a-form-item>
      <a-form-item label="Git类型" name="gitType">
        <a-select v-model:value="form.gitType" placeholder="请输入Git类型">
          <a-select-option value="tag" />
          <a-select-option value="branch" />
        </a-select>
      </a-form-item>
      <a-form-item label="分支名称" name="typeName">
        <a-input v-model:value="form.typeName" placeholder="请输入分支名称" />
      </a-form-item>
      <a-form-item label="指定文件夹" name="commandDirectory">
        <a-input v-model:value="form.commandDirectory" placeholder="请输入指定文件夹" />
      </a-form-item>
      <a-form-item label="镜像号" name="imgNum">
        <a-input v-model:value="form.imgNum" placeholder="请输入镜像号" />
      </a-form-item>
      <a-form-item label="是否扫描" name="isre">
        <a-switch
            v-model:checked="form.isre"
            :checkedValue="true"
            :unCheckedValue="false"
            checked-children="启用"
            un-checked-children="禁用"
          />
      </a-form-item>
    </a-form>
    <div class="footer">
      <a-button style="margin-right: 8px" @click="onClose">取消</a-button>
      <a-button style="margin-right: 8px" type="primary" @click="onSubmit(false)">提交 </a-button>
      <a-button v-if="!form.id" type="primary" @click="onSubmit(true)">提交并添加下一个 </a-button>
    </div>
  </a-drawer>
</template>
<script setup>
  import { message } from 'ant-design-vue';
  import _ from 'lodash';
  import { nextTick, reactive, ref } from 'vue';
  import { menuApi } from '/src/api/system/menu-api';
  import { MENU_DEFAULT_PARENT_ID, MENU_PERMS_TYPE_ENUM, MENU_TYPE_ENUM } from '/src/constants/system/menu-const';
  import { smartSentry } from '/src/lib/smart-sentry';
  import { SmartLoading } from '/src/components/framework/smart-loading';

  // ----------------------- 以下是字段定义 emits props ------------------------
  // emit
  const emit = defineEmits(['reloadList']);

  // ----------------------- 展开、隐藏编辑窗口 ------------------------

  // 是否展示抽屉
  const visible = ref(false);

  const contextMenuTreeSelect = ref();
  const parentMenuTreeSelect = ref();

  //展开编辑窗口
  async function showDrawer(rowData) {
    Object.assign(form, formDefault);
    if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
    }
    visible.value = true;
  }

  // 隐藏窗口
  function onClose() {
    Object.assign(form, formDefault);
    formRef.value.resetFields();
    visible.value = false;
  }

  // ----------------------- form表单相关操作 ------------------------

  const formRef = ref();
  const formDefault = {
    id: undefined,
    productName: undefined,
    productLines: undefined,
    serviceName: undefined,
    gitPath: undefined,
    gitType: undefined,
    typeName: undefined,
    commandDirectory: undefined,
    imgNum: undefined,
    isre: true
  };
  let form = reactive({ ...formDefault });

  function continueResetForm() {
    Object.assign(form, formDefault);
    formRef.value.resetFields();
  }

  const rules = {
    productName: [{ required: true, message: '菜单类型不能为空' }],
    productLines: [{ required: true, message: '权限类型不能为空' }],
    serviceName: [
      { required: true, message: '菜单名称不能为空' }
    ],
    gitPath: [
      { required: true, message: '外链地址不能为空' },
    ],
    gitType: [
      { required: true, message: '路由地址不能为空' },
      { max: 100, message: '路由地址不能大于100个字符', trigger: 'blur' },
    ],
    typeName: [
      { required: true, message: '路由地址不能为空' },
      { max: 100, message: '路由地址不能大于100个字符', trigger: 'blur' },
    ],
    imgNum: [
      { required: true, message: '路由地址不能为空' },
      { max: 100, message: '路由地址不能大于100个字符', trigger: 'blur' },
    ],
    isre: [
      { required: true, message: '路由地址不能为空' },
      { max: 100, message: '路由地址不能大于100个字符', trigger: 'blur' },
    ],
  };

  function validateForm(formRef) {
    return new Promise((resolve) => {
      formRef
        .validate()
        .then(() => {
          resolve(true);
        })
        .catch(() => {
          resolve(false);
        });
    });
  }

  const onSubmit = async (continueFlag) => {
    let validateFormRes = await validateForm(formRef.value);
    if (!validateFormRes) {
      message.error('参数验证错误，请仔细填写表单数据!');
      return;
    }
    SmartLoading.show();
    try {
      if (form.id) {
        await menuApi.updateMenu(form);
      } else {
        await menuApi.addMenu(form);
      }
      message.success(`${form.id ? '修改' : '添加'}成功`);
      if (continueFlag) {
        continueResetForm();
      } else {
        onClose();
      }
      emit('reloadList');
    } catch (error) {
      smartSentry.captureError(error);
    } finally {
      SmartLoading.hide();
    }
  };

  function selectIcon(icon) {
    form.icon = icon;
  }

  // ----------------------- 以下是暴露的方法内容 ------------------------
  defineExpose({
    showDrawer,
  });
</script>
<style lang="less" scoped>
  .footer {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 100%;
    border-top: 1px solid #e9e9e9;
    padding: 10px 16px;
    background: #fff;
    text-align: left;
    z-index: 1;
  }
</style>
