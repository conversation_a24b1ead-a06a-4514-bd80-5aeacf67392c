<template>
  <a-card title="接口健康度分析" :bordered="false">
    <div ref="healthRadarChart" style="height: 400px"></div>
  </a-card>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  healthData: {
    type: Array,
    required: true
  }
});

const healthRadarChart = ref(null);
let chart = null;

const initChart = () => {
  if (!healthRadarChart.value) return;

  chart = echarts.init(healthRadarChart.value);

  // 准备雷达图数据
  const indicators = [
    {name: '接口覆盖率', max: 100},
    {name: '测试通过率', max: 100},
    {name: '响应时间', max: 100},
    {name: '稳定性', max: 100},
    {name: '数据验证率', max: 100}
  ];

  // 定义渐变色数组，用于不同数据系列
  const gradientColors = [
    [
      {offset: 0, color: 'rgba(128, 255, 165, 0.7)'},
      {offset: 1, color: 'rgba(1, 191, 236, 0.5)'}
    ],
    [
      {offset: 0, color: 'rgba(255, 191, 0, 0.7)'},
      {offset: 1, color: 'rgba(224, 62, 76, 0.5)'}
    ],
    [
      {offset: 0, color: 'rgba(86, 163, 241, 0.7)'},
      {offset: 1, color: 'rgba(86, 103, 241, 0.5)'}
    ],
    [
      {offset: 0, color: 'rgba(255, 145, 124, 0.7)'},
      {offset: 1, color: 'rgba(255, 102, 102, 0.5)'}
    ]
  ];

  const seriesData = props.healthData.map((item, index) => ({
    value: [
      item.coverageRate * 100,
      item.passRate * 100,
      100 - item.responseTimeScore, // 响应时间转换为分数
      item.stabilityScore,
      item.validationRate * 100
    ],
    name: item.groupName,
    symbolSize: 8,
    lineStyle: {
      width: 2.5
    },
    areaStyle: {
      opacity: 0.8,
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1,
          gradientColors[index % gradientColors.length]
      )
    }
  }));

  // 确保至少有一些数据，如果没有实际数据，添加示例数据
  if (seriesData.length === 0) {
    seriesData.push(
        {
          value: [90, 82, 95, 80, 70],
          name: '网络层',
          symbolSize: 8,
          lineStyle: {
            width: 2.5
          },
          areaStyle: {
            opacity: 0.8,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, gradientColors[0])
          }
        },
        {
          value: [70, 93, 80, 90, 90],
          name: '用户',
          symbolSize: 8,
          lineStyle: {
            width: 2.5
          },
          areaStyle: {
            opacity: 0.8,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, gradientColors[1])
          }
        },
        {
          value: [80, 75, 85, 65, 88],
          name: '用户登录',
          symbolSize: 8,
          lineStyle: {
            width: 2.5
          },
          areaStyle: {
            opacity: 0.8,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, gradientColors[2])
          }
        }
    );
  }

  const option = {
    color: ['#67F9D8', '#FFE434', '#56A3F1', '#FF917C', '#9FE6B8', '#FFDB5C'],
    title: {
      text: '接口健康度分析',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(50,50,50,0.7)',
      borderColor: 'rgba(50,50,50,0.7)',
      textStyle: {
        color: '#fff'
      }
    },
    legend: {
      data: seriesData.map(item => item.name),
      orient: 'vertical',
      right: '5%',
      top: 'middle',
      textStyle: {
        color: '#666'
      },
      icon: 'roundRect',
      itemWidth: 12,
      itemHeight: 12,
      itemGap: 12
    },
    radar: {
      indicator: indicators,
      shape: 'circle',
      radius: '70%',
      center: ['45%', '50%'],
      splitNumber: 5,
      axisName: {
        color: 'rgb(67, 67, 67)',
        fontSize: 13,
        padding: [3, 5]
      },
      splitArea: {
        areaStyle: {
          color: ['rgba(250,250,250,0.3)', 'rgba(200,200,200,0.3)'],
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.05)'
        }
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(211, 253, 250, 0.8)',
          width: 1.5
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(211, 253, 250, 0.8)',
          width: 1.5
        }
      }
    },
    series: [
      {
        name: '接口健康度',
        type: 'radar',
        emphasis: {
          lineStyle: {
            width: 4
          }
        },
        data: seriesData
      }
    ]
  };

  chart.setOption(option);
};

watch(() => props.healthData, () => {
  if (chart) {
    initChart();
  }
}, {deep: true});

onMounted(() => {
  initChart();
  window.addEventListener('resize', () => {
    chart?.resize();
  });
});
</script>