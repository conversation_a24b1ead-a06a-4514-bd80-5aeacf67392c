<template>
  <div class="case-management-container">
    <!-- 左侧分组树 -->
    <div class="group-tree-container">
      <!-- 树形控件 -->
      <a-spin :spinning="treeLoading">
        <api-menu ref='menuRef' @on-click="onClick" @add-api="addApi"/>
      </a-spin>
    </div>

    <!-- 右侧内容区 -->
    <div class="content-container">
      <!-- 无选中用例时显示 -->
      <div v-if="!currentKey" class="empty-state">
        <a-empty description="请从左侧选择一个接口或分组" />
      </div>
      <!-- 选中用例时显示用例详情 -->
      <div v-else-if="currentKey?.startsWith('api-')" class="case-detail">
        <api-edit :data="apiData"/>
      </div>
      <!-- 选中分组时显示分组下的用例列表 -->
<!--      <div v-else-if="currentKey?.startsWith('group-')" class="group-cases">-->
<!--        <api-edit :api-id="keyId"/>-->
<!--      </div>-->

    </div>

    <!-- 创建分组对话框 -->
    <a-modal
      v-model:visible="groupModalVisible"
      :title="groupModalMode === 'create' ? '创建分组' : '编辑分组'"
      @ok="saveGroup"
    >
      <a-form :model="groupForm" layout="vertical">
        <a-form-item label="分组名称" name="name" :rules="[{ required: true, message: '请输入分组名称' }]">
          <a-input v-model:value="groupForm.name" placeholder="请输入分组名称" />
        </a-form-item>
        <a-form-item v-if="groupModalMode === 'create'" label="父分组" name="parentId">
          <a-tree-select
            v-model:value="groupForm.parentId"
            :tree-data="groupTreeData"
            placeholder="请选择父分组"
            tree-default-expand-all
            :replaceFields="{ title: 'name', key: 'id', value: 'id' }"
            allow-clear
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import ApiMenu from "/@/views/business/apitest/api/components/api-menu.vue";
import ApiEdit from "/src/views/business/apitest/api/components/api-edit.vue";
import {Api} from "/@/api/business/apitest/api.js";
import {message} from "ant-design-vue";

// 路由
const router = useRouter();
const apiDefaultData = reactive({
  apiId:'',
  name: '',
  groupId: null,
  method: 'get',
  url: '',
  desc: '',
  tags: [],
  status: 1,
  headerList: [],
  paramList: [],
  dataList: [],
  fileList: [],
  json: {},
  data: {},
  content: '',
  files: {},
  prefixOperations:[],
  suffixOperations:[],
  auth: null,
  contentType: null,
  settings: {
    followRedirects: true,
    timeout: 30000,
    verify: true
  }
});

const apiData = ref(JSON.parse(JSON.stringify(apiDefaultData)))
// 树形数据相关
const menuRef = ref(null);
const treeData = ref([]);
const treeLoading = ref(false);
const currentKey = ref(null);
const keyId = computed(()=>{
  return getKeyId(currentKey.value)
})
function getKeyId(key){
  if (key){
    return parseInt(key.split('-')[1]);
  }else{
    return null;
  }
}
// 加载API数据
const loadApiData = async (apiId) => {
  try {
    // 调用API获取接口数据
    const response = await Api.get({apiId: apiId});
    const apiDetail = response.data;
    if (apiDetail.length>0){
      apiData.value = apiDetail[0];
    }
  } catch (error) {
    console.error('加载接口数据失败:', error);
    message.error('加载接口数据失败: ' + (error.message || '未知错误'));
  }
};

const addApi = (groupId) => {
  apiData.value = JSON.parse(JSON.stringify(apiDefaultData));
  apiData.value.groupId = groupId;
};
// 分组相关
const groupModalVisible = ref(false);
const groupModalMode = ref('create'); // 'create' 或 'edit'
const groupForm = reactive({
  id: null,
  name: '',
  parentId: null
});
const groupTreeData = ref([]);


// 获取HTTP方法颜色
const getMethodColor = (method) => {
  const colors = {
    get: '#108ee9',
    post: '#87d068',
    put: '#2db7f5',
    delete: '#f50',
    patch: '#722ed1',
    options: '#faad14',
    head: '#fadb14'
  };
  return colors[method?.toLowerCase()] || '#888';
};

const onClick = async (key) =>{
  currentKey.value = key;
  if (key.startsWith('api-') && keyId.value){
    await loadApiData(keyId.value);
  }

}
</script>

<style scoped>
.case-management-container {
  display: flex;
  height: 100%;
  overflow: hidden;
}

.group-tree-container {
  width: 280px;
  border-right: 1px solid #e8e8e8;
  padding: 16px;
  overflow-y: auto;
  background-color: #f5f5f5;
}

.group-tree-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.group-tree-header h3 {
  margin: 0;
}

.content-container {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}


.tree-node:hover .tree-node-actions {
  display: block;
}


.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}


.group-header h2 {
  margin: 0;
}
</style>