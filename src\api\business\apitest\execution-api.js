import {getRequest, postRequest} from "/@/lib/axios.js";

export const ExecutionApi = {
    pageQuery(params){
        return getRequest('/api_test/execution/page',params);
    },
    getById(id){
        return getRequest(`/api_test/execution/get/${id}`);
    },
    delete(executionId){
        return postRequest('/api_test/execution/delete', {executionId});
    },
    batchDelete(executionIds){
        return postRequest('/api_test/execution/batch-delete', {executionIds});
    }
}