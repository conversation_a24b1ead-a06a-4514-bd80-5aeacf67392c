<template>
  <div class="environment-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <a-page-header title="环境管理" sub-title="配置测试环境和环境变量">
        <template #extra>
          <a-space>
            <a-select
              v-model:value="selectedEnvId"
              placeholder="请选择环境"
              style="width: 200px"
              @change="onEnvironmentChange"
              allow-clear
            >
              <a-select-option
                v-for="env in environmentList"
                :key="env.envId"
                :value="env.envId"
              >
                <component :is="getEnvIcon(env.name)" style="margin-right: 8px" />
                {{ env.name }}
              </a-select-option>
            </a-select>
            <a-button type="primary" @click="showEnvironmentModal(null)">
              <template #icon><plus-outlined /></template>
              新增环境
            </a-button>
            <a-button
              v-if="selectedEnvironment"
              @click="showEnvironmentModal(selectedEnvironment)"
            >
              <template #icon><edit-outlined /></template>
              编辑环境
            </a-button>
            <a-popconfirm
              v-if="selectedEnvironment"
              title="确定要删除这个环境吗？"
              @confirm="deleteEnvironment(selectedEnvironment.envId)"
              ok-text="确定"
              cancel-text="取消"
            >
              <a-button danger>
                <template #icon><delete-outlined /></template>
                删除环境
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </a-page-header>
    </div>

    <!-- 上半部分：服务地址配置 -->
    <a-card :bordered="false" class="section-card" style="margin-bottom: 20px;">
      <template #title>
        <span style="font-size: 16px; font-weight: bold; color: #1890ff;">
          <global-outlined /> 服务地址配置
          <span v-if="selectedEnvironment" style="color: #666; font-weight: normal; margin-left: 8px;">
            - {{ selectedEnvironment.name }}
          </span>
        </span>
      </template>

      <div v-if="!selectedEnvironment" class="empty-state">
        <a-empty description="请先选择一个环境" />
      </div>

      <div v-else>
        <!-- 默认服务地址 -->
        <a-descriptions :column="1" bordered size="middle" style="margin-bottom: 16px;">
          <a-descriptions-item label="默认服务地址">
            <div class="default-url-container">
              <a-tag v-if="defaultUrl" color="blue" style="font-size: 14px; margin-right: 8px;">
                <global-outlined style="margin-right: 4px;" />
                {{ defaultUrl }}
              </a-tag>
              <span v-else class="text-gray">未配置默认服务地址</span>
              <a-button
                v-if="defaultUrl"
                type="link"
                size="small"
                @click="copyToClipboard(defaultUrl)"
              >
                <copy-outlined />
              </a-button>
            </div>
          </a-descriptions-item>
        </a-descriptions>

        <!-- 其他服务地址 -->
        <div v-if="otherServices.length > 0">
          <a-descriptions :column="1" bordered size="middle">
            <a-descriptions-item label="其他服务地址">
              <div class="services-container">
                <div
                  v-for="service in otherServices"
                  :key="service.name"
                  class="service-item"
                >
                  <a-tag color="green" class="service-tag">
                    <strong>{{ service.name }}:</strong> {{ service.url }}
                  </a-tag>
                  <a-button
                    type="link"
                    size="small"
                    @click="copyToClipboard(service.url)"
                  >
                    <copy-outlined />
                  </a-button>
                </div>
              </div>
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <div v-else-if="!defaultUrl" style="text-align: center; color: #999; padding: 20px;">
          <a-empty description="暂无服务地址配置" :image="false" />
        </div>
      </div>
    </a-card>

    <!-- 下半部分：环境变量配置 -->
    <a-card :bordered="false" class="section-card">
      <template #title>
        <span style="font-size: 16px; font-weight: bold; color: #52c41a;">
          <code-outlined /> 环境变量配置
          <span v-if="selectedEnvironment" style="color: #666; font-weight: normal; margin-left: 8px;">
            - {{ selectedEnvironment.name }}
          </span>
        </span>
      </template>

      <div v-if="!selectedEnvironment" class="empty-state">
        <a-empty description="请先选择一个环境" />
      </div>

      <div v-else-if="!selectedEnvironmentVariables || selectedEnvironmentVariables.length === 0" class="empty-state">
        <a-empty description="当前环境暂无环境变量">
          <template #description>
            <p>当前环境暂无环境变量</p>
            <a-button type="primary" @click="showEnvironmentModal(selectedEnvironment)">
              添加环境变量
            </a-button>
          </template>
        </a-empty>
      </div>

      <a-table
        v-else
        :dataSource="selectedEnvironmentVariables"
        :loading="loading"
        rowKey="key"
        :pagination="{ pageSize: 20, showSizeChanger: true, showQuickJumper: true }"
        size="middle"
      >
        <a-table-column key="variableName" title="变量名" dataIndex="variableName" width="200px">
          <template #default="{ record }">
            <a-tag color="purple">{{ record.variableName }}</a-tag>
          </template>
        </a-table-column>
        <a-table-column key="variableValue" title="变量值" dataIndex="variableValue" width="300px">
          <template #default="{ record }">
            <div class="variable-value-container">
              <a-tooltip :title="record.variableValue">
                <span class="variable-value">{{ record.variableValue }}</span>
              </a-tooltip>
              <a-button
                type="link"
                size="small"
                @click="copyToClipboard(record.variableValue)"
              >
                <copy-outlined />
              </a-button>
            </div>
          </template>
        </a-table-column>
        <a-table-column key="category" title="分类" dataIndex="category" width="120px">
          <template #default="{ record }">
            <a-tag v-if="record.category" :color="getCategoryColor(record.category)">
              {{ getCategoryLabel(record.category) }}
            </a-tag>
            <span v-else class="text-gray">-</span>
          </template>
        </a-table-column>
        <a-table-column key="status" title="状态" dataIndex="status" width="100px">
          <template #default="{ record }">
            <a-tag :color="record.status ? 'success' : 'default'">
              {{ record.status ? '启用' : '禁用' }}
            </a-tag>
          </template>
        </a-table-column>
        <a-table-column key="description" title="描述" dataIndex="description">
          <template #default="{ record }">
            <span v-if="record.description">{{ record.description }}</span>
            <span v-else class="text-gray">-</span>
          </template>
        </a-table-column>
      </a-table>
    </a-card>

    <!-- 环境编辑对话框 -->
    <a-modal
      v-model:visible="environmentModalVisible"
      :title="currentEnvironment.envId ? '编辑环境' : '新增环境'"
      @ok="handleEnvironmentOk"
      width="1000px"
      :destroyOnClose="true"
    >
      <a-form :model="currentEnvironment" layout="vertical">
        <!-- 基本信息 -->
        <a-form-item label="环境名称" required>
          <a-input
            v-model:value="currentEnvironment.name"
            placeholder="例如：开发环境、测试环境、生产环境"
          />
        </a-form-item>

        <!-- 服务地址配置 -->
        <a-divider orientation="left">
          <span style="font-weight: bold; color: #1890ff;">
            <global-outlined /> 服务地址配置
          </span>
        </a-divider>

        <!-- 默认URL -->
        <a-form-item label="默认服务地址" required>
          <a-input
            v-model:value="currentEnvironment.defaultUrl"
            placeholder="例如：https://api.example.com"
            addon-before="defaultUrl"
          />
        </a-form-item>

        <!-- 其他服务地址 -->
        <a-form-item label="其他服务地址">
          <div v-for="(service, index) in currentEnvironment.services" :key="`service-${index}`" class="param-item">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-input
                  v-model:value="service.name"
                  placeholder="服务名称"
                />
              </a-col>
              <a-col :span="12">
                <a-input
                  v-model:value="service.url"
                  placeholder="服务地址"
                />
              </a-col>
              <a-col :span="4">
                <a-button
                  type="danger"
                  @click="removeService(index)"
                  style="width: 100%"
                >
                  <template #icon><delete-outlined /></template>
                </a-button>
              </a-col>
            </a-row>
          </div>
          <a-button
            type="dashed"
            style="width: 100%; margin-top: 8px;"
            @click="addService"
          >
            <template #icon><plus-outlined /></template>
            添加服务地址
          </a-button>
        </a-form-item>

        <!-- 环境变量配置 -->
        <a-divider orientation="left">
          <span style="font-weight: bold; color: #52c41a;">
            <code-outlined /> 环境变量配置
          </span>
        </a-divider>

        <div v-for="(variable, index) in currentEnvironment.variables" :key="`variable-${index}`" class="variable-item">
          <a-card size="small" class="variable-card">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-form-item label="变量名" :label-col="{ span: 24 }" :wrapper-col="{ span: 24 }">
                  <a-input
                    v-model:value="variable.variableName"
                    placeholder="变量名称"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="变量值" :label-col="{ span: 24 }" :wrapper-col="{ span: 24 }">
                  <a-input
                    v-model:value="variable.variableValue"
                    placeholder="变量值"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="分类" :label-col="{ span: 24 }" :wrapper-col="{ span: 24 }">
                  <a-select
                    v-model:value="variable.category"
                    placeholder="选择分类"
                    allow-clear
                  >
                    <a-select-option value="auth">认证</a-select-option>
                    <a-select-option value="config">配置</a-select-option>
                    <a-select-option value="data">数据</a-select-option>
                    <a-select-option value="other">其他</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="2">
                <a-form-item label="状态" :label-col="{ span: 24 }" :wrapper-col="{ span: 24 }">
                  <a-switch
                    v-model:checked="variable.status"
                    checked-children="启用"
                    un-checked-children="禁用"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="描述" :label-col="{ span: 24 }" :wrapper-col="{ span: 24 }">
                  <a-input
                    v-model:value="variable.description"
                    placeholder="变量描述"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="2">
                <a-form-item label="操作" :label-col="{ span: 24 }" :wrapper-col="{ span: 24 }">
                  <a-button
                    type="danger"
                    @click="removeVariable(index)"
                    style="width: 100%"
                  >
                    <template #icon><delete-outlined /></template>
                  </a-button>
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>
        </div>

        <a-button
          type="dashed"
          style="width: 100%; margin-top: 8px;"
          @click="addVariable"
        >
          <template #icon><plus-outlined /></template>
          添加环境变量
        </a-button>

        <div class="usage-help" style="margin-top: 16px">
          <a-alert
            type="info"
            show-icon
            message="使用说明"
            description="在API测试中，可以通过 ${变量名} 的形式引用环境变量。服务地址会根据配置自动选择对应的URL。"
          />
        </div>
      </a-form>
    </a-modal>

  </div>
</template>

<script>
import { defineComponent, ref, computed, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import {
  PlusOutlined, EditOutlined, DeleteOutlined, CodeOutlined, GlobalOutlined, CopyOutlined
} from '@ant-design/icons-vue';
import { EnvironmentApi, getEnvIcon } from "/@/api/business/apitest/environment-api.js";

export default defineComponent({
  components: {
    PlusOutlined,
    EditOutlined,
    DeleteOutlined,
    CodeOutlined,
    GlobalOutlined,
    CopyOutlined
  },
  setup() {
    // 环境列表
    const environmentList = ref([]);
    const loading = ref(false);

    // 选中的环境ID
    const selectedEnvId = ref(null);

    // 选中的环境对象（计算属性）
    const selectedEnvironment = computed(() => {
      if (!selectedEnvId.value) return null;
      return environmentList.value.find(env => env.envId === selectedEnvId.value);
    });

    // 默认服务地址（计算属性）
    const defaultUrl = computed(() => {
      if (!selectedEnvironment.value || !selectedEnvironment.value.baseUrl) return '';
      return selectedEnvironment.value.baseUrl.defaultUrl || '';
    });

    // 其他服务地址（计算属性）
    const otherServices = computed(() => {
      if (!selectedEnvironment.value || !selectedEnvironment.value.baseUrl) return [];

      const services = [];
      Object.keys(selectedEnvironment.value.baseUrl).forEach(key => {
        if (key !== 'defaultUrl') {
          services.push({
            name: key,
            url: selectedEnvironment.value.baseUrl[key]
          });
        }
      });
      return services;
    });

    // 选中环境的变量数据（计算属性）
    const selectedEnvironmentVariables = computed(() => {
      if (!selectedEnvironment.value || !selectedEnvironment.value.variables) return [];

      return selectedEnvironment.value.variables.map((variable, index) => ({
        key: `${selectedEnvironment.value.envId}-${index}`,
        variableName: variable.variableName || variable.variable_name || '',
        variableValue: variable.variableValue || variable.variable_value || '',
        category: variable.category || null,
        status: variable.status !== undefined ? variable.status : true,
        description: variable.description || ''
      }));
    });

    // 变量表格数据（计算属性）- 保留用于兼容性
    const variableTableData = computed(() => {
      const data = [];
      environmentList.value.forEach(env => {
        if (env.variables && env.variables.length > 0) {
          env.variables.forEach((variable, index) => {
            data.push({
              key: `${env.envId}-${index}`,
              envId: env.envId,
              envName: env.name,
              variableName: variable.variable_name || '',
              variableValue: variable.variable_value || '',
              category: variable.category || null,
              status: variable.status !== undefined ? variable.status : true,
              description: variable.description || ''
            });
          });
        }
      });
      return data;
    });

    // 当前编辑的环境
    const currentEnvironment = ref({
      envId: null,
      name: '',
      defaultUrl: '',
      services: [],
      variables: []
    });

    // 对话框显示状态
    const environmentModalVisible = ref(false);

    // 初始化数据
    onMounted(() => {
      fetchEnvironments();
    });

    // 获取环境列表
    const fetchEnvironments = async () => {
      loading.value = true;
      try {
        const response = await EnvironmentApi.get();
        environmentList.value = response.data.list || response.data || [];

        // 如果有环境数据且没有选中环境，默认选中第一个
        if (environmentList.value.length > 0 && !selectedEnvId.value) {
          selectedEnvId.value = environmentList.value[0].envId;
        }

      } catch (error) {
        console.error('获取环境列表失败:', error);
        message.error('获取环境列表失败');
      } finally {
        loading.value = false;
      }
    };

    // 环境切换处理
    const onEnvironmentChange = (envId) => {
      selectedEnvId.value = envId;
    };

    // 显示环境编辑对话框
    const showEnvironmentModal = (env) => {
      if (env) {
        // 编辑模式，深拷贝避免直接修改列表数据
        const envCopy = JSON.parse(JSON.stringify(env));

        // 处理 baseUrl 数据结构
        const services = [];
        let defaultUrl = '';

        if (envCopy.baseUrl && typeof envCopy.baseUrl === 'object') {
          defaultUrl = envCopy.baseUrl.defaultUrl || '';

          // 将其他服务地址转换为数组格式
          Object.keys(envCopy.baseUrl).forEach(key => {
            if (key !== 'defaultUrl') {
              services.push({
                name: key,
                url: envCopy.baseUrl[key]
              });
            }
          });
        }

        // 处理 variables 数据结构，确保每个变量都有完整的字段（使用驼峰格式）
        const variables = (envCopy.variables || []).map(variable => ({
          variableName: variable.variableName || variable.variable_name || '',
          variableValue: variable.variableValue || variable.variable_value || '',
          category: variable.category || null,
          status: variable.status !== undefined ? variable.status : true,
          description: variable.description || ''
        }));

        currentEnvironment.value = {
          envId: envCopy.envId,
          name: envCopy.name || '',
          defaultUrl: defaultUrl,
          services: services,
          variables: variables
        };
      } else {
        // 新增模式
        currentEnvironment.value = {
          envId: null,
          name: '',
          defaultUrl: '',
          services: [],
          variables: []
        };
      }

      environmentModalVisible.value = true;
    };

    // 保存环境
    const handleEnvironmentOk = async () => {
      if (!currentEnvironment.value.name || !currentEnvironment.value.defaultUrl) {
        message.warning('请填写环境名称和默认服务地址');
        return;
      }

      try {
        // 构建 baseUrl 对象
        const baseUrl = {
          defaultUrl: currentEnvironment.value.defaultUrl
        };

        // 添加其他服务地址
        currentEnvironment.value.services.forEach(service => {
          if (service.name && service.url) {
            baseUrl[service.name] = service.url;
          }
        });

        // 处理变量数据，确保字段名符合后端要求（驼峰格式）
        const variables = currentEnvironment.value.variables.map(variable => ({
          variableName: variable.variableName || variable.variable_name || '',
          variableValue: variable.variableValue || variable.variable_value || '',
          category: variable.category || null,
          status: variable.status !== undefined ? variable.status : true,
          description: variable.description || ''
        }));

        // 构建要保存的数据
        const saveData = {
          envId: currentEnvironment.value.envId,
          name: currentEnvironment.value.name,
          baseUrl: baseUrl,
          variables: variables
        };

        if (currentEnvironment.value.envId) {
          // 编辑模式
          await EnvironmentApi.update(saveData);
          message.success('环境更新成功');
        } else {
          // 新增模式
          await EnvironmentApi.add(saveData);
          message.success('环境创建成功');
        }

        await fetchEnvironments();

      } catch (error) {
        console.error('保存环境失败:', error);
        message.error('保存环境失败');
      } finally {
        environmentModalVisible.value = false;
      }
    };

    // 删除环境
    const deleteEnvironment = async (envId) => {
      try {
        await EnvironmentApi.delete(envId);
        message.success('环境删除成功');

        // 如果删除的是当前选中的环境，清空选择
        if (selectedEnvId.value === envId) {
          selectedEnvId.value = null;
        }

        await fetchEnvironments();
      } catch (error) {
        console.error('删除环境失败:', error);
        message.error('删除环境失败');
      }
    };

    // 添加服务地址
    const addService = () => {
      if (!currentEnvironment.value.services) {
        currentEnvironment.value.services = [];
      }
      currentEnvironment.value.services.push({ name: '', url: '' });
    };

    // 移除服务地址
    const removeService = (index) => {
      currentEnvironment.value.services.splice(index, 1);
    };

    // 添加环境变量
    const addVariable = () => {
      if (!currentEnvironment.value.variables) {
        currentEnvironment.value.variables = [];
      }
      currentEnvironment.value.variables.push({
        variableName: '',
        variableValue: '',
        category: null,
        status: true,
        description: ''
      });
    };

    // 移除环境变量
    const removeVariable = (index) => {
      currentEnvironment.value.variables.splice(index, 1);
    };


    // 格式化日期时间
    const formatDateTime = (dateStr) => {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      return date.toLocaleString();
    };

    // 获取分类颜色
    const getCategoryColor = (category) => {
      const colorMap = {
        'auth': 'red',
        'config': 'blue',
        'data': 'green',
        'other': 'orange'
      };
      return colorMap[category] || 'default';
    };

    // 获取分类标签
    const getCategoryLabel = (category) => {
      const labelMap = {
        'auth': '认证',
        'config': '配置',
        'data': '数据',
        'other': '其他'
      };
      return labelMap[category] || category;
    };

    // 复制到剪贴板
    const copyToClipboard = async (text) => {
      try {
        await navigator.clipboard.writeText(text);
        message.success('已复制到剪贴板');
      } catch (error) {
        console.error('复制失败:', error);
        message.error('复制失败');
      }
    };

    return {
      environmentList,
      loading,
      selectedEnvId,
      selectedEnvironment,
      defaultUrl,
      otherServices,
      selectedEnvironmentVariables,
      variableTableData,
      currentEnvironment,
      environmentModalVisible,
      onEnvironmentChange,
      showEnvironmentModal,
      handleEnvironmentOk,
      deleteEnvironment,
      addService,
      removeService,
      addVariable,
      removeVariable,
      formatDateTime,
      getCategoryColor,
      getCategoryLabel,
      copyToClipboard,
      getEnvIcon
    };
  }
});
</script>

<style scoped>
.environment-container {
  padding: 20px;
  background-color: #f0f2f5;
  min-height: calc(100vh - 64px);
}

.page-header {
  margin-bottom: 20px;
  background-color: white;
  padding: 16px;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.env-selector-card {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.section-card {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.param-item {
  margin-bottom: 8px;
}

.variable-item {
  margin-bottom: 16px;
}

.variable-card {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
}

.service-tag {
  margin-bottom: 4px;
  display: inline-block;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-gray {
  color: #999;
  font-style: italic;
}

.variable-value {
  max-width: 200px;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: top;
}

.usage-help {
  margin-top: 16px;
}

.empty-state {
  padding: 40px 0;
  text-align: center;
}

.services-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.service-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.default-url-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.variable-value-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 表格中的标签样式 */
:deep(.ant-table-tbody > tr > td) {
  vertical-align: top;
}

/* 变量卡片内的表单项间距调整 */
.variable-card :deep(.ant-form-item) {
  margin-bottom: 8px;
}

.variable-card :deep(.ant-form-item-label) {
  padding-bottom: 2px;
}
</style>