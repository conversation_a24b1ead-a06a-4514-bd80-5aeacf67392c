<!-- components/ResponseHeadersViewer.vue -->
<template>
  <div class="headers-viewer">
    <a-empty v-if="!headers || Object.keys(headers).length === 0" description="没有响应头数据" />
    <template v-else>
      <div class="viewer-toolbar">
        <a-input
          placeholder="过滤响应头..."
          v-model:value="filterText"
          allow-clear
          class="filter-input"
        >
          <template #prefix>
            <search-outlined />
          </template>
        </a-input>

        <a-space>
          <a-tooltip title="复制为JSON">
            <a-button size="small" @click="copyAsJson">
              <json-outlined />
            </a-button>
          </a-tooltip>
          <a-tooltip title="复制为文本">
            <a-button size="small" @click="copyAsText">
              <copy-outlined />
            </a-button>
          </a-tooltip>
        </a-space>
      </div>
      <a-table
      :dataSource="filteredHeaders"
      :columns="columns"
      :pagination="false"
      size="small"
      bordered
      :scroll="{ y: 300 }"
      rowKey="name"
    >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'value'">
            <a-typography-paragraph
              :copyable="{ text: record.value }"
              :ellipsis="{ rows: 3, expandable: true, symbol: '展开' }"
            >
              {{ record.value }}
            </a-typography-paragraph>
          </template>
        </template>
      </a-table>
    </template>
  </div>
</template>

<script setup>
import { ref, computed, defineProps, watch } from 'vue';
import { message } from 'ant-design-vue';
import {
  SearchOutlined,
  CopyOutlined,
} from '@ant-design/icons-vue';

const props = defineProps({
  headers: {
    type: Object,
    default: null
  }
});

// 本地状态
const filterText = ref('');

// 表格列定义
const columns = [
  { title: '名称', dataIndex: 'name', width: '30%' },
  { title: '值', dataIndex: 'value', width: '70%' }
];

// 处理头信息为表格数据源
const headerRows = computed(() => {
  if (!props.headers) return [];

  return Object.entries(props.headers).map(([name, value]) => ({
    name,
    value: value || ''
  }));
});

// 过滤后的头信息
const filteredHeaders = computed(() => {
  if (!filterText.value) return headerRows.value;

  const search = filterText.value.toLowerCase();
  return headerRows.value.filter(header =>
    header.name.toLowerCase().includes(search) ||
    String(header.value).toLowerCase().includes(search)
  );
});

// 复制为JSON
const copyAsJson = () => {
  if (!props.headers) return;

  try {
    const json = JSON.stringify(props.headers, null, 2);
    navigator.clipboard.writeText(json)
      .then(() => {
        message.success('响应头已复制为JSON格式');
      })
      .catch(() => {
        message.error('复制失败，请手动复制');
      });
  } catch (error) {
    message.error('复制失败：' + error.message);
  }
};

// 复制为文本
const copyAsText = () => {
  if (!props.headers) return;

  try {
    const text = Object.entries(props.headers)
      .map(([name, value]) => `${name}: ${value}`)
      .join('\n');

    navigator.clipboard.writeText(text)
      .then(() => {
        message.success('响应头已复制为文本格式');
      })
      .catch(() => {
        message.error('复制失败，请手动复制');
      });
  } catch (error) {
    message.error('复制失败：' + error.message);
  }
};
</script>

<style scoped>
.headers-viewer {
  display: flex;
  flex-direction: column;
}

.viewer-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.filter-input {
  width: 250px;
}
</style>