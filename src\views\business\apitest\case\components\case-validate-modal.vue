<template>
  <a-modal
    :open="open"
    :title="isEdit ? '编辑断言规则' : '添加断言规则'"
    @cancel="onCancel"
    @ok="save"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
    >
      <a-form-item label="断言类型" name="type">
        <a-select v-model:value="formData.type" :options="validateTypeOptions" />
      </a-form-item>
      <a-form-item label="期望值" name="expected">
          <a-input v-model:value="formData.expected"/>
      </a-form-item>
      <a-form-item label="表达式:JSONPath|正则" name="expression">
        <a-input v-model:value="formData.expression" />
      </a-form-item>
      <a-form-item label="比较方式" name="comparator">
        <a-select v-model:value="formData.comparator">
          <a-select-option v-for="comparator in validateComparatorOptions" :key="comparator.label" :value="comparator.value"/>
        </a-select>
      </a-form-item>
      <a-form-item label="断言错误信息" name="message">
        <a-input v-model:value="formData.message"/>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import {reactive, ref, watch} from "vue";
import { defineProps, defineEmits, defineExpose } from 'vue';

const props = defineProps({});
const emits = defineEmits(['add','edit']);

const open = ref(false);
const isEdit = ref(false);
const formRef = ref(null);
const formDataState = {
  type: 'body',
  expected: '',
  expression: '',
  comparator: '',
  message: ''
}
const formData = ref({});
const recordCopy = ref(null);
const indexCopy = ref(null);
// 初始规则
const rules = ref({
  type: [
    { required: true, message: '请输入断言类型', trigger: 'blur' },
  ],
  expected: [
    { required: true, message: '请输入期望值', trigger: 'blur' },
  ],
  expression: [], // 初始为空，动态修改
  comparator: [
    { required: true, message: '请选择比较方式', trigger: 'change' },
  ]
});

// 监听 formData.type 的变化
watch(
  () => formData.value.type,
  (newType) => {
    if (newType === 'headers' || newType === 'body') {
      // 动态添加必填规则
      rules.value.expression = [
        { required: true, message: '请输入表达式', trigger: 'blur' },
      ];
    } else {
      // 移除必填规则
      rules.value.expression = [];
    }
  }
);

const validateComparatorOptions = ref([
  { label: '等于', value: 'eq' },
  { label: '不等于', value: 'neq' },
  { label: '大于', value: 'gt' },
  { label: '大于等于', value: 'gte' },
  { label: '小于', value: 'lt' },
  { label: '小于等于', value: 'lte' },
  { label: '包含', value: 'contains' },
  { label: '不包含', value: 'not_contains' },
  { label: '匹配正则', value: 'match' },
  { label: '不匹配正则', value: 'not_match' },
  { label: '为空', value: 'empty' },
  { label: '不为空', value: 'not_empty' },
  { label: '真', value: 'true' },
  { label: '假', value: 'false' },
  { label: '包含', value: 'contains' },
]);

const validateTypeOptions = ref([
  { label: '响应状态码', value: 'statusCode' },
  { label: '响应头', value: 'headers' },
  { label: '响应体', value: 'body' },
]);

const showModal = (record,index) => {
  recordCopy.value = record;
  indexCopy.value = index;
  if (!record){
    Object.assign(formData.value, formDataState);
  }else{
    Object.assign(formData.value, record);
  }
  open.value = true;
};

const save = async () => {
  try {
    if (formRef.value) {
      await formRef.value.validate();
    }
    if (recordCopy.value) {
      // Object.assign(recordCopy.value, formData.value);
      emits('edit', { ...recordCopy.value },indexCopy.value)
    } else {
      emits('add', { ...formData.value });
    }
    onCancel();
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

const onCancel = () => {
  recordCopy.value = null;
  formData.value = {...formDataState};
  open.value = false;
  indexCopy.value = null;
};

defineExpose({ showModal });
</script>