<template>
  <div class="api-explorer">
    <!-- 搜索框 -->
    <div class="search-container">
      <a-input-search
        v-model:value="searchValue"
        placeholder="搜索接口或分组"
        style="margin-bottom: 8px"
        @search="onSearch"
        @change="onSearchChange"
        allowClear
      />
    </div>

    <a-tree
      class="case-tree"
      :tree-data="treeData"
      :field-names="fieldNames"
      :expandedKeys="openKeys"
      :selectedKeys="selectedKeys"
      draggable
      blockNode
      showIcon
      @drop="onDrop"
      @select="onClick"
      @expand="onExpand"
    >
      <template #icon="{ dataRef }">
        <folder-outlined v-if="!dataRef.apiId" class="tree-icon folder-icon" />
        <file-outlined v-else class="tree-icon api-icon" />
      </template>

      <template #title="{ dataRef, key:treeKey }">
        <div
          class="tree-node-container"
          :class="{ 'is-api': dataRef.apiId, 'is-group': !dataRef.apiId }"
          @mouseenter="handleNodeHover(dataRef, true)"
          @mouseleave="handleNodeHover(dataRef, false)"
        >
          <!-- API节点 -->
          <template v-if="dataRef.apiId">
            <!-- 只有当method字段存在时才显示方法标签 -->
            <a-tag
              v-if="dataRef.method"
              :color="getMethodColor(dataRef.method)"
              class="method-tag"
              size="small"
            >
              {{ dataRef.method?.toUpperCase() }}
            </a-tag>
            <a-tooltip :title="dataRef.name" placement="topLeft">
              <span class="tree-node-title api-title">{{ dataRef.name }}</span>
            </a-tooltip>
          </template>

          <!-- 分组节点 -->
          <template v-else>
            <a-tooltip :title="dataRef.name" placement="topLeft">
              <span class="tree-node-title group-title">{{ dataRef.name }}</span>
            </a-tooltip>
            <span v-if="getApiCount(dataRef) > 0" class="api-count">
              ({{ getApiCount(dataRef) }})
            </span>
            <!-- 悬停时显示的操作按钮 -->
            <div
              v-if="hoveredNode === dataRef"
              class="tree-node-actions"
              @click.stop
              @mouseenter="handleDropdownHover(dataRef, true)"
              @mouseleave="handleDropdownHover(dataRef, false)"
            >
              <a-dropdown
                :trigger="['click']"
                placement="bottomRight"
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                :destroyPopupOnHide="true"
                @click="async (e) => await handleAddApi(dataRef)"
              >
                <a class="ant-dropdown-link" @click.prevent.stop>
                  <plus-outlined />
                </a>
              </a-dropdown>
              <a-dropdown
                :trigger="['click']"
                placement="bottomRight"
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                :destroyPopupOnHide="true"
              >
                <a class="ant-dropdown-link" @click.prevent.stop>
                  <more-outlined />
                </a>
                <template #overlay>
                  <a-menu @click="(e) => handleGroupMenuClick(e, dataRef)">
                    <a-menu-item key="copy">
                      <copy-outlined />
                      添加子目录
                    </a-menu-item>
                    <a-menu-item key="delete">
                      <delete-outlined />
                      删除
                    </a-menu-item>
                    <a-menu-item key="import">
                      <import-outlined />
                      导入
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </div>
          </template>
        </div>
      </template>
    </a-tree>

    <!-- 导入模态框 -->
    <a-modal
      v-model:open="importModalVisible"
      title="导入ApiFox-OpenAi3.0-Json文件"
      @ok="handleImportConfirm"
      @cancel="handleImportCancel"
      :maskClosable="false"
      :keyboard="false"
    >
      <div class="import-modal-content">
        <div class="upload-section">
          <a-upload
            v-model:file-list="uploadFileList"
            :before-upload="beforeUpload"
            accept=".json"
            :multiple="true"
          >
            <a-button>
              <upload-outlined />
              选择文件
            </a-button>
          </a-upload>
        </div>
      </div>
    </a-modal>
    <group-edit-modal ref='groupRef' group-type="api" @loadTree="fetchMenuTree"/>
  </div>
</template>

<script setup>
import {ref, onMounted} from 'vue';
import {message, Modal} from 'ant-design-vue';
import {
  FolderOutlined,
  FileOutlined,
  MoreOutlined,
  CopyOutlined,
  DeleteOutlined,
  PlusOutlined,
  ImportOutlined,
  UploadOutlined
} from '@ant-design/icons-vue';
import {Api} from "/@/api/business/apitest/api.js";
import {GroupApi} from "/@/api/business/apitest/group-api.js";
import GroupEditModal from "/@/views/business/apitest/group/group-edit-modal.vue";


const emits = defineEmits(["onClick", "refresh","addApi"]);
const groupRef = ref(null);
// 状态管理
const openKeys = ref([]);
const selectedKeys = ref([]);
const treeData = ref([]);
const originalTreeData = ref([]); // 保存原始树数据
const searchValue = ref('');
const hoveredNode = ref(null);
const hoverTimer = ref(null);

// 导入相关数据
const importModalVisible = ref(false);
const uploadFileList = ref([]);
const currentGroupId = ref(null);

// 获取HTTP方法对应的颜色
const getMethodColor = (method) => {
  const colors = {
    'GET': '#52c41a',
    'POST': '#1890ff',
    'PUT': '#faad14',
    'DELETE': '#ff4d4f',
    'PATCH': '#722ed1',
    'OPTIONS': '#13c2c2',
    'HEAD': '#eb2f96'
  };
  return colors[method?.toUpperCase()] || '#d9d9d9';
};

// 计算分组下的API数量
const getApiCount = (groupNode) => {
  let count = 0;

  // 计算直接子API
  if (groupNode.apis) {
    count += groupNode.apis.length;
  }

  // 递归计算子分组的API
  if (groupNode.children) {
    groupNode.children.forEach(child => {
      if (child.apiId) {
        count += 1;
      } else {
        count += getApiCount(child);
      }
    });
  }

  return count;
};

// 字段映射配置
const fieldNames = {
  children: 'children',
  title: 'name',
  key: 'key'
};

// 处理树数据，添加key和isLeaf属性
const processTreeData = (data) => {
  if (!data) return [];

  return data.map(item => {
    const node = {...item};

    if (node.groupId) {
      node.key = `group-${node.groupId}`;

      if (node.children && node.children.length > 0) {
        node.children = processTreeData(node.children);
      }

      if (node.apis && node.apis.length > 0) {
        const apiNodes = node.apis.map(apiItem => ({
          ...apiItem,
          key: `api-${apiItem.apiId}`,
          name: apiItem.name,
          isLeaf: true
        }));

        if (node.children) {
          node.children = [...node.children, ...apiNodes];
        } else {
          node.children = apiNodes;
        }
      }

      if ((!node.children || node.children.length === 0) &&
          (!node.apis || node.apis.length === 0)) {
        node.isLeaf = true;
      }
    }

    return node;
  });
};

const fetchMenuTree = async () => {
  try {
    const response = await Api.queryApiTree()
    const processedData = processTreeData(response.data);
    originalTreeData.value = processedData; // 保存原始数据
    treeData.value = processedData;
    openKeys.value = processedData.map(node => node.key);
  } catch (error) {
    console.error('获取菜单树出错:', error);
  }
};

// 处理点击事件
function onClick(selectedKeys, info) {
  if (selectedKeys.length === 0) return;
  const key = selectedKeys[0];
  emits('onClick', key);
}

// 处理展开事件
function onExpand(expandedKeys) {
  openKeys.value = expandedKeys;
}

// 搜索功能
const onSearch = (value) => {
  searchValue.value = value;
  performSearch(value);
};

const onSearchChange = (e) => {
  const value = e.target.value;
  if (!value) {
    // 清空搜索时恢复原始数据
    performSearch('');
  }
};

const performSearch = (searchText) => {
  if (!searchText) {
    // 恢复原始数据
    treeData.value = [...originalTreeData.value];
    openKeys.value = originalTreeData.value.map(node => node.key);
    return;
  }

  // 递归搜索树节点
  const searchTree = (nodes, searchValue) => {
    const result = [];

    for (const node of nodes) {
      const nodeClone = { ...node };
      let shouldInclude = false;

      // 检查当前节点是否匹配
      if (node.name && node.name.toLowerCase().includes(searchValue.toLowerCase())) {
        shouldInclude = true;
      }

      // 检查子节点
      if (node.children && node.children.length > 0) {
        const filteredChildren = searchTree(node.children, searchValue);
        if (filteredChildren.length > 0) {
          nodeClone.children = filteredChildren;
          shouldInclude = true;
        }
      }

      if (shouldInclude) {
        result.push(nodeClone);
      }
    }

    return result;
  };

  // 执行搜索
  const filteredData = searchTree(originalTreeData.value, searchText);
  treeData.value = filteredData;

  // 展开所有匹配的节点
  const getAllKeys = (nodes) => {
    const keys = [];
    const traverse = (nodeList) => {
      nodeList.forEach(node => {
        if (!node.apiId && !node.caseId) { // 只展开分组节点
          keys.push(node.key);
        }
        if (node.children) {
          traverse(node.children);
        }
      });
    };
    traverse(nodes);
    return keys;
  };

  openKeys.value = getAllKeys(filteredData);
};

// 处理拖拽事件
const onDrop = async (info) => {
  const dropKey = info.node.key;
  const dragKey = info.dragNode.key;
  const dropPos = info.node.pos.split('-');
  const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1]);

  // 解析拖拽节点和目标节点的信息
  const isDragApi = dragKey.startsWith('api-');
  const isDropApi = dropKey.startsWith('api-');
  const dragId = parseInt(dragKey.split('-')[1]);
  const dropId = parseInt(dropKey.split('-')[1]);

  console.log('拖拽信息:', {
    dragKey,
    dropKey,
    dropPos,
    dropPosition,
    isDragApi,
    isDropApi,
    dragId,
    dropId
  });

  try {
    // 不允许将分组拖到API下
    if (!isDragApi && isDropApi) {
      message.error('不能将分组拖到API下');
      return;
    }

    // 克隆数据
    const data = [...treeData.value];

    // 查找被拖拽节点
    let dragObj;
    const loop = (data, key, callback) => {
      for (let i = 0; i < data.length; i++) {
        if (data[i].key === key) {
          return callback(data, i, data[i]);
        }
        if (data[i].children) {
          loop(data[i].children, key, callback);
        }
      }
    };

    // 删除拖拽的节点
    loop(data, dragKey, (list, index, item) => {
      dragObj = item;
      list.splice(index, 1);
    });

    if (!dragObj) {
      return;
    }

    // 根据放置位置处理
    if (!info.dropToGap) {
      // 放到节点上 - 成为子节点
      loop(data, dropKey, (list, index, item) => {
        // 如果目标是API，不允许放入
        if (isDropApi) {
          message.error('不能将节点拖到API下');
          return;
        }

        // 目标是分组，放入其中
        item.children = item.children || [];

        // 更新拖拽节点的父级ID
        if (isDragApi) {
          dragObj.groupId = dropId;
        } else {
          dragObj.parentId = dropId;
        }

        // 放在开头
        item.children.unshift(dragObj);
      });
    } else if (dropPosition === -1) {
      // 放到节点前面
      loop(data, dropKey, (list, index, item) => {
        // 更新拖拽节点的父级ID
        if (isDragApi) {
          // 如果是API，父级是当前节点的父级
          dragObj.groupId = isDropApi ? item.groupId : item.parentId;
        } else {
          // 如果是分组，父级是当前节点的父级
          dragObj.parentId = item.parentId;
        }

        list.splice(index, 0, dragObj);
      });
    } else {
      // 放到节点后面
      loop(data, dropKey, (list, index, item) => {
        // 更新拖拽节点的父级ID
        if (isDragApi) {
          // 如果是API，父级是当前节点的父级
          dragObj.groupId = isDropApi ? item.groupId : item.parentId;
        } else {
          // 如果是分组，父级是当前节点的父级
          dragObj.parentId = item.parentId;
        }

        list.splice(index + 1, 0, dragObj);
      });
    }

    // 更新树数据
    treeData.value = data;

    message.success('更新菜单顺序成功');
  } catch (error) {
    console.error('更新菜单顺序失败:', error);
    message.error('更新菜单顺序失败');

    // 如果失败，重新加载树
    await fetchMenuTree();
  }
};

const handleNodeHover = (dataRef, isHover) => {
  // 清除之前的定时器
  if (hoverTimer.value) {
    clearTimeout(hoverTimer.value);
    hoverTimer.value = null;
  }

  if (isHover && !dataRef.apiId) {
    hoveredNode.value = dataRef;
  } else if (!isHover) {
    // 延迟隐藏，避免抖动
    hoverTimer.value = setTimeout(() => {
      hoveredNode.value = null;
    }, 300);
  }
};

// 处理dropdown区域悬停
const handleDropdownHover = (dataRef, isHover) => {
  if (hoverTimer.value) {
    clearTimeout(hoverTimer.value);
    hoverTimer.value = null;
  }

  if (isHover) {
    hoveredNode.value = dataRef;
  } else {
    hoverTimer.value = setTimeout(() => {
      hoveredNode.value = null;
    }, 300);
  }
};

const handleVisibleChange = (visible) => {
  console.log('Dropdown visible:', visible);
};

// 处理分组菜单点击
const handleGroupMenuClick = async (e, dataRef) => {
  switch (e.key) {
    case 'copy':
      await handleAddChildren(dataRef)
      break;
    case 'delete':
      await handleDelete(dataRef.groupId)
      break;
    case 'import':
      handleImport(dataRef);
      break;
  }

};

const handleAddChildren = async (dataRef) => {
  groupRef.value.showModel({parentId: dataRef.groupId})
};

const handleAddApi = async(dataRef) => {
  emits('onClick','api-')
  emits('addApi', dataRef.groupId)
  await fetchMenuTree()
};
const handleImport = (dataRef) => {
  console.log('打开导入模态框');
  currentGroupId.value = dataRef.groupId;
  importModalVisible.value = true;
};

const handleImportCancel = () => {
  importModalVisible.value = false;
  uploadFileList.value = [];
};

const handleImportConfirm = async () => {
  if (uploadFileList.value.length === 0) {
    message.warning('请先选择文件');
    return;
  }

  for (const item of uploadFileList.value) {
    try {
      const formData = new FormData();
      console.log('file', item.originFileObj);
      formData.append('file', item.originFileObj);
      formData.append('group_id', currentGroupId.value);
      await Api.importOpenai(formData);
      message.success(item.name + '导入成功');
      await fetchMenuTree()
    } catch (e) {
      message.error(item.name + '导入失败');
    }
  }

  importModalVisible.value = false;
  uploadFileList.value = [];
  emits('refresh');
};
const handleDelete = async (groupId) => {
  Modal.confirm({
    title: '删除分组',
    content: '确定要删除该分组及其所有接口？',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await GroupApi.delete({groupId: groupId})
        message.success('删除分组成功');
        await fetchMenuTree()
      } catch (e) {
        message.error('删除分组失败');
      }
    },
  });

}
// 文件上传前处理
const beforeUpload = () => {
  return false; // 阻止自动上传
};

defineExpose(['fetchMenuTree', 'performSearch'])

onMounted(() => {
  fetchMenuTree();
});
</script>

<style scoped>
.api-explorer {
  height: 100%;
  overflow: auto;
  background: #fafafa;
}

.search-container {
  padding: 8px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.case-tree {
  width: 100%;
  background: transparent;
}

/* 树节点容器 - 移除悬停动画 */
.tree-node-container {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 2px 0;
  border-radius: 4px;
  min-height: 24px;
  position: relative;
  /* 移除 transition: background-color 0.1s ease-out; */
}

.tree-node-container:hover {
  background-color: #f0f0f0;
}

/* API节点样式 */
.tree-node-container.is-api {
  gap: 8px;
}

/* 分组节点样式 */
.tree-node-container.is-group {
  justify-content: space-between;
}

/* 树图标样式 */
.tree-icon {
  font-size: 14px;
}

.folder-icon {
  color: #faad14;
}

.api-icon {
  color: #1890ff;
}

/* HTTP方法标签 */
.method-tag {
  min-width: 45px;
  text-align: center;
  font-size: 10px;
  font-weight: 500;
  border: none;
  margin: 0;
  height: 18px;
  line-height: 16px;
  flex-shrink: 0;
}

/* 节点标题 */
.tree-node-title {
  font-size: 13px;
  line-height: 1.4;
  cursor: pointer;
}

/* API标题 */
.api-title {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 分组标题 */
.group-title {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
}

/* API数量显示 */
.api-count {
  color: #999;
  font-size: 11px;
  white-space: nowrap;
  margin-left: 8px;
  flex-shrink: 0;
}

/* 节点操作按钮 - 移除过渡动画 */
.tree-node-actions {
  display: flex;
  opacity: 0;
  margin-left: 8px;
  flex-shrink: 0;
  padding: 2px;
  border-radius: 2px;
  position: relative;
  z-index: 10;
}

.tree-node-container:hover .tree-node-actions,
.tree-node-actions:hover {
  opacity: 1;
}

/* dropdown按钮样式 */
.tree-node-actions .ant-dropdown-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 2px;
  transition: background-color 0.05s; /* 减少过渡时间 */
}

.tree-node-actions .ant-dropdown-link:hover {
  background-color: #e6f7ff;
}

/* 优化树节点的过渡效果 */
.tree-node-container {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 2px 0;
  border-radius: 4px;
  transition: background-color 0.05s ease-out; /* 减少过渡时间 */
  min-height: 24px;
  position: relative;
}

/* 自定义ant-design样式 */
:deep(.ant-tree-node-content-wrapper) {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0 4px !important;
  border-radius: 4px;
  line-height: 24px;
}

:deep(.ant-tree-node-content-wrapper:hover) {
  background-color: transparent !important;
}

:deep(.ant-tree-node-content-wrapper.ant-tree-node-selected) {
  background-color: #e6f7ff !important;
}

:deep(.ant-tree-title) {
  width: 100%;
  margin-left: 0 !important;
}

/* 确保图标和内容对齐 */
:deep(.ant-tree-iconEle) {
  width: 16px;
  height: 16px;
  line-height: 16px;
  text-align: center;
  margin-right: 8px;
}

/* 拖拽样式 */
:deep(.ant-tree-drop-indicator) {
  background-color: #1890ff;
}

:deep(.ant-tree-treenode-dragging) {
  background-color: #e6f7ff;
  border: 1px dashed #1890ff;
}

/* 树节点间距调整 */
:deep(.ant-tree-treenode) {
  padding: 1px 0;
}

:deep(.ant-tree-child-tree) {
  margin-left: 24px;
}

/* 选中状态样式 */
:deep(.ant-tree-node-selected .tree-node-title) {
  color: #1890ff;
  font-weight: 500;
}

/* 确保展开/收起图标对齐 */
:deep(.ant-tree-switcher) {
  width: 16px;
  height: 24px;
  line-height: 24px;
  text-align: center;
}

/* 修复节点内容对齐 */
:deep(.ant-tree-node-content-wrapper) {
  position: relative;
}

:deep(.ant-tree-node-content-wrapper .ant-tree-iconEle) {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}

:deep(.ant-tree-node-content-wrapper .ant-tree-title) {
  margin-left: 24px !important;
}

/* dropdown 按钮样式 */
.ant-dropdown-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 2px;
  color: #666;
  text-decoration: none;
}

.ant-dropdown-link:hover {
  background-color: #e6f7ff;
  color: #1890ff;
}

/* 确保图标不会抖动 */
.ant-dropdown-link .anticon {
  font-size: 12px;
  transition: none;
}

/* 导入模态框样式 */
.import-modal-content {
  padding: 16px 0;
}

.upload-section {
  text-align: center;
}
</style>
