<template>
  <div class="operation-container">
    <!-- 中间位置的添加按钮 -->
    <div class="add-button-container">
      <a-button type="primary" @click="showModal">
        <template #icon><plus-outlined /></template>
        {{props.operationTitle}}
      </a-button>
    </div>

    <!-- 已添加的操作列表 -->
    <a-collapse v-if="operationList?.length > 0">
      <a-collapse-panel
        v-for="(operation, operationIndex) in operationList"
        :key="operation.type + operationIndex"
        :header="getOperationHeader(operation)"
      >
        <template #extra>
          <a-button type="text" danger @click.stop="removeOperation(operationIndex)">
            <delete-outlined />
          </a-button>
        </template>

        <div v-if="operation.type === 'script'">
          <a-form layout="vertical">
            <a-form-item label="脚本内容">
              <script-editor
                v-model="operation.code"
                :rows="8"
              />
            </a-form-item>
            <a-form-item label="描述">
              <a-input
                v-model:value="operation.description"
                placeholder="请输入脚本描述"
              />
            </a-form-item>
          </a-form>
        </div>
        <div v-else-if="operation.type === 'database'">
          <a-form layout="vertical">
            <database-select v-model:model-value="operation.database"/>
            <a-form-item label="SQL语句">
              <a-textarea
                v-model:value="operation.operations.sql"
                :rows="4"
                placeholder="请输入SQL语句"
              />
            </a-form-item>
            <a-form-item label="参数">
              <a-input
                v-model:value="operation.operations.params"
                placeholder="请输入参数，以逗号分隔"
              />
            </a-form-item>
          </a-form>
        </div>
        <div v-else-if="operation.type === 'extract_variable'">
          <a-table
            :data-source="operation.extracts"
            :columns="extractColumns"
            :pagination="false"
          >
            <template #bodyCell="{ column, record,index}">
              <div class="action-buttons">
                <template v-if="column.dataIndex !== 'action'">
                  <a-input
                    v-if="extractEditingIndex===`${operationIndex}-${index}`"
                    v-model:value="extractEditingItem[column.dataIndex]"
                  />
                  <template v-else>
                    {{record[column.dataIndex]}}
                  </template>
                </template>
                <template v-else-if="column.dataIndex === 'action'">
                  <a-button v-if="extractEditingIndex !== `${operationIndex}-${index}`" @click="extractClick(record,index,operationIndex)">
                    <template #icon><EditOutlined /></template>
                  </a-button>
                  <template v-else>
                    <div aria-atomic="true">
                      <a-button @click="saveExtractEditing(record)">
                      <template #icon><CheckOutlined /></template>
                      </a-button>
                      <a-button @click="closeExtractEditing">
                        <template #icon><CloseOutlined /></template>
                      </a-button>
                    </div>
                  </template>
                  <a-button v-if="extractEditingIndex !== `${operationIndex}-${index}`" @click="copyExtract(operation,record)">
                    <template #icon><CopyOutlined /></template>
                  </a-button>
                  <a-button v-if="extractEditingIndex !== `${operationIndex}-${index}`" @click="deleteExtract(operation,index)">
                    <template #icon><DeleteOutlined /></template>
                  </a-button>
                </template>
              </div>
            </template>
          </a-table>
        </div>
        <div v-else-if="operation.type === 'assertion'">
          <a-table
            :data-source="operation.assertions"
            :columns="assertColumns"
            :pagination="false"
          >
            <template #bodyCell="{ column, record,index}">
              <div class="action-buttons">
                <template v-if="column.dataIndex !== 'action'">
                  <a-input
                    v-if="extractEditingIndex===`${operationIndex}-${index}`"
                    v-model:value="extractEditingItem[column.dataIndex]"
                  />
                  <template v-else>
                    {{record[column.dataIndex]}}
                  </template>
                </template>
                <template v-else-if="column.dataIndex === 'action'">
                  <a-button v-if="extractEditingIndex !== `${operationIndex}-${index}`" @click="extractClick(record,index,operationIndex)">
                    <template #icon><EditOutlined /></template>
                  </a-button>
                  <template v-else>
                    <div aria-atomic="true">
                      <a-button @click="saveExtractEditing(record)">
                      <template #icon><CheckOutlined /></template>
                      </a-button>
                      <a-button @click="closeExtractEditing">
                        <template #icon><CloseOutlined /></template>
                      </a-button>
                    </div>
                  </template>
                  <a-button v-if="extractEditingIndex !== `${operationIndex}-${index}`" @click="copyExtract(operation,record)">
                    <template #icon><CopyOutlined /></template>
                  </a-button>
                  <a-button v-if="extractEditingIndex !== `${operationIndex}-${index}`" @click="deleteExtract(operation,index)">
                    <template #icon><DeleteOutlined /></template>
                  </a-button>
                </template>
              </div>
            </template>
          </a-table>
        </div>
      </a-collapse-panel>
    </a-collapse>

    <!-- 空状态提示 -->
    <a-empty v-else :description="props.operationTitle" />

    <!-- 选择操作类型的模态框 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="props.operationTitle"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      :footer="null"
      :width="modalWidth"
    >
      <div class="operation-type-list">
        <a-card
          v-for="option in props.operationOptions"
          :key="option.value"
          hoverable
          class="operation-card"
          @click="selectOperation(option)"
        >
          <template #cover>
            <div class="operation-icon">
              <component :is="option.icon" :style="{fontSize: '28px', color: option.color}" />
            </div>
          </template>
          <a-card-meta :title="option.label" :description="option.description" />
        </a-card>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import {ref, defineProps, defineEmits, watch, computed, onMounted} from "vue";
import {databaseApi} from "/@/api/business/apitest/database-api.js";
import {
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  CheckOutlined,
  CloseOutlined,
  CopyOutlined
} from "@ant-design/icons-vue";
import ScriptEditor from "/@/views/business/apitest/case/components/script-editor.vue";
import {message} from "ant-design-vue";
import DatabaseSelect from "/@/views/business/apitest/database/database-select.vue";

const props = defineProps({
  operationTitle: {
    type: String,
    default: '默认操作'
  },
  operations: {
    type: Array,
    default: ()=>[]
  },
  operationOptions: {
    type: Array,
    default: ()=>[]
  }
})
const emit = defineEmits(['update:operations']);
const databases = ref([])
// 模态框显示状态
const modalVisible = ref(false);
const extractEditingIndex = ref('')
const extractEditingItem = ref({})
function extractClick(record,index,operationIndex){
  extractEditingIndex.value = `${operationIndex}-${index}`
  extractEditingItem.value = {...record}
}
function saveExtractEditing(record){
  if (!extractEditingItem.value.name?.trim()){
    message.error('请输入变量名')
    return
  }
  if (!extractEditingItem.value.path?.trim()){
    message.error('请输入提取表达式')
    return
  }
  Object.assign(record,extractEditingItem.value)
  extractEditingIndex.value = null
}
function closeExtractEditing(){
  extractEditingItem.value = {}
  extractEditingIndex.value = null
}
function copyExtract(operation,record){
  console.log('record',record)
  operation.extracts.push({...record})
}
function deleteExtract(operation,index){
  operation.extracts.splice(index,1)
}
// 前置操作列表
const operationList = computed({
 get: () => props.operations ? [...props.operations] : [],
 set: (value) => emit('update:operations', value)
});
//变量提取列表
const extractColumns = [
  {
    title: '变量名',
    dataIndex: 'name'
  },
  {
    title: '提取表达式',
    dataIndex: 'path'
  },
  {
    title: '提取类型',
    dataIndex: 'type'
  },
    {
    title: '默认值',
    dataIndex: 'default'
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 100
  }
]

const assertColumns = [
  {
    title: '断言名称',
    dataIndex: 'name'
  },
  {
    title: '断言路径',
    dataIndex: 'path'
  },
  {
    title: '期望值',
    dataIndex: 'expected'
  },
    {
    title: '比较运算符',
    dataIndex: 'operator'
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 100
  }
]
// 显示模态框
const showModal = () => {
  modalVisible.value = true;
  console.log((operationList.value))
};

// 处理模态框确认
const handleModalOk = () => {
  modalVisible.value = false;
};

// 处理模态框取消
const handleModalCancel = () => {
  modalVisible.value = false;
};

// 选择操作类型
const selectOperation = (option) => {
  // 添加新操作到列表
  operationList.value.push({...option.template});
  // 关闭模态框
  modalVisible.value = false;
  // 通知父组件
  emit('update:operations', operationList.value);
};

// 删除操作
const removeOperation = (index) => {
  operationList.value.splice(index, 1);
  // 通知父组件
  emit('update:operations', operationList.value);
};

// 获取操作标题
const getOperationHeader = (operation) => {
  if (operation.type === '自定义脚本') {
    return `${operation.type}${operation.data.description ? ': ' + operation.data.description : ''}`;
  } else if (operation.type === '数据库操作') {
    return `${operation.type}: ${operation.data.connection || '未指定连接'}`;
  } else if (operation.type === '接口调用') {
    return `${operation.type}: ${operation.data.url || '未指定URL'}`;
  }
  return operation.type;
};
// 动态计算模态框宽度
const modalWidth = computed(() => {
  const optionCount = props.operationOptions.length;
  const cardWidth = 160; // 每个卡片的宽度
  const gap = 16; // 卡片间距
  const padding = 48; // 模态框内边距

  const totalWidth = optionCount * cardWidth + (optionCount - 1) * gap + padding;

  // 设置最小和最大宽度
  const minWidth = 400;
  const maxWidth = Math.min(1200, window.innerWidth * 0.9);

  return Math.max(minWidth, Math.min(maxWidth, totalWidth)) + 'px';
});

async function loadDatabase() {
  try{
    const response = await databaseApi.getList();
    databases.value = response.data;
  }catch (e) {
    message.error("获取数据库列表失败")
  }
}

onMounted(async()=>{
  await loadDatabase()
})
</script>

<style scoped>
.operation-container {
  width: 100%;
  padding: 16px 0;
}

.add-button-container {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.operation-type-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  justify-content: center;
}

.operation-card {
  width: 140px;
  cursor: pointer;
  transition: all 0.3s;
}

.operation-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.operation-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80px;
  background-color: #f5f5f5;
}
.operation-type-list {
  display: flex;
  flex-wrap: nowrap;
  gap: 16px;
  justify-content: center;
  align-items: stretch;
}

.operation-card {
  flex: 1;
  min-width: 140px;
  max-width: 200px;
  cursor: pointer;
  transition: all 0.3s;
}

.operation-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.operation-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60px;
  background: #fafafa;
}
.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

</style>