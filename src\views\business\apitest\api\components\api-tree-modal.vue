<template>
  <a-modal
    v-model:visible="modalVisible"
    title="选择API接口"
    width="800px"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="api-tree-container">
      <!-- 搜索框 -->
      <div class="search-container">
        <a-input-search
          v-model:value="searchValue"
          placeholder="搜索接口名称或URL"
          style="width: 100%"
          @search="onSearch"
        />
      </div>

      <!-- 左侧模块树 -->
      <div class="tree-container">
        <a-spin :spinning="loading">
          <a-tree
            v-if="treeData.length > 0"
            :tree-data="treeData"
            :field-names="{title: 'name',key: 'groupId',children: 'children'}"
            :defaultExpandAll="false"
            :selectedKeys="selectedKeys"
            @select="onSelectNode"
          >
            <template #title="{ name, groupId, isLeaf }">
              <span :class="{ 'api-node': isLeaf }">{{ name }}</span>
            </template>
          </a-tree>
          <a-empty v-else description="暂无数据" />
        </a-spin>
      </div>

      <!-- 右侧API列表 -->
      <div class="api-list-container">
        <a-spin :spinning="apiLoading">
          <a-table
            :dataSource="apiList"
            :columns="columns"
            :pagination="false"
            size="small"
            :scroll="{ y: 400 }"
            :rowKey="record => record.apiId"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'method'">
                <a-tag :color="getMethodColor(record.method)">
                  {{ record.method.toUpperCase() }}
                </a-tag>
              </template>
              <template v-else-if="column.dataIndex === 'action'">
                <a-button type="primary" size="small" @click="selectApi(record)">
                  选择
                </a-button>
              </template>
            </template>
          </a-table>
        </a-spin>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="modal-footer">
      <a-button @click="handleCancel">取消</a-button>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import {defineExpose} from 'vue'
import {GroupApi} from "/@/api/business/apitest/group-api.js";
import {Api} from "/@/api/business/apitest/api.js";

// 数据
const modalVisible = ref(false);
const searchValue = ref('');
const treeData = ref([]);
const apiList = ref([]);
const selectedKeys = ref([]);
const loading = ref(false);
const apiLoading = ref(false);
const currentModuleId = ref(null);
const onSelectCallback = ref(null);

// 表格列定义
const columns = [
  {
    title: '方法',
    dataIndex: 'method',
    width: 80
  },
  {
    title: '接口名称',
    dataIndex: 'name',
    ellipsis: true
  },
  {
    title: 'URL',
    dataIndex: 'url',
    ellipsis: true
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 80,
    fixed: 'right'
  }
];

// 获取API方法颜色
const getMethodColor = (method) => {
  const methodColors = {
    'get': 'green',
    'post': 'blue',
    'put': 'orange',
    'delete': 'red',
    'patch': 'purple'
  };
  return methodColors[method?.toLowerCase()] || 'default';
};

// 加载分组树
const loadGroupTree = async () => {
  loading.value = true;
  try {
    const response = await GroupApi.getGroupTree();
      // 转换数据格式为树形结构
    treeData.value = response.data;

  } catch (error) {
    console.error('加载模块树失败:', error);
    message.error('加载模块树失败');
  } finally {
    loading.value = false;
  }
};

// 选择节点
const onSelectNode = (selectedKeys, {selected, node}) => {
  if (selected) {
    currentModuleId.value = node.key;
    loadApiList(node.key);
  }
};

// 加载API列表
const loadApiList = async (groupId) => {
  apiLoading.value = true;
  try {
    const response = await Api.getByGroupId(groupId);
    apiList.value = response.data;
  } catch (error) {
    console.error('加载API列表失败:', error);
    message.error('加载API列表失败');
    apiList.value = [];
  } finally {
    apiLoading.value = false;
  }
};

// 搜索API
const onSearch = async (value) => {
  if (!value) {
    if (currentModuleId.value) {
      await loadApiList(currentModuleId.value);
    }
    apiList.value = [];
    return;
  }

  apiLoading.value = true;
  try {
    const response = await Api.search(value);
    apiList.value = response.data;
  } catch (error) {
    console.error('搜索API失败:', error);
    message.error('搜索API失败');
    apiList.value = [];
  } finally {
    apiLoading.value = false;
  }
};

// 选择API
const selectApi = (api) => {
  if (onSelectCallback.value && typeof onSelectCallback.value === 'function') {
    onSelectCallback.value(api);
  }
  handleCancel();
};

// 取消选择
const handleCancel = () => {
  modalVisible.value = false;
  // 重置状态
  searchValue.value = '';
  selectedKeys.value = [];
  currentModuleId.value = null;
  apiList.value = [];
  onSelectCallback.value = null;
};

// 显示弹框
const show = (callback) => {
  modalVisible.value = true;
  onSelectCallback.value = callback;
  // 加载模块树
  loadGroupTree();
};

// 暴露方法给父组件
defineExpose({
  show
});
</script>

<style scoped>
.api-tree-container {
  display: flex;
  flex-direction: column;
  height: 500px;
}

.search-container {
  margin-bottom: 16px;
}

.tree-container {
  height: 200px;
  overflow: auto;
  border: 1px solid #f0f0f0;
  padding: 12px;
  margin-bottom: 16px;
}

.api-list-container {
  flex: 1;
  overflow: auto;
}

.api-node {
  color: #1890ff;
}

.modal-footer {
  margin-top: 16px;
  text-align: right;
}
</style>