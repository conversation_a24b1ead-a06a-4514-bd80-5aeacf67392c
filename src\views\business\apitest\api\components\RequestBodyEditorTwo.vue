<template>
  <div class="request-body-editor">
    <a-tabs class="body-tabs" v-model:active-key="activeKey" @change="onChangeTab">
      <a-tab-pane :key="null" tab="none" />
      <a-tab-pane v-for="type in contentTypeList" :key="type" :tab="processTab(type)" />
    </a-tabs>
    <div class="editor-content">
      <template v-if="activeKey === null">
        <a-empty description="暂无请求体数据" />
      </template>
      <template v-else-if="activeKey === ContentTypeEnum.JSON">
        <div class="json-section">
          <div class="json-toolbar">
            <a-space>
              <a-tooltip title="格式化 JSON">
                <a-button size="small" type="text" @click="formatJson" :disabled="!!jsonError">
                  <code-outlined />
                </a-button>
              </a-tooltip>
              <a-tooltip title="压缩 JSON">
                <a-button size="small" type="text" @click="compactJson" :disabled="!!jsonError">
                  <compress-outlined />
                </a-button>
              </a-tooltip>
              <a-tooltip title="清空内容">
                <a-button size="small" type="text" @click="clearJson">
                  <clear-outlined />
                </a-button>
              </a-tooltip>
            </a-space>
          </div>
          <div class="json-editor-container">
            <monaco-editor
              v-model:value="jsonBody"
              :options="jsonEditorOptions"
              language="json"
              class="json-editor"
              @change="updateJsonBody"
            />
          </div>
          <a-alert v-if="jsonError" type="error" :message="jsonError" show-icon class="json-error-alert" />
        </div>
      </template>
      <template v-else-if="activeKey === ContentTypeEnum.FORM">
        <a-table :dataSource="localData" :columns="formDataColumns" :pagination="false" size="small" bordered :scroll="{ y: 300 }" rowKey="id">
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'enabled'">
              <a-checkbox v-model:checked="record.enabled" />
            </template>

            <template v-else-if="column.dataIndex === 'name'">
              <a-input v-model:value="record.name" placeholder="字段名" />
            </template>

            <template v-else-if="column.dataIndex === 'value'">
              <a-input v-model:value="record.value" placeholder="字段值" />
            </template>

            <template v-else-if="column.dataIndex === 'action'">
              <a-space>
                <a-tooltip title="复制">
                  <a-button type="text" size="small" @click="copyFormData(record)">
                    <copy-outlined />
                  </a-button>
                </a-tooltip>
                <a-tooltip title="删除">
                  <a-button type="text" danger size="small" @click="removeFormData(index)">
                    <delete-outlined />
                  </a-button>
                </a-tooltip>
              </a-space>
            </template>
          </template>
        </a-table>

        <div class="editor-footer">
          <a-button type="dashed" block @click="addFormData">
            <plus-outlined />
            添加表单项
          </a-button>
        </div>

        <a-divider style="margin: 16px 0">
          <a-tooltip title="在编辑器中查看">
            <a-button type="link" size="small" @click="showFormDataRaw = !showFormDataRaw">
              <code-outlined />
              查看原始数据
            </a-button>
          </a-tooltip>
        </a-divider>

        <div v-if="showFormDataRaw" class="raw-form-data">
          <a-textarea :value="formDataAsString" :rows="6" readonly />
        </div>
      </template>
      <template v-else-if="activeKey === ContentTypeEnum.MULTIPART">
        <div class="upload-container">
          <a-upload v-model:fileList="localFiles" :beforeUpload="() => false" multiple @change="handleFileChange">
            <a-button>
              <upload-outlined />
              选择文件
            </a-button>
          </a-upload>
        </div>
      </template>
      <template v-else-if="activeKey === ContentTypeEnum.XML">
        <div class="xml-editor-container">
          <monaco-editor
            :value="localContent"
            :options="xmlEditorOptions"
            language="xml"
            class="xml-editor"
          />
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
  import { computed, defineEmits, defineProps, ref, watch } from 'vue';
  import { message } from 'ant-design-vue';
  import {
    CodeOutlined,
    CopyOutlined,
    DeleteOutlined,
    PlusOutlined,
    UploadOutlined,
    CompressOutlined,
    ClearOutlined
  } from '@ant-design/icons-vue';
  import MonacoEditor from 'monaco-editor-vue3';
  import { codeEditorOptions } from '/@/config/code-editor-config.js';
  import { ContentTypeEnum } from '/@/views/business/apitest/api/enum/content-type-enum.js';
  import {debounce} from "lodash";

  const props = defineProps({
    data: {
      type: [Array,null],
      default: null,
    },
    json: {
      type: Object,
      default: null,
    },
    content: {
      type: String,
      default: null,
    },
    files: {
      type: [Array,null],
      default: null,
    },
    headerMap: {
      type: [Object,null],
      default: null,
    },
  });

  const emit = defineEmits(['update:data', 'update:json','update:content','update:files','update:content-type']);

  const localFiles = ref(props.files || [])
  const localData = ref(props.data || []);
  const localContent = ref(props.content || '');
  const contentTypeList = computed(() => {
    return Object.values(ContentTypeEnum);
  });
  const processTab = (key) => {
    return key.split('/')[1];
  };
  const activeKey = ref(props.headerMap?.['content-type'] || null);

  // 初始化 JSON 内容，确保格式正确
  const initJsonBody = (body) => {
    try {
      if (body) {
        // 如果已经是对象，直接格式化
        return JSON.stringify(body, null, 2);
      }
      return '{}';
    } catch (error) {
      console.warn('JSON 初始化失败，使用默认值:', error);
      return '{}';
    }
  };
  const jsonBody = ref(initJsonBody(props.json));
  const jsonError = ref('');
  const showFormDataRaw = ref(false);

  // JSON 编辑器专用配置
  const jsonEditorOptions = computed(() => ({
    ...codeEditorOptions,
    height: 300, // 固定高度
    automaticLayout: true,
    scrollBeyondLastLine: false,
    wordWrap: 'on',
    minimap: { enabled: false },
    folding: true,
    lineNumbers: 'on',
    formatOnPaste: true,
    formatOnType: true
  }));

  // XML 编辑器专用配置
  const xmlEditorOptions = computed(() => ({
    ...codeEditorOptions,
    height: 300, // 固定高度
    automaticLayout: true,
    scrollBeyondLastLine: false,
    wordWrap: 'on',
    minimap: { enabled: false },
    folding: true,
    lineNumbers: 'on'
  }));

  watch(()=>props.data,(newData)=>{
    localData.value = newData;
  })
  watch(()=>props.files,(newData)=>{
    localFiles.value = newData;
  })
  watch(()=>props.json,(newData)=>{
    jsonBody.value = initJsonBody(newData);
  },{ deep: true })
  watch(()=>props.content,(newData)=>{
    localContent.value = newData;
  })

  // 监听 contentType 变化，更新 activeKey
  watch(
    () => props.headerMap?.['content-type'],
    (newContentType) => {
      activeKey.value = newContentType;
    },
    { immediate: true }
  );
  watch(()=>localData.value,(newData)=>{
    localData.value = newData;
  })
  watch(()=>localContent.value,(newData)=>{
    localContent.value = newData;
  })
  watch(()=>localFiles.value,(newData)=>{
    localFiles.value = newData;
  })
  const debouncedUpdateJsonBody = debounce((value) => {
    try {
      jsonError.value = '';
      if (!value.trim()) {
        emit('update:json', null);
      }else{
         emit('update:json', JSON.parse(value));
      }
    } catch (error) {
      jsonError.value = error.message;
      message.error('JSON 格式错误',error.message)
    }
  }, 500); // 500ms 防抖延迟
  function updateJsonBody(value){
    debouncedUpdateJsonBody(value);
  }

  // 表单数据列定义
  const formDataColumns = [
    { title: '', dataIndex: 'enabled', width: 50 },
    { title: '字段名', dataIndex: 'name', width: '40%' },
    { title: '字段值', dataIndex: 'value', width: '40%' },
    { title: '操作', dataIndex: 'action', width: '10%' },
  ];

  // 表单数据转字符串
  const formDataAsString = computed(() => {
    return localData.value
      ?.filter((item) => item.enabled !== false && item.name)
      .map((item) => `${item.name}=${item.value || ''}`)
      .join('&');
  });

  // 添加表单数据
  const addFormData = () => {
    localData.value.push({
      enabled: true,
      name: '',
      value: '',
    });
  };

  // 删除表单数据
  const removeFormData = (index) => {
    localData.value.splice(index, 1);
  };

  // 复制表单数据
  const copyFormData = (record) => {
    localData.value.push({ ...record});
    message.success('表单项已复制');
  };

  const onChangeTab = (value) => {
    console.log('tab',value)
    emit('update:content-type', value);
  };

  // JSON 操作方法
  const formatJson = () => {
    try {
      const parsed = JSON.parse(jsonBody.value);
      jsonBody.value = JSON.stringify(parsed, null, 2);
      message.success('JSON 已格式化');
    } catch (error) {
      message.error('JSON 格式错误，无法格式化');
    }
  };

  const compactJson = () => {
    try {
      const parsed = JSON.parse(jsonBody.value);
      jsonBody.value = JSON.stringify(parsed);
      message.success('JSON 已压缩');
    } catch (error) {
      message.error('JSON 格式错误，无法压缩');
    }
  };

  const clearJson = () => {
    jsonBody.value = '{}';
    message.success('JSON 已清空');
  };

  // 获取当前激活的请求体数据
  const getCurrentRequestBody = () => {
    if (!activeKey.value) {
      return null;
    }
    switch (activeKey.value) {
      case ContentTypeEnum.JSON:
        try {
          return jsonBody.value.trim() ? JSON.parse(jsonBody.value) : null;
        } catch (error) {
          message.error('JSON 错误: ' + error.message)
          console.error('JSON 解析错误:', error);
          return null;
        }
      case ContentTypeEnum.FORM:
        return localData.value?.filter(item => item.enabled !== false && item.name) || [];
      case ContentTypeEnum.MULTIPART:
        return localFiles.value || [];
      case ContentTypeEnum.XML:
        return localContent.value || '';
      default:
        return null;
    }
  };

  // 暴露方法给父组件
  defineExpose({
    getCurrentRequestBody,
    getName:()=>"张三"
  });
</script>

<style scoped>
  .request-body-editor {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
  }

  .body-tabs {
    flex-shrink: 0;
    background: #fff;
    border-bottom: 1px solid #e8e8e8;
  }

  :deep(.ant-tabs-nav) {
    margin: 0;
    padding: 0 16px;
  }

  :deep(.ant-tabs-tab) {
    padding: 12px 16px;
    margin: 0 4px;
    border-radius: 6px 6px 0 0;
    transition: all 0.3s ease;
  }

  :deep(.ant-tabs-tab:hover) {
    background: #f5f5f5;
  }

  :deep(.ant-tabs-tab-active) {
    background: #fff;
    border: 1px solid #e8e8e8;
    border-bottom: 1px solid #fff;
    margin-bottom: -1px;
  }

  .editor-content {
    flex: 1;
    position: relative;
    overflow: auto;
    padding: 16px;
    background: #fff;
    max-height: calc(100vh - 300px);
  }

  .json-section {
    display: flex;
    flex-direction: column;
    gap: 12px;
    height: 100%;
  }

  .json-toolbar {
    display: flex;
    justify-content: flex-end;
    padding: 8px 12px;
    background: #f8f9fa;
    border: 1px solid #e8e8e8;
    border-bottom: none;
    border-radius: 8px 8px 0 0;
  }

  .json-toolbar :deep(.ant-btn) {
    color: #666;
    border: none;
    background: transparent;
    transition: all 0.3s ease;
  }

  .json-toolbar :deep(.ant-btn:hover) {
    background: #e8e8e8;
    color: #333;
  }

  .json-toolbar :deep(.ant-btn:disabled) {
    color: #ccc;
  }

  .json-editor-container,
  .xml-editor-container {
    height: 350px;
    width: 100%;
    border: 1px solid #e8e8e8;
    border-top: none;
    border-radius: 0 0 8px 8px;
    overflow: hidden;
    background: #fff;
  }

  .json-editor,
  .xml-editor {
    height: 100% !important;
    width: 100% !important;
  }

  :deep(.monaco-editor) {
    width: 100% !important;
    height: 100% !important;
  }

  :deep(.monaco-editor .margin) {
    background: #f8f9fa !important;
  }

  .upload-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 300px;
    padding: 40px;
    background: #fafafa;
    border-radius: 8px;
    border: 2px dashed #d9d9d9;
    transition: all 0.3s ease;
  }

  .upload-container:hover {
    border-color: #1890ff;
    background: #f0f8ff;
  }

  :deep(.ant-upload-list) {
    margin-top: 20px;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
  }

  :deep(.ant-upload .ant-btn) {
    font-size: 16px;
    height: 40px;
    padding: 0 24px;
    border-radius: 6px;
    background: #1890ff;
    border: none;
    color: #fff;
    font-weight: 500;
    transition: all 0.3s ease;
  }

  :deep(.ant-upload .ant-btn:hover) {
    background: #40a9ff;
  }

  /* 表格样式优化 */
  :deep(.ant-table) {
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #e8e8e8;
  }

  :deep(.ant-table-thead > tr > th) {
    background: #fafafa;
    color: #333;
    border: none;
    font-weight: 500;
  }

  :deep(.ant-table-tbody > tr > td) {
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.3s ease;
  }

  :deep(.ant-table-tbody > tr:hover > td) {
    background: #f5f5f5;
  }

  .editor-footer {
    margin-top: 16px;
  }

  .editor-footer :deep(.ant-btn) {
    height: 40px;
    border-radius: 8px;
    border: 2px dashed #d9d9d9;
    background: #fafafa;
    color: #666;
    font-weight: 500;
    transition: all 0.3s ease;
  }

  .editor-footer :deep(.ant-btn:hover) {
    border-color: #1890ff;
    background: #e6f7ff;
    color: #1890ff;
  }

  .raw-form-data {
    margin-top: 16px;
  }

  .raw-form-data :deep(.ant-input) {
    border-radius: 8px;
    border: 1px solid #e8e8e8;
    background: #f8f9fa;
  }

  .json-error-alert {
    margin-top: 12px;
    border-radius: 8px;
    border: none;
    box-shadow: 0 2px 8px rgba(255, 77, 79, 0.2);
    flex-shrink: 0;
  }

  /* 空状态样式 */
  :deep(.ant-empty) {
    padding: 60px 20px;
  }

  :deep(.ant-empty-description) {
    color: #999;
    font-size: 14px;
  }

  /* 分割线样式 */
  :deep(.ant-divider) {
    border-color: #e8e8e8;
    margin: 20px 0;
  }

  :deep(.ant-divider-inner-text) {
    background: #fafafa;
    color: #666;
    font-size: 12px;
  }
</style>
