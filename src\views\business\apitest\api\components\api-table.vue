<a-table
    :scroll="{ x: 1300 }"
    size="small"
    :dataSource="tableData"
    :columns="columns"
    rowKey="id"
    :pagination="false"
    :loading="tableLoading"
    bordered
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'action'">
        <div class="smart-table-operate">
          <a-button @click="showDrawer(record)" size="small" type="link">编辑</a-button>
          <a-button @click="confirmDelete(record.enterpriseId)" size="small" danger type="link">删除</a-button>
        </div>
      </template>
    </template>
  </a-table>
<script setup>

import {defineProps, defineEmits, ref, reactive, h} from 'vue'
import {Modal, Tag} from "ant-design-vue";

const props = defineProps({
  tableData: {
    type: Array,
    default: ()=>([]),
  },
  columns: {
    type: Array,
    default: ()=>([]),
  }
});


const emits = defineEmits(['getTableData']);

const tableLoading = ref(false);
const total = ref(0);

const ajaxQuery = () => {
  emits('getTableData');
};

function confirmDelete(enterpriseId) {
  Modal.confirm({
    title: '确定要删除吗？',
    content: '删除后，该信息将不可恢复',
    okText: '删除',
    okType: 'danger',
    onOk() {
      console.log('OK');
    },
    cancelText: '取消',
    onCancel() {},
  });
}
</script>