import {getRequest, postRequest} from "/@/lib/axios.js";

export const CaseApi = {
    pageQuery: (param) => {
        return getRequest('/case/page', param);
    },
    tree(){
        return getRequest('/case/tree');
    },
    getById: (id) => {
        return getRequest('/case/' + id);
    },
    update: (data) => {
        return postRequest(`/case/update`, data);
    },
    get(params){
        return getRequest('/case/get', params);
    },
    getList(data){
        return postRequest('/case/get-list', data);
    },
    getByGroupId(groupId) {
        return getRequest('/case/get/parent', {parentId: groupId});
    }

}