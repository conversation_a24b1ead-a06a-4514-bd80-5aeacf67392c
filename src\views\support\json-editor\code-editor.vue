<template>
  <div>
    <monaco-editor
      :value="props.value"
      language="json"
      :options="options"
      height="100%"
    />
  </div>
</template>

<script setup>
import MonacoEditor from 'monaco-editor-vue3';
import {defineProps} from 'vue'

const props = defineProps({
  value:{
    type: String,
    default: ''
  }
})

const options = {
    automaticLayout: true,
    scrollBeyondLastLine: false,
    minimap: { enabled: true },  // 启用代码缩略图
    fontSize: 14,
    tabSize: 2,
    wordWrap: 'on',
    lineNumbers: 'on',
    folding: true,  // 启用代码折叠
    foldingStrategy: 'auto',  // 自动折叠策略
    formatOnPaste: true,  // 粘贴时自动格式化
    formatOnType: true,   // 输入时自动格式化
    autoIndent: 'full',   // 自动缩进
    // 启用内置的命令
    contextmenu: true,    // 启用右键菜单
    // 智能提示配置
    quickSuggestions: true,
    suggestOnTriggerCharacters: true,
    // 其他高级功能
    parameterHints: { enabled: true },
    snippets: 'inline',
    // 括号匹配
    matchBrackets: 'always',
    autoClosingBrackets: 'always',
    autoClosingQuotes: 'always'
};
</script>

<style scoped>
.editor-container {
  height: 100%; /* 设置明确的高度 */
  width: 100%;
}
</style>