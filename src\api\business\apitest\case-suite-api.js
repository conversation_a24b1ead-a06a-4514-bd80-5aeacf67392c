import {getRequest, postRequest} from "/@/lib/axios.js";

export const CaseSuiteApi = {
    pageQuery: (param) => {
        return getRequest('/api_test/case_suite/page', param);
    },
    add(data){
        return postRequest('/api_test/case_suite/add', data);
    },
    update(data){
        return postRequest('/api_test/case_suite/update', data);
    },
    delete(ids){
        return getRequest('/api_test/case_suite/delete/',{caseSuiteIds:ids});
    },
    get(suiteId){
        return getRequest('/api_test/case_suite/get', {suiteId:suiteId});
    }
}